stages:
  - deploy

deploy_dev:
  stage: deploy
  image: node:20
  script:
    - echo "Deploying Next.js 15 app to Vercel (Development)..."
    - npm install --global vercel
    # Remove "--prod" to create a preview-like (development) deployment
    - vercel deploy --token "$VERCEL_ACCESS_TOKEN" --scope "$VERCEL_TEAM_ID"
  only:
    - develop
  environment:
    name: development
    url: "https://kyc-marketplace-dev.vercel.app/"  # optional, for GitLab environment dashboard

deploy_prod:
  stage: deploy
  image: node:20
  script:
    - echo "Deploying Next.js 15 app to Vercel (Production)..."
    - npm install --global vercel
    # Use --prod to map the deployment to your Production domain on Vercel
    - vercel deploy --prod --token "$VERCEL_ACCESS_TOKEN" --scope "$VERCEL_TEAM_ID"
  only:
    - main
  environment:
    name: production
    url: "https://kyc-marketplace.vercel.app/"  # optional, for GitLab environment dashboard
