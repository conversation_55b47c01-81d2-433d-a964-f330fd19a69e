import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import React from "react";

import { SessionProvider } from "./auth/auth-provider";
import { NotificationProvider } from "./notification/notification-provider";

interface ProvidersProps {
  children: React.ReactNode;
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 0,
    },
  },
});

const Providers = ({ children }: ProvidersProps) => {
  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider>
        <NotificationProvider>{children}</NotificationProvider>
      </SessionProvider>
    </QueryClientProvider>
  );
};

export default Providers;
