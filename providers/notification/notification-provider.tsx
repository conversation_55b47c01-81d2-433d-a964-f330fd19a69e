import {
  useCreateNotification,
  useDeleteNotification,
  useMarkAllNotificationsAsRead,
  useMarkNotificationAsRead,
  useNotifications,
} from "@/services/notification/notification-hooks";
import { Notification, NotificationContextType } from "@/types/notification";
import { useSignalRNotifications } from "@/hooks/useSignalRNotifications";
import { useCurrentUser } from "@/services/user/user-hooks";
import { useSession } from "@/providers/auth/auth-provider";
import { InterviewNotificationModal } from "@/components/modals/interview-notification-modal";
import { InterviewNotification } from "@/types/interview-notification";
import { useGetInterviewNotifications } from "@/services/notification/interview-notification-hooks";
import {
  createContext,
  useContext,
  useMemo,
  useState,
  useEffect,
  type PropsWithChildren,
  useCallback,
} from "react";

const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  markAsRead: () => {},
  markAllAsRead: () => {},
  addNotification: () => {},
  removeNotification: () => {},
  clearAllNotifications: () => {},
  isLoading: false,
});

export function NotificationProvider({ children }: PropsWithChildren) {
  const { data: apiNotifications = [], isLoading } = useNotifications();

  console.log("🔔 Notifications loaded:", apiNotifications);

  const markAsReadMutation = useMarkNotificationAsRead();
  const markAllAsReadMutation = useMarkAllNotificationsAsRead();
  const deleteNotificationMutation = useDeleteNotification();
  const createNotificationMutation = useCreateNotification();

  // SignalR notifications
  const { token } = useSession();
  const { data: user } = useCurrentUser();
  const isAuthenticated = !!token;
  const userId = user?.user_id || null;

  const {
    currentInterviewNotification,
    isInterviewModalOpen,
    closeInterviewModal,
    handleAcceptInterview,
    handleRejectInterview,
  } = useSignalRNotifications(isAuthenticated ? userId : null);

  // Polling notifications - only enable when user is authenticated or when explicitly needed for non-authenticated users
  const { mostRecentNotification: polledInterviewNotification } =
    useGetInterviewNotifications(
      userId,
      isAuthenticated || !!userId // Enable polling when authenticated OR when userId exists (for persistent notifications)
    );

  // State for polling interview modal (for all users with persistent notifications)
  const [showPolledInterviewModal, setShowPolledInterviewModal] =
    useState(false);
  const [
    polledInterviewNotificationForModal,
    setPolledInterviewNotificationForModal,
  ] = useState<InterviewNotification | null>(null);

  // Check for pending interview notifications from polling (only when authenticated or userId exists)
  useEffect(() => {
    // Only process notifications if we have authentication or valid userId
    if (
      user &&
      polledInterviewNotification &&
      userId &&
      (isAuthenticated || userId)
    ) {
      // Check if interview is not completed
      const interviewNotCompleted = !user.userInfo?.interviewCompletedDate;

      // For authenticated users, only show if no SignalR modal is already open
      // For non-authenticated users, always show if conditions are met
      const shouldShowModal = isAuthenticated
        ? interviewNotCompleted &&
          !showPolledInterviewModal &&
          !isInterviewModalOpen
        : interviewNotCompleted && !showPolledInterviewModal;

      if (shouldShowModal) {
        // Convert API notification format to the format expected by the modal
        const modalNotification: InterviewNotification = {
          id: polledInterviewNotification.id,
          message:
            polledInterviewNotification.message ||
            "You have been invited for an interview verification.",
          title: "Interview Request",
          type: "interview",
          timestamp: new Date(polledInterviewNotification.createdAt),
        };

        setPolledInterviewNotificationForModal(modalNotification);
        setShowPolledInterviewModal(true);
        console.log(
          `📋 Showing persistent interview modal - Auth: ${isAuthenticated}, User: ${userId}`
        );
      } else if (!interviewNotCompleted) {
        console.log("🚫 Interview already completed, ignoring notification");
      }
    }
  }, [
    user,
    polledInterviewNotification,
    userId,
    isAuthenticated,
    showPolledInterviewModal,
    isInterviewModalOpen,
  ]);

  // Use only API notifications (interview notifications are handled separately via modals)
  const allNotifications = useMemo(() => {
    return apiNotifications.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }, [apiNotifications]);

  const unreadCount = useMemo(() => {
    return allNotifications.filter((notification) => !notification.read).length;
  }, [allNotifications]);

  const markAsRead = (id: string) => {
    markAsReadMutation.mutate(id);
  };

  const markAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };

  const addNotification = (notification: Omit<Notification, "id">) => {
    createNotificationMutation.mutate(notification);
  };

  const removeNotification = (id: string) => {
    deleteNotificationMutation.mutate(id);
  };

  const clearAllNotifications = () => {
    // Delete all API notifications
    apiNotifications.forEach((notification) => {
      deleteNotificationMutation.mutate(notification.id);
    });
  };

  // Handlers for polling interview modal
  const closePolledInterviewModal = useCallback(() => {
    setShowPolledInterviewModal(false);
    setPolledInterviewNotificationForModal(null);
  }, []);

  const handleAcceptPolledInterview = useCallback(async () => {
    console.log("✅ Polled interview accepted");
    closePolledInterviewModal();
  }, [closePolledInterviewModal]);

  const handleRejectPolledInterview = useCallback(async () => {
    console.log("❌ Polled interview rejected");
    closePolledInterviewModal();
  }, [closePolledInterviewModal]);

  const value: NotificationContextType = {
    notifications: allNotifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    addNotification,
    removeNotification,
    clearAllNotifications,
    isLoading:
      isLoading ||
      markAsReadMutation.isPending ||
      markAllAsReadMutation.isPending ||
      deleteNotificationMutation.isPending ||
      createNotificationMutation.isPending,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}

      {/* SignalR Interview Notification Modal */}
      <InterviewNotificationModal
        isOpen={isInterviewModalOpen}
        notification={currentInterviewNotification}
        onClose={closeInterviewModal}
        onAccept={handleAcceptInterview}
        onReject={handleRejectInterview}
      />

      {/* Persistent Interview Notification Modal (for all users) */}
      <InterviewNotificationModal
        isOpen={showPolledInterviewModal}
        notification={polledInterviewNotificationForModal}
        onClose={closePolledInterviewModal}
        onAccept={handleAcceptPolledInterview}
        onReject={handleRejectPolledInterview}
      />
    </NotificationContext.Provider>
  );
}

/**
 * Hook to use notification context
 */
export function useNotificationContext() {
  const context = useContext(NotificationContext);

  if (process.env.NODE_ENV !== "production") {
    if (!context) {
      throw new Error(
        "useNotificationContext must be used within a NotificationProvider"
      );
    }
  }

  return context;
}
