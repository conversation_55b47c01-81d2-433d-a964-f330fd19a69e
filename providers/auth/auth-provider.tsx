import { AUTH_TOKEN_KEY, onTokenRefresh, onLogout } from "@/constants/api";
import { login, logout, register } from "@/services/auth/auth-service";
import { LoginInputs, RegistrationInputs } from "@/types/auth";
import { router, usePathname } from "expo-router";
import {
  createContext,
  type PropsWithChildren,
  useContext,
  useEffect,
  useState,
} from "react";
import { useStorageState } from "./useStorageState";
import { useQueryClient } from "@tanstack/react-query";

const AUTH_PAGES = [
  "/sign-in",
  "/registration-type",
  "/registration-kyc",
  "/registration-kyb",
];
const AuthContext = createContext<{
  signIn: (data: LoginInputs) => Promise<void>;
  signUp: (data: RegistrationInputs) => Promise<void>;
  signOut: () => void;
  clearJustSignedUp: () => void;
  clearError: () => void;
  token?: string | null;
  isLoading: boolean;
  error: string | null;
}>({
  signIn: async () => {},
  signUp: async () => {},
  signOut: () => null,
  clearJustSignedUp: () => {},
  clearError: () => {},
  token: null,
  isLoading: false,
  error: null,
});

// This hook can be used to access the user info.
export function useSession() {
  const value = useContext(AuthContext);
  if (process.env.NODE_ENV !== "production") {
    if (!value) {
      throw new Error("useSession must be wrapped in a <SessionProvider />");
    }
  }

  return value;
}

export function SessionProvider({ children }: PropsWithChildren) {
  const [[isTokenLoading, token], setToken] = useStorageState(AUTH_TOKEN_KEY);
  const queryClient = useQueryClient();

  console.log("🔑 Auth token:", token);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [justSignedUp, setJustSignedUp] = useState(false);

  // check if user is logged in, and if current url is sign-in, registration-type, registration-kyc, registration-kyb
  const pathname = usePathname();
  const isOnAuthPage = AUTH_PAGES.includes(pathname);

  // Listen for token refresh events from API calls
  useEffect(() => {
    const unsubscribe = onTokenRefresh((newToken: string) => {
      console.log("🔄 Token refreshed in API, updating auth state");
      setToken(newToken);
    });

    return unsubscribe;
  }, [setToken]);

  // Listen for logout events from API calls
  useEffect(() => {
    const unsubscribe = onLogout(() => {
      console.log(
        "🚪 Logout event received, clearing auth state and redirecting"
      );

      // Clear React Query cache to prevent stale data usage
      queryClient.clear();
      console.log("🧹 React Query cache cleared");

      setToken(null);
      setError(null);
      setJustSignedUp(false);

      // Navigate to sign-in page
      router.replace("/sign-in");
    });

    return unsubscribe;
  }, [setToken, queryClient]);

  // Clear error when navigating between auth pages
  useEffect(() => {
    if (isOnAuthPage && error) {
      console.log("🧹 Clearing error on auth page navigation:", pathname);
      setError(null);
    }
  }, [pathname, isOnAuthPage]);

  // Navigation logic
  useEffect(() => {
    if (isLoading) {
      return;
    }

    if (token && pathname === "/welcome" && !justSignedUp) {
      return router.replace("/");
    }

    if (token && pathname === "/welcome" && justSignedUp) {
      console.log("isLoggedIn && pathname === /welcome - staying on welcome");
      return;
    }

    if (token && isOnAuthPage && !justSignedUp) {
      console.log("isLoggedIn && isOnAuthPage - redirecting to home");
      return router.replace("/");
    }
  }, [token, isLoading, justSignedUp, pathname]);

  return (
    <AuthContext.Provider
      value={{
        signIn: async (data: LoginInputs) => {
          try {
            setIsLoading(true);
            setError(null); // Clear previous errors
            const response = await login(data);
            const token = response.token;

            setToken(token || null);
          } catch (error: any) {
            setError(error?.message || "Invalid email or password");
          } finally {
            setIsLoading(false);
          }
        },
        signUp: async (data: RegistrationInputs) => {
          await register(data)
            .then(async () => {
              const response = await login(data);
              const token = response.token;

              setToken(token || null);
              setJustSignedUp(true);
              router.replace("/welcome");
            })
            .catch((error: any) => {
              setError(error?.message || "Registration failed");
            })
            .finally(() => {
              setIsLoading(false);
            });
        },
        signOut: async () => {
          console.log("🚪 Manual logout initiated");

          // Clear React Query cache to prevent stale data usage
          queryClient.clear();
          console.log("🧹 React Query cache cleared on manual logout");

          await logout();
          setToken(null);
        },
        clearJustSignedUp: () => {
          setJustSignedUp(false);
        },
        clearError: () => {
          setError(null);
        },
        token,
        isLoading: isTokenLoading || isLoading,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
