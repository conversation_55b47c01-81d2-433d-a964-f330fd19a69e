import { getStorageItemAsync, setStorageItemAsync } from "@/utils/storage";
import { useCallback, useEffect, useReducer } from "react";
import { Platform } from "react-native";
import { AUTH_TOKEN_KEY } from "@/constants/api-constants";

type UseStateHook<T> = [[boolean, T | null], (value: T | null) => void];

function useAsyncState<T>(
  initialValue: [boolean, T | null] = [true, null]
): UseStateHook<T> {
  return useReducer(
    (
      _state: [boolean, T | null],
      action: T | null = null
    ): [boolean, T | null] => [false, action],
    initialValue
  ) as UseStateHook<T>;
}

export function useStorageState(key: string): UseStateHook<string> {
  // Public
  const [state, setState] = useAsyncState<string>();

  // Get
  useEffect(() => {
    if (Platform.OS === "web") {
      try {
        if (typeof localStorage !== "undefined") {
          setState(localStorage.getItem(key));
        }
      } catch (e) {
        console.error("Local storage is unavailable:", e);
      }
    } else {
      getStorageItemAsync(key).then((value) => {
        setState(value);
      });
    }
  }, [key]);

  // Listen for storage changes from external sources (like force logout)
  useEffect(() => {
    let lastValue: string | null = null;

    const checkStorageChanges = async () => {
      const currentValue = await getStorageItemAsync(key);
      // Only update if value actually changed
      if (currentValue !== lastValue) {
        lastValue = currentValue;
        setState(currentValue);
      }
    };

    // Check for changes periodically when key is AUTH_TOKEN_KEY
    if (key === AUTH_TOKEN_KEY) {
      const interval = setInterval(checkStorageChanges, 500); // Check every 500ms
      return () => clearInterval(interval);
    }
  }, [key]);

  // Set
  const setValue = useCallback(
    (value: string | null) => {
      setState(value);
      setStorageItemAsync(key, value);
    },
    [key]
  );

  return [state, setValue];
}
