import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { PUBLIC_LINKS } from "@/data/content";
import { maybeRefreshToken, getSession } from "./react-query/query/auth";

const PUBLIC_ROUTES = [
  "/login",
  "/register",
  "/forgot-password",
  "/reset-password",
  "/mobile-kyb",
  "/error",
  "/debug/notification-test",
  // Note: "/marketplace/*" is handled separately below
  ...PUBLIC_LINKS.map((link) => link.href),
]; // Public routes that do not require authentication
const WELCOME_ROUTES = ["/welcome"]; // Welcome flow routes

export async function middleware(req: NextRequest) {
  // First, check for the encrypted session
  const sessionCookie = req.cookies.get("session");
  const hasSession = Boolean(sessionCookie?.value);

  // Also check legacy cookies for backward compatibility
  const email = req.cookies.get("email");
  const userId = req.cookies.get("user_id");
  const token = req.cookies.get("token");
  const accessLevel = req.cookies.get("state");

  // Determine if the user is authenticated
  const isLegacyAuthenticated = Boolean(token && email && userId);
  const requestedPath = req.nextUrl.pathname; // Get the requested path
  if (requestedPath.startsWith("/debug/")) {
    return NextResponse.next();
  }
  // Try to get session data from encrypted cookie
  let isSessionAuthenticated = false;
  let shouldRedirectToLogin = false;

  if (hasSession || token) {
    // Try to refresh token if needed
    try {
      const refreshResult = await maybeRefreshToken(true); // true indicates call from middleware

      // Check if user should be logged out
      if (refreshResult?.shouldLogout) {
        shouldRedirectToLogin = true;
      } else {
        // Get current session (which may have been refreshed)
        const session = await getSession();

        // Check if we have a valid session
        isSessionAuthenticated = Boolean(session && !session.isExpired);

        // If not authenticated via session but we have a token cookie,
        // consider the user authenticated through the legacy method
        if (!isSessionAuthenticated && token) {
          isSessionAuthenticated = isLegacyAuthenticated;
        }
      }
    } catch (error) {
      isSessionAuthenticated = isLegacyAuthenticated;
    }
  }

  // If token refresh indicated logout, redirect to login page
  if (shouldRedirectToLogin && !PUBLIC_ROUTES.includes(requestedPath)) {
    const response = NextResponse.redirect(new URL("/", req.url));

    // Clear all auth cookies
    response.cookies.delete("session");
    response.cookies.delete("user_id");
    response.cookies.delete("email");
    response.cookies.delete("state");
    response.cookies.delete("registrationtype");
    response.cookies.delete("applicant_id");
    response.cookies.delete("token");

    return response;
  }

  // Consider the user authenticated if either method works
  const isAuthenticated = isSessionAuthenticated || isLegacyAuthenticated;

  // Check if the requested path is a public route
  const isPublicRoute =
    PUBLIC_ROUTES.includes(requestedPath) ||
    requestedPath === "/marketplace" ||
    requestedPath.startsWith("/marketplace/");

  // Check if path starts with /welcome
  const isWelcomePage = WELCOME_ROUTES.some((route) =>
    requestedPath.startsWith(route)
  );

  // Welcome flow handling - allow if in tier0 state or has token
  if (isWelcomePage) {
    if (accessLevel?.value === "tier0" || isAuthenticated) {
      return NextResponse.next();
    } else {
      return NextResponse.redirect(new URL("/", req.url));
    }
  }

  // Allow authenticated users to access public routes and redirect them to `/categories`
  // But don't redirect if they have just registered (tier0 state) or accessing marketplace
  if (isAuthenticated && isPublicRoute) {
    // Allow access to marketplace routes for authenticated users
    if (
      requestedPath === "/marketplace" ||
      requestedPath.startsWith("/marketplace/")
    ) {
      return NextResponse.next();
    }
    if (accessLevel?.value === "tier0") {
      // Allow newly registered users to stay on other public routes
      return NextResponse.next();
    }
    return NextResponse.redirect(new URL("/categories", req.url)); // Redirect to categories page for other public routes
  }

  // Block unauthenticated users from accessing private routes and redirect to the home page
  if (!isAuthenticated && !isPublicRoute && !isWelcomePage) {
    return NextResponse.redirect(new URL("/", req.url)); // Redirect to the home page
  }

  // Allow requests to proceed for authenticated users on private routes or unauthenticated on public/welcome routes
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|favicon.ico|api/logout|api/mobile-kyb).*)"], // Exclude `/api/logout` and `/api/mobile-kyb` from middleware
};
