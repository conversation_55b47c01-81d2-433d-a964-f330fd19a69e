import { ArrowRight } from "lucide-react";
import Link from "next/link";

export function KycPrompt() {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-gradient-to-t from-[#F5F6F6] via-[#F5F6F6]/90 to-transparent p-4">
      <div className="mx-auto flex max-w-[1440px] items-center justify-between rounded-2xl bg-white p-4 shadow-sm">
        <p className="text-sm text-[#1C3F3C]">Unlock platforms & services</p>
        <button className="rounded-full bg-[#16A34A] px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-[#15803D]">
          <Link href="/kyc" className="flex items-center gap-1.5">
            START KYC
            <ArrowRight className="h-4 w-4" />
          </Link>
        </button>
      </div>
    </div>
  );
}
