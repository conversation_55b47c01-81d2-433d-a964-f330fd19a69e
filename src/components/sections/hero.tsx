import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowUpRight } from "lucide-react";
import { cn } from "@/utils/tailwind";
import Image from "next/image";
import image from "../../../public/background/landing.png";

export function Hero() {
  return (
    <section className="relative lg:min-h-[calc(70vh-64px)] flex items-center justify-center container overflow-visible">
      {/* Right-side background image, scrolls naturally with hero section */}
      <div
        className="absolute top-0 h-full pointer-events-none select-none z-0"
        style={{
          width: '60vw',
          right: 'calc(-50vw + 50%)'
        }}
      >
        <div className="relative w-full h-full min-w-[600px] max-w-[1200px] ml-auto">
          <Image
            src={image}
            alt="Landing background"
            fill
            style={{
              objectFit: "contain",
              objectPosition: "right center"
            }}
            priority
            className="drop-shadow-sm"
          />
        </div>
      </div>
      <div className="grid md:grid-cols-1 gap-12 items-center w-full relative z-10">
        <div className="flex flex-col gap-4 md:gap-1 w-full">
          <h2 className="text-[28px] md:text-[42px] font-medium text-[#1A3B36]">
            Federated Community of
          </h2>

          <h1 className="text-[38px]  md:text-[101px] font-medium text-[#1A3B36] leading-[1.1]">
            Trusted
          </h1>
          <h1 className="text-[38px]  md:text-[101px] font-medium text-[#1A3B36] leading-[1.1]">
            Onboarding
          </h1>

          <div className="w-full flex flex-col gap-4 md:gap-6 mt-10">
            {/* <p className="text-[18px] md:text-base text-black mb-6 leading-relaxed max-w-[581px]">
              Introducing trustnexus: Revolutionizing the KYC landscape with our
              cutting-edge digital platform, designed to facilitate secure,
              efficient, and compliant digital interactions across the globe.
            </p> */}

            <div className="flex flex-col md:flex-row gap-4 md:gap-6">
              <Link href="/register" className="w-full md:w-auto">
                <Button
                  className={cn(
                    "flex items-center justify-center gap-2 font-medium text-base w-full min-w-[250px]",
                    "bg-[#1A3B36] hover:bg-[#152F2B] text-white h-12 md:h-16 px-6",
                  )}
                >
                  CREATE ACCOUNT
                  <ArrowUpRight className="h-5 w-5" />
                </Button>
              </Link>

              <Link href="/contact" className="w-full md:w-auto">
                <Button
                  variant="outline"
                  className={cn(
                    "font-medium text-base bg-white text-[#1A3B36] border-[#E5E7EB] rounded-full min-w-[250px]",
                    "hover:bg-gray-50 h-12 md:h-16 w-full md:w-auto px-6 shadow-md",
                  )}
                >
                  BOOK A DEMO
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
