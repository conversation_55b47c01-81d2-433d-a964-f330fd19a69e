import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowUpRight } from "lucide-react";

interface CTAProps {
  text?: string;
}
export function CTA({
  text = "Onboarded Once, Verified Everywhere",
}: CTAProps) {
  return (
    <section className=" pb-8 sm:pb-24 container">
      <div className="flex flex-col items-center justify-center gap-8 sm:gap-12 w-full">
        <h2 className="text-center font-poppins font-normal text-[36px] leading-[40px] sm:text-[45px] sm:leading-[60px] lg:text-[67px] lg:leading-[74px] tracking-[-1px] sm:tracking-[-2px] lg:tracking-[-3px] text-[#1C3F3C] px-4">
          {text}
        </h2>
        <Link href="/register" className="w-full">
          <Button
            className="w-full lg:max-w-[300px] font-semibold flex items-center justify-center mx-auto"
            variant="default"
            size="lg"
          >
            CREATE ACCOUNT
            <ArrowUpRight className="w-5 h-5 sm:w-6 sm:h-6 ml-2 text-border" />
          </Button>
        </Link>
      </div>
    </section>
  );
}
