import Image from "next/image";
import partner1 from "../../../public/companies/law-firm/dspartners.png";
import partner2 from "../../../public/companies/law-firm/law-firm-1.jpg";
import partner3 from "../../../public/companies/law-firm/llpo-law-firm.png";
import partner4 from "../../../public/companies/utilities/deh.png";
import partner5 from "../../../public/companies/utilities/ahk.jpg";
import partner6 from "../../../public/companies/sports/apoel.png";
import partner7 from "../../../public/companies/events/soldouttickets.png";

const partners = [
  { name: "Partner 1", logo: partner1 },
  { name: "Partner 2", logo: partner2 },
  { name: "Partner 3", logo: partner3 },
  { name: "Partner 4", logo: partner4 },
  { name: "Partner 5", logo: partner5 },
  { name: "Partner 6", logo: partner6 },
  { name: "Partner 7", logo: partner7 },
];

export function TrustedBy() {
  return (
    <section className="container py-16">
      <div className="flex flex-col items-center lg:flex-row gap-6 lg:gap-20 w-full">
        <p className="text-primary text-xl underline tracking-wide">
          Trusted by:
        </p>
        <div className="grid grid-cols-2 gap-8 md:grid-cols-6 lg:grid-cols-7">
          {partners.map((partner) => (
            <div
              key={partner.name}
              className="col-span-1 flex lg:justify-center md:col-span-2 lg:col-span-1"
            >
              <Image
                src={partner.logo || "/placeholder.svg"}
                alt={partner.name}
                width={120}
                height={50}
                className="object-contain"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
