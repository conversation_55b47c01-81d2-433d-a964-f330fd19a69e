"use client";

import { useState } from "react";
import { cn } from "@/utils/tailwind";
import Image from "next/image";
import largeLogo from "../../../public/large-logo.png";

const features = [
  {
    number: "01",
    title: "Launching the Portal",
    description:
      "Introducing Trustnexus, a portal that simplifies access to services in ways that extend beyond simple onboarding and KYC. Operating via a federated community network of peer-verification, Trustnexus creates a marketplace of services in a way that is easy, fast and secure for everyone involved. This novel approach opens the gateways to the future of KYC and e-identities, redefining onboarding with federated assurance and interoperability.",
  },
  {
    number: "02",
    title: "Redefining Onboarding with Legalized Assurance",
    description: "Welcome to the future of KYC and digital identity—meet Trustnexus, the portal that transforms the way you access services. Gone are the days of tedious onboarding, repetitive KYC checks, and fragmented identity verification. Trustnexus redefines the game with a federated community network that empowers businesses and individuals alike.",
  },
  {
    number: "03",
    title: "User Empowerment, Infinite Opportunities",
    description:
      "With Trustnexus, you create your own e-ID, manage your data, and decide who gets access — no more, no less. It's not just a tool; it's a movement toward user empowerment, federated assurance, and interoperability. Trustnexus shifts control of data, to data owners, who decide to share uploaded information with specific providers in order to gain access to their services. At any point in time, users know what they have shared and with whom, and are free to revoke that consent.",
  },
];

export function Features() {
  const [openedFeature, setOpenedFeature] = useState<string>("01");

  return (
    <section className="py-10 lg:py-24 relative container">
      <div
        className="py-4 px-6 lg:py-10 lg:px-9 relative backdrop-blur-sm rounded-[40px]"
        style={{
          background:
            "linear-gradient(180deg, rgba(227, 227, 227, 0.5) 0.01%, rgba(246, 245, 250, 0) 100%)",
        }}
      >
        <div className="grid lg:grid-cols-2 gap-10 lg:gap-24">
          <div className="flex flex-col items-center gap-8 pt-8 lg:pt-12">
            {/* Logo */}
            <div className="w-[300px] h-[300px] lg:w-[400px] lg:h-[400px] relative flex-shrink-0">
              <Image 
                src={largeLogo}
                alt="trustnexus Logo" 
                fill
                className="object-contain"
              />
            </div>
            
          </div>
          <div className="flex flex-col gap-4 lg:gap-12">
            {features.map((feature, index) => (
              <div
                key={feature.title}
                className="relative border-b border-b-input pb-4 lg:pb-12"
              >
                <div className="flex gap-1 lg:gap-8 items-start">
                  <span
                    className={cn(
                      "text-2xl lg:text-[32px] lg:leading-[38px] text-black",
                      {
                        "font-medium":
                          openedFeature === feature.number,
                      },
                    )}
                  >
                    {feature.number}.
                  </span>
                  <div className="space-y-4 w-full">
                    <h3
                      className={cn(
                        "text-2xl lg:text-[32px] lg:leading-[38px] text-black cursor-pointer",
                        { "font-medium": openedFeature === feature.number },
                      )}
                      onClick={() => setOpenedFeature(feature.number)}
                    >
                      {feature.title}
                    </h3>

                    {openedFeature === feature.number && (
                      <>
                        <p className="text-input pr-8 leading-[21px] font-normal">
                          {feature.description}
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
