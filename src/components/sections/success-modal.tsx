import type React from "react";
import { X } from "lucide-react";
import { Card } from "@/components/common/card";

interface SuccessModalProps {
  isOpen: boolean;
  companyName: string | undefined;
  onClose: () => void;
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  companyName,
  onClose,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      <div className="relative bg-white w-[28.25rem] rounded-[1.25rem] p-6 border border-[#C8E2CE] shadow-lg">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute left-6 top-6 rounded-full p-1 bg-gray-100 hover:bg-gray-200 focus:ring-2 focus:ring-primary focus:outline-none"
        >
          <X className="w-5 h-5 text-black" />
        </button>

        {/* Main Content */}
        <Card className="p-5 rounded-[20px] bg-white">
          <div className="flex flex-col items-center space-y-6 text-center">
            {/* Success Icon and Header */}
            <div className="bg-[#D1FAE5] px-3 py-1.5 rounded-full flex items-center gap-1.5 text-sm text-green-600">
              <span className="font-medium">Success!</span>
            </div>

            {/* Success Message */}
            <h2 className="text-lg font-medium text-black">
              You have successfully applied to Onboard with {companyName}, they
              will communicate the next steps.
            </h2>

            {/* Confirm Button */}
            <button
              onClick={onClose}
              className="w-[15.5rem] h-[3.5rem] bg-primary rounded-[2.5rem] font-poppins font-semibold text-base text-white hover:bg-primary/90 focus:ring-2 focus:ring-offset-2 focus:ring-primary focus:outline-none"
            >
              BACK
            </button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default SuccessModal;
