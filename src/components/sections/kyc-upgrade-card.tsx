import React from "react";
import type { SUMSUB_LEVEL } from "@/types/user";
import { X, Dot, Lock, Info } from "lucide-react";
import Image from "next/image";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { TIER_NAMES } from "@/components/company/tier";
import { getNextTier } from "@/utils/utils";

interface KycUpgradeCardProps {
  companyName: string;
  companyLogo?: string | null;
  tierNumber: SUMSUB_LEVEL;
  onBack: () => void;
  onApplyKyc: () => void;
}

type RegistrationType = "individual" | "company";

interface TierDataStructure {
  individual: Record<string, string[]>;
  company: Record<string, string[]>;
}

// Helper function to extract tier number from tier string
const getTierNumber = (tierString: string): number => {
  if (!tierString) return 0;
  const match = tierString.match(/(\d+)$/);
  return match ? parseInt(match[1], 10) : 0;
};

// Helper function to get tier name
const getTierName = (tierNumber: number): string => {
  const tierNames: Record<number, string> = {
    0: "Entry",
    1: "Basic",
    2: "Standard",
    3: "Pro",
  };
  return tierNames[tierNumber] || `Tier ${tierNumber}`;
};

// Helper function to determine if user needs multi-step upgrade to reach target
const needsMultiStepUpgrade = (
  currentTier: string,
  targetTier: string
): boolean => {
  const currentTierNum = getTierNumber(currentTier);
  const targetTierNum = getTierNumber(targetTier);
  return targetTierNum - currentTierNum > 1;
};

// Helper function to get all required steps from current tier to target tier
const getCumulativeSteps = (
  currentTier: string,
  targetTier: number,
  registrationType: RegistrationType,
  tierData: TierDataStructure
): { tierNumber: number; tierName: string; steps: string[] }[] => {
  const currentTierNum = getTierNumber(currentTier);
  const targetTierNum = targetTier;

  const cumulativeSteps: {
    tierNumber: number;
    tierName: string;
    steps: string[];
  }[] = [];

  // Get steps for each tier from current+1 to target
  const startTier = Math.max(1, currentTierNum + 1);

  console.log(
    "Debug getCumulativeSteps - Start Tier:",
    startTier,
    "Target Tier:",
    targetTierNum,
    "Current Tier Num:",
    currentTierNum
  );

  for (let tier = startTier; tier <= targetTierNum; tier++) {
    const tierKey = `tier${tier}`;
    const steps = tierData[registrationType][tierKey] || [];

    console.log(
      `Debug getCumulativeSteps - Processing tier ${tier} (${getTierName(tier)}), steps:`,
      steps
    );

    if (steps.length > 0) {
      cumulativeSteps.push({
        tierNumber: tier,
        tierName: getTierName(tier),
        steps: steps,
      });
    }
  }

  return cumulativeSteps;
};

const KycUpgradeCard: React.FC<KycUpgradeCardProps> = ({
  companyName,
  companyLogo,
  tierNumber,
  onBack,
  onApplyKyc,
}) => {
  const { data: userProfile } = useGetUserProfile();

  // Get tier name from tier number
  const tierName = TIER_NAMES[Number(tierNumber)] || `Tier ${tierNumber}`;

  // Get current user tier and next tier information
  const currentUserTier = userProfile?.state || "";
  const nextTier = getNextTier(currentUserTier);
  const targetTierString = tierNumber.toString();

  // Check if this is a multi-step upgrade
  const isMultiStepUpgrade = needsMultiStepUpgrade(
    currentUserTier,
    targetTierString
  );

  // Define the tier data mappings
  const tierData: TierDataStructure = {
    individual: {
      tier1: [
        "Proof of Identity (POI)",
        "Proof of Residence (POR)",
        "Liveness Check",
        "AML Screening",
      ],
      tier2: [
        "Proof of Income/Source of Funds",
        "Employment Contract; and/or",
        "Payslip; and/or",
        "Tax Return",
      ],
      tier3: [
        "Suitability & Appropriateness Test – Questionnaire",
        "Economic Profile Questionnaire",
      ],
    },
    company: {
      tier1: [
        "Company Data Collection",
        "Company Representative Individual KYC",
        "Corporate Certificates",
        "Tax Residence and Tax Identification/VAT",
      ],
      tier2: [
        "Source of Funds",
        "Financial Statements; and/or Engagement Letter",
        "Ownership Structure",
        "Individual KYC on UBOs/Representatives",
        "AML Screening on Company and UBOs",
        "Bank Reference Letter (if applicable)",
      ],
      tier3: ["Company Profile Questionnaire"],
    },
  };

  const registrationType: RegistrationType =
    (userProfile?.registrationtype as RegistrationType) || "individual";

  // Debug: Log current tier information
  console.log("Debug - Current User Tier:", currentUserTier);
  console.log("Debug - Target Tier Number:", tierNumber);
  console.log("Debug - Current Tier Number:", getTierNumber(currentUserTier));
  console.log("Debug - Target Tier Number (parsed):", Number(tierNumber));

  // Get cumulative steps from current tier to target tier
  const cumulativeSteps = getCumulativeSteps(
    currentUserTier,
    Number(tierNumber),
    registrationType,
    tierData
  );

  // Debug: Log cumulative steps
  console.log("Debug - Cumulative Steps:", cumulativeSteps);

  // For display purposes, flatten all steps but keep tier information
  const allSteps = cumulativeSteps.flatMap((tierStep) => tierStep.steps);

  // Split all steps into two columns
  const midIndex = Math.ceil(allSteps.length / 2);
  const leftColumn = allSteps.slice(0, midIndex);
  const rightColumn = allSteps.slice(midIndex);

  return (
    <div className="relative w-full max-w-[28.25rem] mx-auto px-4">
      {/* Main Card */}
      <div className="bg-white rounded-[1.25rem] border border-[#C8E2CE] p-4 md:p-6 flex flex-col items-center">
        {/* Close button */}
        <button
          onClick={onBack}
          className="absolute left-4 md:left-6 top-4 md:top-6 rounded-full p-1 bg-gray-100 hover:bg-gray-200 focus:ring-2 focus:ring-primary focus:outline-none"
        >
          <X className="w-5 h-5 text-black" />
        </button>

        {/* Company Logo Section - Positioned like in Figma but skinnier */}
        {companyLogo && (
          <div className="flex justify-center items-center gap-3 md:gap-[1.125rem] mt-8 md:mt-[2.8125rem]">
            <div className="w-[32px] md:w-[40.87px] h-[28px] md:h-[36px] rounded-[1rem] md:rounded-[1.3125rem] overflow-hidden bg-gray-50 flex items-center justify-center">
              <img
                src={companyLogo}
                alt={`${companyName} logo`}
                className="w-full h-full object-contain"
                onError={(e) => {
                  e.currentTarget.parentElement!.style.display = "none";
                }}
              />
            </div>
            <div className="text-sm md:text-base font-poppins font-medium text-[#231F20]">
              {companyName}
            </div>
          </div>
        )}

        {/* Tier Lock Text Section - Positioned like in Figma but skinnier */}
        <div className="flex justify-center mt-2 md:mt-[2.1875rem]">
          <div className="font-poppins font-medium text-lg md:text-[1.25rem] leading-[1em] text-center tracking-[0.02em] text-primary px-4">
            "{tierName}" is locked
          </div>
        </div>

        {/* Lock Icon - Positioned like in Figma but skinnier */}
        <div className="flex justify-center mt-4 md:mt-[1.9375rem]">
          <Image
            src="/icons/locked_company.svg"
            alt="Locked Company"
            width={70}
            height={68}
            className="md:w-[88px] md:h-[87px]"
            priority
          />
        </div>

        {/* Multi-step upgrade warning */}
        {isMultiStepUpgrade && currentUserTier && nextTier && (
          <div className="w-full mt-4 md:mt-6 mb-4 p-3 md:p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <Info className="w-4 md:w-5 h-4 md:h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs md:text-sm text-blue-800">
                <p className="font-medium mb-1">Step-by-step upgrade process</p>
                <p>
                  You are currently at{" "}
                  <strong>{getTierName(getTierNumber(currentUserTier))}</strong>{" "}
                  tier. This upgrade will first take you to{" "}
                  <strong>{getTierName(getTierNumber(nextTier))}</strong> tier.
                  To access <strong>{companyName}</strong> requiring{" "}
                  <strong>{tierName}</strong> tier, you will need to complete
                  additional upgrade steps after this one.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Description Section */}
        <div className="bg-[#EFF7ED] rounded-lg p-3 md:p-4 mt-4 md:mt-6 w-full text-sm md:text-base text-[#1C3F3C] font-poppins">
          In order to onboard with <strong>{companyName}</strong>, the required
          level of verification is <strong>"{tierName}"</strong>.
          {isMultiStepUpgrade ? (
            <>
              {" "}
              To reach <strong>"{tierName}"</strong> from your current tier, you
              will need to complete all the following steps across multiple
              tiers:
            </>
          ) : (
            <>
              {" "}
              To verify your account to <strong>"{tierName}"</strong>, you will
              need to complete the following steps:
            </>
          )}
        </div>

        {/* Show tier breakdown for multi-step upgrades */}
        {isMultiStepUpgrade && cumulativeSteps.length > 1 && (
          <div className="w-full mt-4 mb-4 p-3 md:p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="text-sm md:text-base font-poppins font-medium text-[#1C3F3C] mb-3">
              Tier Progression:
            </div>
            <div className="flex flex-wrap gap-2">
              {cumulativeSteps.map((tierStep, index) => (
                <div key={tierStep.tierNumber} className="flex items-center">
                  <span className="px-2 py-1 bg-primary/10 text-primary rounded-md text-xs md:text-sm font-medium">
                    {tierStep.tierName}
                  </span>
                  {index < cumulativeSteps.length - 1 && (
                    <span className="mx-2 text-gray-400">→</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Two Columns - All Required Steps */}
        <div className="w-full flex flex-col md:flex-row md:flex-wrap justify-between gap-x-4 md:gap-x-8 gap-y-2 md:gap-y-4 mb-4 md:mb-6 mt-4 md:mt-6">
          {/* Left Column */}
          <div className="flex flex-col flex-1 gap-1 md:gap-2 text-[#444] font-poppins text-sm md:text-base leading-6 md:leading-7">
            {leftColumn.map((item, index) => (
              <div key={index} className="flex items-start gap-2">
                <Dot className="text-primary w-3 h-3 shrink-0 mt-1" />
                <span className="break-words">{item}</span>
              </div>
            ))}
          </div>
          {/* Right Column */}
          <div className="flex flex-col flex-1 gap-1 md:gap-2 text-[#444] font-poppins text-sm md:text-base leading-6 md:leading-7">
            {rightColumn.map((item, index) => (
              <div key={index} className="flex items-start gap-2">
                <Dot className="text-primary w-3 h-3 shrink-0 mt-1" />
                <span className="break-words">{item}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Consent Text for Verification - Only show for initial upgrade */}
        {Number(tierNumber) === 1 && (
          <div className="bg-[#F9F9F9] rounded-lg p-3 md:p-4 mb-4 md:mb-6 w-full text-xs md:text-sm text-[#444] font-poppins">
            You are about to submit personal data to Trustnexus. If you received
            this link from a suspicious source, please close this page and
            notify us immediately.
            <br />
            <br />
            By proceeding, you agree to the Terms & Conditions of Trustnexus
            Limited.
          </div>
        )}

        {/* Confirm Button */}
        <button
          onClick={onApplyKyc}
          className="w-full max-w-[15.5rem] h-[3rem] md:h-[3.5rem] bg-primary rounded-[2rem] md:rounded-[2.5rem] font-poppins font-semibold text-sm md:text-base text-white hover:bg-primary/90 focus:ring-2 focus:ring-offset-2 focus:ring-primary focus:outline-none"
        >
          {isMultiStepUpgrade
            ? `UPGRADE TO ${getTierName(getTierNumber(nextTier || "")).toUpperCase()}`
            : `UPGRADE TO ${tierName.toUpperCase()}`}
        </button>

        {/* Back Button */}
        <button
          onClick={onBack}
          className="w-full max-w-[15.5rem] h-[3rem] md:h-[3.5rem] mt-2 mx-auto bg-white rounded-[2rem] md:rounded-[2.5rem] font-poppins font-medium text-sm md:text-base text-primary flex items-center justify-center focus:ring-2 focus:ring-offset-2 focus:ring-primary focus:outline-none shadow-[0px_6px_8.3px_4px_rgba(28,63,60,0.2)]"
        >
          BACK
        </button>
      </div>
    </div>
  );
};

export default KycUpgradeCard;
