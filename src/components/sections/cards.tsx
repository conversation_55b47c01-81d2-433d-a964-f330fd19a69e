import Link from "next/link";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import trust from "../../../public/icons/trust.png";
import interoperability from "../../../public/icons/interoperability.png";
import marketplace from "../../../public/icons/marketplace.png";

const cards = [
  {
    icon: trust,
    title: "Trust",
    description: "Trusted, Verified & Linked. Security through four-eye compliance.",
    button: "Learn More"
  },
  {
    icon: interoperability,
    title: "Interoperability",
    description: "Upload your KYC onto the platform once, unlock unlimited services.",
    button: "Learn More"
  },
  {
    icon: marketplace,
    title: "Marketplace",
    description: "Browse through a variety of services to seamlessly click, consent and onboard",
    button: "Learn More"
  },
];

export function Cards() {
  return (
    <section className="py-18 md:py-24">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 lg:gap-10">
          {cards.map((card, index) => (
            <div key={index} className="rounded-[40px] bg-white relative w-full max-w-[375.48px] h-[548.17px] mb-16 mx-auto">
              {/* Background container */}
              <div className="w-full h-[327.22px] bg-[rgba(227,227,227,0.3)] rounded-[40px]">
                {/* Icon overlay */}
                <div className="absolute top-[40px] left-1/2 transform -translate-x-1/2 w-[140px] h-[140px] flex items-center justify-center">
                  <Image
                    src={card.icon}
                    alt={card.title}
                    width={120}
                    height={120}
                    className="object-contain"
                  />
                </div>
              </div>
              
              {/* Card content */}
              <div className="absolute top-[64px] left-0 w-full">
                <div className="mt-[154.85px] w-full h-[329.32px] bg-white shadow-[22px_40px_40px_rgba(0,0,0,0.05)] rounded-[40px] p-10 flex flex-col overflow-hidden">
                  {/* Title */}
                  <h3 className="font-medium text-[38px] leading-[42px] text-primary tracking-[-0.02em] mb-8 max-w-[291px]">
                    {card.title}
                  </h3>
                  
                  {/* Description */}
                  <p className="font-semibold text-[18px] leading-[24px] text-[#888E92] mb-auto max-w-[287px]">
                    {card.description}
                  </p>
                  
                  {/* Button */}
                  <div className="mt-6 mb-3">
                    <Link href="/why-us">
                      <div className="border border-charity-border rounded-[100px] inline-block h-[29px] w-[126px]">
                        <span className="flex items-center justify-center h-full font-medium text-[18px] leading-[14px] text-black">
                          {card.button}
                        </span>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
