import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export function About() {
  return (
    <section className="py-16 md:py-24">
      <div className="container max-w-6xl mx-auto">
        <div className="flex flex-col lg:flex-row gap-10 lg:gap-20">
          {/* Left side - "Why us?" heading and button */}
          <div className="lg:w-1/3 flex flex-col gap-8">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-medium text-primary text-center lg:text-left">
              Why us?
            </h2>
            
            <div className="flex justify-center lg:justify-start">
              <Link href="/why-us">
                <Button 
                  className="bg-white text-[#1C3F3C] font-semibold text-[20px] leading-[1.6em] rounded-[40px] w-[295px] h-[89px] px-[30px] py-[14px] border-0 hover:bg-primary hover:text-white transition-colors"
                  style={{
                    boxShadow: '10px 14px 44px 0px rgba(28, 63, 60, 0.2)'
                  }}
                >
                  GET TO KNOW US
                </Button>
              </Link>
            </div>
          </div>
          
          {/* Right side - Content */}
          <div className="lg:w-2/3 flex items-start">
            <p className="text-lg md:text-xl text-primary font-normal leading-relaxed">
              <strong>We are a team of lawyers,</strong> with an aptitude for technology and deep legal and financial expertise in Greek, Cypriot and European Know-Your-Customer and Anti-Money-Laundering regulation. Frustrated by the countless KYC/KYB checks we have had to do for ourselves, for our company and for our clients, we decided to create a tool that not only simplifies on boarding for both the end-user and the companies they onboard with!
            </p>
          </div>
        </div>
      </div>
    </section>
  );
} 