"use client";

import { useState } from "react";
import { Minus, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

const faqs = [
  {
    question: "Why choose trustnexus?",
    answer:
      "Introducing trustnexus: Revolutionizing the KYC landscape with our cutting-edge digital platform.",
  },
  {
    question: "Do I need to change banks?",
    answer:
      "No, you don't need to change banks to use trustnexus. Our platform is designed to work seamlessly with your existing banking relationships.",
  },
  {
    question: "How does signing up work?",
    answer:
      "Signing up for trustnexus is simple and straightforward. Just visit our website, click on the 'Register' button, and follow the prompts to create your account.",
  },
];

export function FAQ() {
  const [openIndex, setOpenIndex] = useState<number>(0); // First item open by default

  const toggleFAQ = (index: number) => {
    setOpenIndex(index === openIndex ? -1 : index);
  };

  return (
    <section className="py-16 md:py-[66px] px-4 sm:px-6 md:px-[80px] bg-white rounded-[28px] max-w-[1191px] mx-auto">
      <div className="flex flex-col md:flex-row gap-8 md:gap-[90px]">
        {/* Left Column - Title and CTA */}
        <div className="flex flex-col gap-8 md:gap-[48px] md:max-w-[531px]">
          <h2 className="font-medium text-4xl md:text-[50px] leading-tight md:leading-[60px] tracking-[-0.04em] text-primary">
            Frequently Asked Questions
          </h2>
          
          <p className="font-medium text-base leading-[140%] text-[#878C91]">
            We've compiled answers to the most common questions about our platform. If you can't find what you're looking for, please reach out to our support team.
          </p>
          
          <div className="flex items-center gap-12 md:gap-[48px]">
            <p className="font-medium text-base leading-[140%] tracking-[-0.02em] text-black">
              More Questions
            </p>
            
            <Link href="/contact">
              <Button 
                variant="outline" 
                className="h-[56px] w-[176px] px-4 border border-[#010205] rounded-[50px] font-medium text-base leading-[140%] tracking-[-0.02em] text-black hover:bg-primary hover:text-white transition-colors"
              >
                Contact us
              </Button>
            </Link>
          </div>
        </div>
        
        {/* Right Column - FAQ Accordion */}
        <div className="flex flex-col w-full md:w-[410px]">
          <div className="flex flex-col">
            {/* First FAQ (Open) */}
            <div className="border-t border-b border-black">
              {openIndex === 0 ? (
                <div className="px-6 py-[26px]">
                  <div className="flex justify-between items-center mb-[48px]">
                    <span className="font-semibold text-[20px] leading-[20px] text-primary">
                      {faqs[0].question}
                    </span>
                    <button onClick={() => toggleFAQ(0)}>
                      <Minus className="w-6 h-6 text-black flex-shrink-0" />
                    </button>
                  </div>
                  <p className="text-base leading-[26px] text-[#5F6168] font-normal">
                    {faqs[0].answer}
                  </p>
                </div>
              ) : (
                <button
                  className="flex justify-between items-center w-full py-[26px] px-6 text-left"
                  onClick={() => toggleFAQ(0)}
                >
                  <span className="font-semibold text-[20px] leading-[20px] text-primary">
                    {faqs[0].question}
                  </span>
                  <Plus className="w-6 h-6 text-black flex-shrink-0" />
                </button>
              )}
            </div>
            
            {/* Second FAQ */}
            <div className="border-b border-black">
              {openIndex === 1 ? (
                <div className="px-6 py-[26px]">
                  <div className="flex justify-between items-center mb-[48px]">
                    <span className="font-semibold text-[20px] leading-[20px] text-primary">
                      {faqs[1].question}
                    </span>
                    <button onClick={() => toggleFAQ(1)}>
                      <Minus className="w-6 h-6 text-black flex-shrink-0" />
                    </button>
                  </div>
                  <p className="text-base leading-[26px] text-[#5F6168] font-normal">
                    {faqs[1].answer}
                  </p>
                </div>
              ) : (
                <button
                  className="flex justify-between items-center w-full py-[26px] px-6 text-left"
                  onClick={() => toggleFAQ(1)}
                >
                  <span className="font-semibold text-[20px] leading-[20px] text-primary">
                    {faqs[1].question}
                  </span>
                  <Plus className="w-6 h-6 text-black flex-shrink-0" />
                </button>
              )}
            </div>
            
            {/* Third FAQ */}
            <div className="border-b border-black">
              {openIndex === 2 ? (
                <div className="px-6 py-[26px]">
                  <div className="flex justify-between items-center mb-[48px]">
                    <span className="font-semibold text-[20px] leading-[20px] text-primary">
                      {faqs[2].question}
                    </span>
                    <button onClick={() => toggleFAQ(2)}>
                      <Minus className="w-6 h-6 text-black flex-shrink-0" />
                    </button>
                  </div>
                  <p className="text-base leading-[26px] text-[#5F6168] font-normal">
                    {faqs[2].answer}
                  </p>
                </div>
              ) : (
                <button
                  className="flex justify-between items-center w-full py-[26px] px-6 text-left"
                  onClick={() => toggleFAQ(2)}
                >
                  <span className="font-semibold text-[20px] leading-[20px] text-primary">
                    {faqs[2].question}
                  </span>
                  <Plus className="w-6 h-6 text-black flex-shrink-0" />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
