"use client";

import { useState, useRef, useEffect } from "react";
import { cn } from "@/utils/tailwind";
import Image from "next/image";
import Link from "next/link";
import { INDUSTRIES_DATA } from "@/data/industries";
import left from "../../../public/figma-icons/view-all-left.png";
import right from "../../../public/figma-icons/view-all-right.png";

// Take all industries for proper pagination
const homeIndustries = INDUSTRIES_DATA;

export function HomeFeatures() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [maxVisible, setMaxVisible] = useState(2); // Fixed number of cards per slide

  // Calculate fixed number of cards per slide
  useEffect(() => {
    const updateMaxVisible = () => {
      if (scrollContainerRef.current) {
        const containerWidth = scrollContainerRef.current.offsetWidth;
        const isMobile = containerWidth < 1024; // Use 1024px as breakpoint

        // Fixed number of items per slide: 2 for mobile/tablet, 3 for desktop
        const count = isMobile ? 2 : 3;
        setMaxVisible(count);
      }
    };

    updateMaxVisible();
    window.addEventListener('resize', updateMaxVisible);
    return () => window.removeEventListener('resize', updateMaxVisible);
  }, []);

  // Update current index based on scroll position
  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const scrollLeft = scrollContainerRef.current.scrollLeft;
      const containerWidth = scrollContainerRef.current.offsetWidth;
      const pageWidth = containerWidth; // Each page takes full container width
      const newIndex = Math.round(scrollLeft / pageWidth);
      setCurrentIndex(newIndex);
    }
  };

  const scrollToIndex = (index: number) => {
    if (scrollContainerRef.current) {
      const containerWidth = scrollContainerRef.current.offsetWidth;
      scrollContainerRef.current.scrollTo({
        left: index * containerWidth,
        behavior: 'smooth'
      });
      setCurrentIndex(index);
    }
  };

  const scrollLeft = () => {
    const newIndex = Math.max(0, currentIndex - 1);
    scrollToIndex(newIndex);
  };

  const scrollRight = () => {
    const maxIndex = totalPages - 1;
    const newIndex = Math.min(maxIndex, currentIndex + 1);
    scrollToIndex(newIndex);
  };

  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(homeIndustries.length / maxVisible));

  return (
    <section className="py-10 lg:py-24 relative overflow-hidden">
      {/* Title Section */}
      <div className="container mx-auto px-4 mb-6">
        <h2 
          className="text-[60px] leading-[70px] font-medium text-[#231F20] tracking-[-0.033em]"
        >
          Marketplace
        </h2>
      </div>

      {/* Scrollable Industry Cards */}
      <div className="container mx-auto px-4 mb-8 max-w-8xl">
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className="overflow-x-auto scrollbar-hide py-6"
          style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            scrollSnapType: 'x mandatory',
          }}
        >
          <div className="flex">
            {/* Create pages with fixed card counts */}
            {Array.from({ length: totalPages }).map((_, pageIndex) => (
              <div
                key={pageIndex}
                className="flex-shrink-0 w-full grid gap-6 lg:gap-8 px-4"
                style={{
                  gridTemplateColumns: `repeat(${maxVisible}, minmax(300px, 1fr))`,
                  scrollSnapAlign: 'start',
                }}
              >
                {homeIndustries
                  .slice(pageIndex * maxVisible, (pageIndex + 1) * maxVisible)
                  .map((industry) => (
                    <Link
                      key={industry.slug}
                      href={industry.href}
                      className="group"
                    >
                      <div className="h-[160px] bg-white rounded-[40px] border-2 border-[#E3E3E3] hover:border-[#C8E2CE] transition-all duration-300 hover:shadow-lg relative overflow-hidden min-w-[300px]">
                        {/* Icon - Top Right */}
                        <div className="absolute top-[32px] right-[16px] lg:right-[24px]">
                          <div className="w-[48px] h-[48px] lg:w-[64px] lg:h-[64px] relative">
                            <Image
                              src={industry.icon}
                              alt={`${industry.title} icon`}
                              fill
                              className="object-contain"
                            />
                          </div>
                        </div>

                        {/* Content - Left Side */}
                        <div className="absolute left-[24px] lg:left-[44px] top-[27px] right-[80px] lg:right-[100px]">
                          <h3
                            className="text-[20px] lg:text-[26px] leading-[24px] lg:leading-[30px] font-semibold text-[#1C3F3C] mb-[12px] lg:mb-[18px] pr-2"
                          >
                            {industry.title}
                          </h3>
                          <p
                            className="text-[14px] lg:text-[18px] leading-[18px] lg:leading-[24px] font-semibold text-[#888E92] pr-2"
                          >
                            {industry.subtitle}
                          </p>
                </div>
                      </div>
                    </Link>
                  ))}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Navigation Controls - BELOW THE CARDS */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-center">
          {/* Navigation Group */}
          <div className="flex items-center gap-6">
            {/* Left Arrow */}
            <button
              onClick={scrollLeft}
              disabled={currentIndex === 0}
              className={cn(
                "w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200",
                currentIndex === 0 
                  ? "opacity-30 cursor-not-allowed" 
                  : "hover:bg-gray-100"
              )}
              aria-label="Previous"
            >
              <Image
                src={left}
                alt="Previous"
                width={16}
                height={28}
              />
            </button>

            {/* Pagination Dots */}
            <div className="flex items-center gap-2">
              {Array.from({ length: totalPages }, (_, i) => (
                <button
                  key={i}
                  onClick={() => scrollToIndex(i)}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-200",
                    i === currentIndex
                      ? "bg-[#1C3F3C]"
                      : "bg-gray-300 hover:bg-gray-400"
                  )}
                  aria-label={`Go to page ${i + 1}`}
                />
              ))}
            </div>

            {/* Right Arrow */}
            <button
              onClick={scrollRight}
              disabled={currentIndex >= totalPages - 1}
              className={cn(
                "w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200",
                currentIndex >= totalPages - 1
                  ? "opacity-30 cursor-not-allowed"
                  : "hover:bg-gray-100"
              )}
              aria-label="Next"
            >
              <Image
                src={right}
                alt="Next"
                width={16}
                height={28}
              />
            </button>
          </div>

          {/* View All Button - Positioned to the right */}
          <Link
            href="/marketplace"
            className="absolute right-4 flex items-center gap-1 text-[#C8E2CE] hover:text-[#1C3F3C] transition-colors duration-200 mr-10"
          >
            <span 
              className="text-[19px] leading-[29px] font-normal"
            >
              View all
            </span>
          </Link>
        </div>
      </div>

      {/* Custom scrollbar hide styles */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  );
} 