import React, { useState, useEffect } from "react";
import SumSubIframe from "./sumsub-iframe";
import { SUMSUB_LEVEL } from "@/types/user";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { getSumSubToken } from "@/react-query/query/sumsub-token";
import { getClientSumSubToken } from "@/utils/client-sumsub-token";
import { X, Video, Clock } from "lucide-react";
import { useFetchCompany } from "@/hooks/use-fetch-company";

type Notification = {
  message: string;
  title: string;
  type: string;
  timestamp: Date;
  companyId?: string;
};

interface InterviewNotificationModalProps {
  isOpen: boolean;
  notification: Notification | null;
  onClose: () => void;
  onAccept: () => void;
  onReject: () => void;
}

export const InterviewNotificationModal: React.FC<InterviewNotificationModalProps> = ({
  isOpen,
  notification,
  onClose,
  onAccept,
  onReject,
}) => {

  const [showSumSubModal, setShowSumSubModal] = useState(false);
  const [sumSubToken, setSumSubToken] = useState<string | null>(null);
  const { data: userData } = useGetUserProfile();

  // Fetch company data if companyId is provided
  const { companyData, loading: companyLoading } = useFetchCompany(
    notification?.companyId || undefined
  );

  const handleAccept = async () => {
    try {
      // Check if user is company type and has beneficiary information
      const isCompanyUser = userData?.registrationtype === "company";
      const beneficiaryUserId = userData?.userCompany?.beneficiaryUserId;
      const beneficiaryApplicantId = userData?.userCompany?.beneficiaryApplicantId;
      
      console.log("User type:", userData);
      console.log("Is company user:", isCompanyUser);
      console.log("Beneficiary User ID:", beneficiaryUserId);
      
      let token;
      
      if (isCompanyUser && beneficiaryUserId && beneficiaryApplicantId) {
        // For company users, use the beneficiary userId with client-side function
        console.log("Using beneficiary userId for company user:", beneficiaryUserId);
        token = await getClientSumSubToken(SUMSUB_LEVEL.INTERVIEW, beneficiaryUserId, beneficiaryApplicantId);
      } else {
        // For individual users or companies without beneficiary info, use regular token
        console.log("Using regular token for individual user or company without beneficiary");
        token = await getSumSubToken(SUMSUB_LEVEL.INTERVIEW);
      }
      
      if (token) {
        setSumSubToken(token);
        setShowSumSubModal(true);
      }
    } catch (error) {
      console.error("Failed to get SumSub token:", error);
      // Fallback to original onAccept if token fails
      onAccept();
    }
  };

  const handleSumSubComplete = () => {
    setShowSumSubModal(false);
    setSumSubToken(null);
    onAccept(); // Call original accept handler
    onClose(); // Close the notification modal
  };

  const handleModalClose = () => {
    setShowSumSubModal(false);
    setSumSubToken(null);
    onClose();
  };

  if (!isOpen || !notification) return null;

  // Show SumSub modal if it's open
  if (showSumSubModal && sumSubToken && userData?.email) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
        <div className="relative w-full max-w-4xl mx-auto bg-white rounded-[1.25rem] border border-[#C8E2CE] shadow-lg p-6 max-h-[90vh] overflow-hidden">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Interview Verification</h2>
            <button
              onClick={handleModalClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <SumSubIframe
            token={sumSubToken}
            email={userData.email}
            sumsubLevel={SUMSUB_LEVEL.INTERVIEW}
            onApplicantReviewComplete={handleSumSubComplete}
          />
        </div>
      </div>
    );
  }

  // Show original notification modal
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      <div className="relative w-full max-w-[28.25rem] mx-auto px-4">
        {/* Main Card */}
        <div className="bg-white rounded-[1.25rem] border border-[#C8E2CE] p-4 md:p-6 flex flex-col items-center">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute left-4 md:left-6 top-4 md:top-6 rounded-full p-1 bg-gray-100 hover:bg-gray-200 focus:ring-2 focus:ring-primary focus:outline-none"
          >
            <X className="w-5 h-5 text-black" />
          </button>

          {/* Company Logo Section */}
          {companyData && (
            <div className="flex justify-center items-center gap-3 md:gap-[1.125rem] mt-8 md:mt-[2.8125rem]">
              <div className="w-[32px] md:w-[40.87px] h-[28px] md:h-[36px] rounded-[1rem] md:rounded-[1.3125rem] overflow-hidden bg-gray-50 flex items-center justify-center">
                <img
                  src={companyData.s3ImageUrl || companyData.s3LogoUrl || ''}
                  alt={`${companyData.name} logo`}
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    e.currentTarget.parentElement!.style.display = 'none';
                  }}
                />
              </div>
              <div className="text-sm md:text-base font-poppins font-medium text-[#231F20]">
                {companyData.name}
              </div>
            </div>
          )}

          {/* Interview Icon Section - Show when no company data */}
          {!companyData && (
            <div className="flex justify-center items-center gap-3 md:gap-[1.125rem] mt-8 md:mt-[2.8125rem]">
              <div className="w-[32px] md:w-[40.87px] h-[28px] md:h-[36px] rounded-[1rem] md:rounded-[1.3125rem] overflow-hidden bg-primary/10 flex items-center justify-center">
                <Video className="w-4 md:w-5 h-4 md:h-5 text-primary" />
              </div>
              <div className="text-sm md:text-base font-poppins font-medium text-[#231F20]">
                Interview Request
              </div>
            </div>
          )}

          {/* Title Section */}
          <div className="flex justify-center mt-2 md:mt-[2.1875rem]">
            <div className="font-poppins font-medium text-lg md:text-[1.25rem] leading-[1em] text-center tracking-[0.02em] text-primary px-4">
              {notification.title}
            </div>
          </div>

          {/* Interview Icon */}
          <div className="flex justify-center mt-4 md:mt-[1.9375rem]">
            <div className="w-[70px] h-[68px] md:w-[88px] md:h-[87px] bg-primary/10 rounded-full flex items-center justify-center">
              <Video className="w-8 md:w-10 h-8 md:h-10 text-primary" />
            </div>
          </div>

          {/* Description Section */}
          <div className="bg-[#EFF7ED] rounded-lg p-3 md:p-4 mt-4 md:mt-6 w-full text-sm md:text-base text-[#1C3F3C] font-poppins">
            {companyData ? (
              <>
                You have been invited by <strong>{companyData.name}</strong> to complete a video interview as part of your verification process. {notification.message}
              </>
            ) : (
              notification.message
            )}
          </div>

          {/* Interview Details */}
          <div className="w-full flex flex-col gap-1 md:gap-2 mb-4 md:mb-6 mt-4 md:mt-6">
            <div className="flex items-start gap-2 text-[#444] font-poppins text-sm md:text-base leading-6 md:leading-7">
              <Clock className="text-primary w-3 h-3 shrink-0 mt-1" />
              <span className="break-words">Estimated duration: 10-15 minutes</span>
            </div>
            <div className="flex items-start gap-2 text-[#444] font-poppins text-sm md:text-base leading-6 md:leading-7">
              <Video className="text-primary w-3 h-3 shrink-0 mt-1" />
              <span className="break-words">Video and audio recording required</span>
            </div>
            <div className="flex items-start gap-2 text-[#444] font-poppins text-sm md:text-base leading-6 md:leading-7">
              <Clock className="text-primary w-3 h-3 shrink-0 mt-1" />
              <span className="break-words">Working hours: 9:00 - 17:00</span>
            </div>
          </div>

          {/* Accept Button */}
          <button
            onClick={handleAccept}
            className="w-full max-w-[15.5rem] h-[3rem] md:h-[3.5rem] bg-primary rounded-[2rem] md:rounded-[2.5rem] font-poppins font-semibold text-sm md:text-base text-white hover:bg-primary/90 focus:ring-2 focus:ring-offset-2 focus:ring-primary focus:outline-none"
          >
            START INTERVIEW
          </button>

          {/* Decline Button */}
          <button
            onClick={onReject}
            className="w-full max-w-[15.5rem] h-[3rem] md:h-[3.5rem] mt-2 mx-auto bg-white rounded-[2rem] md:rounded-[2.5rem] font-poppins font-medium text-sm md:text-base text-primary flex items-center justify-center focus:ring-2 focus:ring-offset-2 focus:ring-primary focus:outline-none shadow-[0px_6px_8.3px_4px_rgba(28,63,60,0.2)]"
          >
            DECLINE
          </button>
        </div>
      </div>
    </div>
  );
}; 