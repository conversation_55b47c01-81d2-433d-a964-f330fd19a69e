"use client";

const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");

interface AlphabetNavigationProps {
  selectedLetter: string | null;
  setSelectedLetter: (letter: string | null) => void;
}

export function AlphabetNavigation({
  selectedLetter,
  setSelectedLetter,
}: AlphabetNavigationProps) {
  return (
    <>
      {/* Desktop Version - Enhanced responsiveness with viewport constraints */}
      <div className="hidden lg:flex fixed right-4 xl:right-8 top-[375px] xl:top-[375px] bottom-8 flex-col gap-1 xl:gap-[5px] z-10 max-h-[calc(100vh-220px)] xl:max-h-[calc(100vh-240px)]">
        <div className="flex flex-col gap-1 xl:gap-[5px] overflow-y-auto alphabet-scrollbar-hidden pr-1">
          {/* Clear Filter Button */}
          <button
            onClick={() => setSelectedLetter(null)}
            className={`text-xs xl:text-sm px-2 xl:px-3 py-1 xl:py-1.5 mb-2 rounded font-semibold transition-all duration-200 flex-shrink-0 ${
              selectedLetter === null
                ? "bg-primary text-white shadow-md"
                : "bg-gray-200 text-black hover:bg-gray-300 hover:shadow-sm"
            }`}
          >
            Clear
          </button>

          {alphabet.map((letter) => (
            <button
              key={letter}
              onClick={() => setSelectedLetter(letter)}
              className={`text-xs xl:text-sm w-[18px] h-[18px] xl:w-[22px] xl:h-[22px] rounded-full font-semibold transition-all duration-200 flex items-center justify-center flex-shrink-0 ${
                selectedLetter === letter
                  ? "border-2 border-primary bg-primary/10 text-primary shadow-sm"
                  : "text-black hover:bg-gray-100 hover:text-primary"
              }`}
            >
              {letter}
            </button>
          ))}
        </div>
      </div>

      {/* Tablet Version - New breakpoint for medium screens */}
      <div className="hidden md:flex lg:hidden justify-center w-full py-3">
        <div className="flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full shadow-sm border border-gray-200 max-w-full overflow-x-auto scrollbar-hide">
          {/* Clear Filter Button */}
          <button
            onClick={() => setSelectedLetter(null)}
            className={`flex-shrink-0 px-3 py-1.5 rounded-full text-sm font-semibold transition-all duration-200 ${
              selectedLetter === null
                ? "bg-primary text-white shadow-md"
                : "bg-gray-200 text-[#231F20] hover:bg-gray-300"
            }`}
          >
            Clear
          </button>

          <div className="w-px h-6 bg-gray-300 mx-1"></div>

          {alphabet.map((letter) => (
            <button
              key={letter}
              onClick={() => setSelectedLetter(letter)}
              className={`flex-shrink-0 relative w-8 h-8 rounded-full text-sm font-semibold transition-all duration-200 flex items-center justify-center ${
                selectedLetter === letter
                  ? "bg-primary text-white shadow-md"
                  : "text-[#231F20] hover:bg-gray-100 hover:text-primary"
              }`}
            >
              {letter}
            </button>
          ))}
        </div>
      </div>

      {/* Mobile Version - Enhanced for better touch experience */}
      <div className="md:hidden flex items-center gap-2 px-3 py-3 w-full overflow-x-auto scrollbar-hide">
        {/* Clear Filter Button */}
        <button
          onClick={() => setSelectedLetter(null)}
          className={`flex-shrink-0 px-4 py-2 rounded-full text-sm font-semibold transition-all duration-200 min-w-[60px] ${
            selectedLetter === null
              ? "bg-[#1C3F3C] text-white shadow-md"
              : "bg-gray-200 text-[#231F20] hover:bg-gray-300"
          }`}
        >
          Clear
        </button>

        {alphabet.map((letter) => (
          <button
            key={letter}
            onClick={() => setSelectedLetter(letter)}
            className={`flex-shrink-0 relative w-10 h-10 rounded-full text-sm font-semibold transition-all duration-200 flex items-center justify-center ${
              selectedLetter === letter
                ? "bg-[#1C3F3C] text-white shadow-md scale-105"
                : "text-[#231F20] hover:bg-gray-100 active:bg-gray-200"
            }`}
          >
            {letter}
          </button>
        ))}
      </div>
    </>
  );
}
