import Link from "next/link";
import { CompanyTier } from "@/components/company/tier";
import { useFetchCompanies } from "@/hooks/use-fetch-companies";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { SUMSUB_LEVEL } from "@/types/user";
import Image from "next/image";

interface CategoryListProps {
  selectedCategorySlug: string | null;
  filterByLetter?: string | null;
  searchQuery?: string;
}

export function CompanyList({
  selectedCategorySlug,
  filterByLetter,
  searchQuery,
}: CategoryListProps) {
  const { companies, loading: companiesLoading, error } = useFetchCompanies();
  const { data: userData, isPending: userLoading } = useGetUserProfile();

  // Only proceed when both data sources are loaded
  const loading = companiesLoading || userLoading;

  const getTierNumber = (state?: string) => {
    if (!state) return 0;
    return parseInt(state.replace(/[^\d]/g, "")) || 0;
  };

  if (loading) {
    return (
      <p className="text-center text-gray-500 text-base md:text-lg py-4">
        Loading companies...
      </p>
    );
  }

  if (error) {
    return (
      <p className="text-center text-red-500 text-base md:text-lg py-4">
        Failed to load companies: {error}
      </p>
    );
  }

  // Make sure we have valid user data
  const userTier = getTierNumber(userData?.state);
  const userRegistrationType = userData?.registrationtype;

  // Filter companies by category (if a category slug is selected)
  const filteredByCategory = selectedCategorySlug
    ? companies?.filter(
        (company) =>
          company.category?.toLowerCase() === selectedCategorySlug.toLowerCase()
      )
    : companies;

  // Filter companies by starting letter (if specified)
  const filteredByLetter = filterByLetter
    ? filteredByCategory.filter((company) =>
        company.name.toLowerCase().startsWith(filterByLetter.toLowerCase())
      )
    : filteredByCategory;

  // Filter companies by search query (if specified)
  const filteredCompanies =
    searchQuery && searchQuery.trim()
      ? filteredByLetter.filter((company) =>
          company.name.toLowerCase().includes(searchQuery.toLowerCase().trim())
        )
      : filteredByLetter;

  // Sort companies to put "Attica Bank" first (temporary hardcoded priority)
  const sortedCompanies = filteredCompanies.sort((a, b) => {
    if (a.name === "Attica Bank") return -1;
    if (b.name === "Attica Bank") return 1;
    return 0;
  });

  return (
    <div className="h-full pr-2 md:pr-4">
      <div className="space-y-4 md:space-y-5">
        {/* Display list of companies or fallback if no matches */}
        {sortedCompanies.length > 0 ? (
          sortedCompanies.map((company, index) => {
            const isIndividual = userRegistrationType === "individual";
            const requiredTier = isIndividual
              ? company.required_individual_tier
              : company.required_corporate_tier;
            const isAtticaBank = company.name === "Attica Bank";

            return (
              <Link
                href={`/company/${company.company_id}`}
                key={`${company.company_id}-${index}`}
                className="block"
              >
                <div
                  className={`flex items-center gap-4 md:gap-6 p-4 md:p-2 rounded-full border border-border ${
                    requiredTier > userTier
                      ? "bg-light-gray"
                      : "bg-white/50 hover:bg-white/80 cursor-pointer"
                  } transition-colors duration-200 ${isAtticaBank ? "ring-2 ring-blue-200" : ""}`}
                >
                  {/* Company Logo */}
                  <div className="w-10 h-10 md:w-[38px] md:h-[38px] rounded-full overflow-hidden bg-white flex-shrink-0">
                    <div className="w-10 h-10 md:w-[38px] md:h-[38px] rounded-full overflow-hidden bg-white flex-shrink-0">
                      <div className="relative w-10 h-10 md:w-[38px] md:h-[38px] rounded-full overflow-hidden bg-white flex-shrink-0">
                        <div className="w-10 h-10 md:w-[38px] md:h-[38px] rounded-full overflow-hidden bg-white flex-shrink-0">
                          <img
                            src={company.s3LogoUrl}
                            alt={company.name || "Company Logo"} // Add alt text for accessibility
                            className="object-contain w-full h-full"
                            onError={(e) => {
                              e.currentTarget.src =
                                "/images/logo-placeholder.png"; // Replace with fallback logo on error
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Company Name */}
                  <div className="flex-1 flex flex-col">
                    <div className="flex items-center gap-2">
                      <p className="text-xs md:text-xl text-black font-medium truncate">
                        {company.name}
                      </p>
                      {isAtticaBank && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Best in Category
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Tier and Lock */}
                  <CompanyTier
                    companyTier={requiredTier as unknown as SUMSUB_LEVEL}
                    userTier={userTier}
                  />
                </div>
              </Link>
            );
          })
        ) : (
          <p className="text-center text-gray-500 text-base md:text-lg py-4">
            No companies available for this selection.
          </p>
        )}
      </div>
    </div>
  );
}
