"use client";

import React, { useEffect, useState } from "react";
import { Menu, X } from "lucide-react";
import { Logo } from "@/components/logo";
import { SearchInput } from "@/components/search-input";
import { UserDropdown } from "@/components/user-dropdown";
import Link from "next/link";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { TIER_NAMES } from "@/components/company/tier";
import { useRouter } from "next/navigation";
import { BiSearch } from "react-icons/bi";
import { SignOutConfirmationModal } from "@/components/sign-out-confirmation-modal";

interface HeaderProps {
  hideSearchBar?: boolean;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
}

export const Header: React.FC<HeaderProps> = ({ 
  hideSearchBar = false, 
  searchQuery: externalSearchQuery,
  onSearchChange 
}) => {
  const [internalSearchQuery, setInternalSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isSignOutModalOpen, setIsSignOutModalOpen] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  const data = useGetUserProfile();
  const router = useRouter();

  // Use external search query if provided, otherwise use internal state
  const searchQuery = externalSearchQuery !== undefined ? externalSearchQuery : internalSearchQuery;
  const setSearchQuery = onSearchChange || setInternalSearchQuery;

  const getTierNumber = (state?: string) => {
    if (!state) return 0;
    return parseInt(state.replace(/[^\d]/g, "")) || 0;
  };

  // Get the tier name based on the tier number
  const getTierName = (state?: string) => {
    const tierNumber = getTierNumber(state);
    return TIER_NAMES[tierNumber] || `Tier #${tierNumber}`;
  };

  const handleSignOutClick = () => {
    setIsOpen(false); // Close the mobile menu
    setIsSignOutModalOpen(true);
  };

  const handleSignOutConfirm = () => {
    setIsSigningOut(true);
    // Navigate to logout page which handles the actual logout process
    router.push("/logout");
  };

  const handleSignOutCancel = () => {
    setIsSignOutModalOpen(false);
    setIsSigningOut(false);
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  // @ts-ignore
  return (
    <>
      <header className="relative z-100">
        <div className="flex flex-col md:flex-row md:h-[132px] items-center justify-between px-4 py-4 md:py-0">
          {/* Mobile Layout */}
          <div className="w-full flex flex-col gap-4 md:hidden">
            <div className="flex items-center justify-between">
              <Logo className="w-[120px] h-auto" />
              <button
                className="p-2 hover:bg-gray-100 rounded-full z-50"
                onClick={() => setIsOpen(!isOpen)}
              >
                {isOpen ? (
                  <X className="w-6 h-6 text-[#231F20]" aria-hidden="true" />
                ) : (
                  <Menu className="w-6 h-6 text-[#231F20]" aria-hidden="true" />
                )}
              </button>
            </div>
            {!hideSearchBar && (
              <SearchInput
                placeholder="Search"
                value={searchQuery}
                onChange={setSearchQuery}
                className="border border-gray-200 focus:ring-2 focus:ring-[#1c3f3c]"
              />
            )}
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:flex w-full items-center justify-between gap-8">
            <Link href="/categories" className="cursor-pointer">
              <Logo className="w-[203px] h-[41px]" />
            </Link>
            <div className="flex items-center gap-5">
              {!hideSearchBar && (
                <SearchInput
                  placeholder="Search"
                  value={searchQuery}
                  onChange={setSearchQuery}
                  className="border border-gray-200 lg:w-[714px] focus:ring-2 focus:ring-[#1c3f3c]"
                />
              )}
              <div className="flex items-center gap-6">
                {getTierNumber(data?.data?.state) > 0 && (
                  <div className="flex items-center justify-center h-[30px] px-4 rounded-full border border-[#1C3F3C] whitespace-nowrap">
                    <span className="text-base font-normal text-[#231F20]">
                      {/*// @ts-ignore*/}
                      {getTierName(data?.data?.state)}
                    </span>
                  </div>
                )}
                {/*// @ts-ignore*/}
                <UserDropdown
                  // @ts-ignore
                  kycLevel={getTierNumber(data?.data?.state)}
                />{" "}
                {/* Prop passed dynamically */}
              </div>
            </div>
          </div>

          {/* Mobile Navigation Dropdown */}
          {isOpen && (
            <>
              <div className="md:hidden fixed inset-0 bg-white bg-opacity-90 z-40"></div>
              <div className="md:hidden fixed top-[132px] left-0 w-full bg-white shadow-md z-50 overflow-y-auto max-h-[calc(100vh-132px)]">
                <div className="px-4 pt-2 pb-3 space-y-1">
                  {getTierNumber(data?.data?.state) > 0 && (
                    <div className="flex items-center px-3 py-2 mb-2">
                      <div className="ml-3">
                        <div className="text-base font-medium text-[#1C3F3C]">
                          {/*// @ts-ignore*/}
                          {getTierName(data?.data?.state)}
                        </div>
                      </div>
                    </div>
                  )}
                  <button
                    onClick={() => {
                      setIsOpen(false);
                      router.push("/profile");
                    }}
                    className="w-full text-left block px-3 py-2 text-sm font-normal text-[#231F20] hover:bg-[#F3F4F6]"
                  >
                    Profile Settings
                  </button>
                  <button
                    onClick={handleSignOutClick}
                    className="w-full text-left block px-3 py-2 text-sm font-normal text-red-600 hover:bg-red-50"
                  >
                    Sign Out
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </header>

      {/* Sign Out Confirmation Modal */}
      <SignOutConfirmationModal
        isOpen={isSignOutModalOpen}
        onClose={handleSignOutCancel}
        onConfirm={handleSignOutConfirm}
        isLoading={isSigningOut}
      />
    </>
  );
};
