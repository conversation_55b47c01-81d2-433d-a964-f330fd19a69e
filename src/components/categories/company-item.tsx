"use client";

import Image from "next/image";
import { LockIcon } from "lucide-react";

interface CategoryItemProps {
  category: {
    id: string;
    name: string;
    tier: number;
    logo: any;
  };
  userTier: number; // User's current tier to check locking
}

export function CompanyItem({ category, userTier }: CategoryItemProps) {
  // Determine if the company should be locked based on the tier
  const isLocked = category.tier > userTier;

  return (
    <div
      className={`flex items-center justify-between w-full rounded-full px-3 py-2 sm:px-4 sm:py-3 ${
        isLocked ? "bg-[#E3E3E3]" : "bg-white bg-opacity-50"
      } border border-[#C8E2CE] shadow-[0px_1px_3px_rgba(3,8,24,0.1)]`}
    >
      <div className="flex items-center gap-3 sm:gap-6">
        <div className="relative w-8 h-8 sm:w-[38px] sm:h-[38px]">
          <Image
            src={category.logo || "/placeholder.svg?height=38&width=38"}
            alt={`${category.name} logo`}
            width={38}
            height={38}
            className="rounded-full"
          />
        </div>
        <span className="font-poppins font-medium text-sm sm:text-[20px] leading-tight sm:leading-[26px] text-[#231F20] truncate max-w-[150px] sm:max-w-none">
          {category.name}
        </span>
      </div>
      <div className="flex items-center gap-2 sm:gap-3">
        <div className="flex items-center justify-center h-6 sm:h-[33px] px-2 sm:px-3 border border-[#1C3F3C] rounded-full">
          <span className="font-poppins font-normal text-xs sm:text-[20px] leading-none sm:leading-[20px] tracking-[0.02em] text-[#1C3F3C]">
            Tier #{category.tier ?? 1}
          </span>
        </div>
        {isLocked && (
          <div className="w-6 h-6 sm:w-[23px] sm:h-[23px] flex items-center justify-center">
            <LockIcon size={16} className="sm:w-5 sm:h-5" color="#1C3F3C" />
          </div>
        )}
      </div>
    </div>
  );
}
