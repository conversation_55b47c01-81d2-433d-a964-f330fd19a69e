import { ChevronRight } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export function CategoryBreadcrumbs() {
  return (
    <div className="flex items-center justify-between mb-6 px-4 sm:px-0 z-10">
      <div className="flex items-center gap-3">
        <span className="text-xl font-normal text-primary">Categories</span>
        <ChevronRight color="#1C3F3C" className="w-[21px] h-[21px]" />
        <span className="text-xl font-normal text-primary">Search</span>
      </div>

      <Link
        href="/marketplace"
        className="flex items-center gap-2 text-xl font-normal text-primary hover:opacity-80 transition-opacity"
      >
        <span>View all</span>
      </Link>
    </div>
  );
}
