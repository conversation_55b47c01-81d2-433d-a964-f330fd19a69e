import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/utils/tailwind";

const buttonVariants = cva(
  "inline-flex text-center items-center justify-center rounded-lg min-w-[100px] min-h-[0px] text-lg font-semibold transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-white font-medium hover:bg-black disabled:bg-gray-300 rounded-[40px]",
        accent:
          "bg-black text-white font-medium hover:bg-gray-800 disabled:bg-gray-300",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        brand: "bg-brand text-white hover:bg-brand-foreground rounded-2xl",
        secondary:
          "bg-white text-primary rounded-[40px] hover:primary/10 drop-shadow-sm",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        text: "text-primary hover:text-button-text-hover disabled:text-primary/50 outline-none",
        "text-underline":
          "text-accent underline hover:text-accent-hover min-w-0 leading-5",
        icon: "bg-transparent min-w-0 ",
        pagination: "text-gray-secondary hover:text-primary min-w-0",
        "pagination-active": "bg-button-pagination min-w-0",
      },
      size: {
        default: "h-12 px-4 py-2 w-full",
        sm: "h-9 rounded-md px-3",
        md: "px-4 py-3",
        lg: "h-14 md:h-full px-[20px] w-full md:py-[28px] md:px-[42px]",
        icon: "h-10 w-10",
        text: "p-0 min-w-none",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
