"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export function LogoutHandler() {
  const queryClient = useQueryClient();
  const router = useRouter();

  useEffect(() => {
    const handleLogout = async () => {
      // Clear all React Query cache data
      await queryClient.clear();
      
      // Redirect to the server-side logout endpoint which will clear cookies
      router.push("/api/logout");
    };

    handleLogout();
  }, [queryClient, router]);

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      <span className="ml-2">Logging out...</span>
    </div>
  );
} 