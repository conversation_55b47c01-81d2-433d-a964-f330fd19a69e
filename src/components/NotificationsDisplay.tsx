import React, { useEffect, useState } from 'react';
import { useSignalR } from '../hooks/useSignalR';
import { InterviewNotificationModal } from './interview-notification-modal';
import { useGetUserProfile } from '@/react-query/auth-hooks';
import { useGetUserNotifications } from '@/react-query/notifications-hook';
import 'react-toastify/dist/ReactToastify.css';

interface NotificationsDisplayProps {
  userId: string | null;
  apiBaseUrl: string;
}

const NotificationsDisplay: React.FC<NotificationsDisplayProps> = ({ userId, apiBaseUrl }) => {
  const { 
    isConnected,
    currentInterviewNotification,
    isInterviewModalOpen,
    closeInterviewModal,
    handleAcceptInterview,
    handleRejectInterview 
  } = useSignalR(apiBaseUrl, userId);

  const { data: userProfile } = useGetUserProfile();
  const { data: notifications } = useGetUserNotifications(userId);
  
  const [showInterviewModal, setShowInterviewModal] = useState(false);
  const [pendingInterviewNotification, setPendingInterviewNotification] = useState<any>(null);

  // Check for pending interview notifications after login
  useEffect(() => {
    if (userProfile && notifications && userId) {
      // Check if interview is not completed
      const interviewNotCompleted = !userProfile.userInfo?.interviewCompletedDate;
      
      if (interviewNotCompleted) {
        // Look for Interview notification
        const interviewNotification = notifications.find(
          notification => notification.notificationType.name.toLowerCase() === 'interview'
        );
        
        if (interviewNotification && !isInterviewModalOpen) {
          // Convert API notification format to the format expected by the modal
          const modalNotification = {
            message: interviewNotification.message,
            title: "Interview Request",
            type: "interview",
            timestamp: new Date(interviewNotification.createdAt)
          };
          
          setPendingInterviewNotification(modalNotification);
          setShowInterviewModal(true);
        }
      }
    }
  }, [userProfile, notifications, userId, isInterviewModalOpen]);

  const handleClosePendingModal = () => {
    setShowInterviewModal(false);
    setPendingInterviewNotification(null);
  };

  const handleAcceptPendingInterview = async () => {
    // Handle accept logic here - you can call an API endpoint
    console.log('Pending interview accepted');
    handleClosePendingModal();
  };

  const handleRejectPendingInterview = async () => {
    // Handle reject logic here - you can call an API endpoint
    console.log('Pending interview rejected');
    handleClosePendingModal();
  };

  return (
    <>
      {/* SignalR Interview Notification Modal */}
      <InterviewNotificationModal
        isOpen={isInterviewModalOpen}
        notification={currentInterviewNotification}
        onClose={closeInterviewModal}
        onAccept={handleAcceptInterview}
        onReject={handleRejectInterview}
      />
      
      {/* Pending Interview Notification Modal (from API check) */}
      <InterviewNotificationModal
        isOpen={showInterviewModal}
        notification={pendingInterviewNotification}
        onClose={handleClosePendingModal}
        onAccept={handleAcceptPendingInterview}
        onReject={handleRejectPendingInterview}
      />
      
      {/* Connection status indicator */}
      {!isConnected && userId && (
        <div className="fixed bottom-4 right-4 p-2 bg-yellow-100 text-yellow-800 rounded shadow-md">
          Reconnecting to notification service...
        </div>
      )}
    </>
  );
};

export default NotificationsDisplay;
