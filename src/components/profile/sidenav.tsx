"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/utils/tailwind";

const ProfileSidenav = () => {
  const pathname = usePathname();

  return (
    <aside className="hidden md:flex max-w-[150] w-full flex-col gap-10">
      <h3 className="font-semibold text-lg text-black px-4">Profile Account</h3>
      <nav className="flex flex-col gap-1">
        <Link href="/profile">
          <p
            className={cn(
              "font-base font-medium text-black px-4 py-2.5 rounded-[40px]",
              {
                "border border-secondary bg-card-foreground":
                  pathname === "/profile",
              },
            )}
          >
            Profile Information
          </p>
        </Link>

        <Link href="/profile/documents">
          <p
            className={cn(
              "font-base font-medium text-black px-4 py-2.5 rounded-[40px]",
              {
                "border border-secondary bg-card-foreground":
                  pathname?.includes("documents"),
              },
            )}
          >
            My documents
          </p>
        </Link>

        <Link href="/profile/history">
          <p
            className={cn(
              "font-base font-medium text-black px-4 py-2.5 rounded-[40px]",
              {
                "border border-secondary bg-card-foreground":
                  pathname?.includes("history"),
              },
            )}
          >
            Active Companies
          </p>
        </Link>

        {/*<Link href="/profile/settings">*/}
        {/*  <p*/}
        {/*    className={cn(*/}
        {/*      "font-base font-medium text-black px-4 py-2.5 rounded-[40px]",*/}
        {/*      {*/}
        {/*        "border border-secondary bg-card-foreground":*/}
        {/*          pathname?.includes("settings"),*/}
        {/*      },*/}
        {/*    )}*/}
        {/*  >*/}
        {/*    Settings*/}
        {/*  </p>*/}
        {/*</Link>*/}
      </nav>
    </aside>
  );
};
export default ProfileSidenav;
