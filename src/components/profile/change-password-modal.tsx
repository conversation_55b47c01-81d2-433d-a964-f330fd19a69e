"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChangePasswordSchema } from "../../schemas";
import { Loader, X } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { cn } from "@/utils/tailwind";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { Input } from "@/components/controls/input";
import { useChangePassword } from "@/react-query/auth-hooks";
import { useState } from "react";

interface ChangePasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const ChangePasswordModal = ({
  isOpen,
  onClose,
  className,
}: ChangePasswordModalProps) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const form = useForm<z.infer<typeof ChangePasswordSchema>>({
    resolver: zodResolver(ChangePasswordSchema),
    defaultValues: {
      password_current: "",
      password_new: "",
      password_confirm: "",
    },
  });

  const { mutate: changePassword, status } = useChangePassword({
    mutationOptions: {
      onSuccess: (data) => {
        console.log("SUCCESS CALLBACK TRIGGERED:", data);
        setIsSubmitted(true);
        // Auto close after 2 seconds
        setTimeout(() => {
          onClose();
          setIsSubmitted(false);
          form.reset();
        }, 2000);
      },
      onError: (error: any) => {
        console.log("ERROR CALLBACK TRIGGERED:", error);
        console.error("Change password error:", error);
        
        form.setError("root", {
          type: "manual",
          message: error?.message || "An error occurred while changing password.",
        });
      },
    },
  });

  const isLoading = status === "pending";

  function onSubmit(data: z.infer<typeof ChangePasswordSchema>) {
    // Clear any previous errors
    form.clearErrors();
    
    console.log("SUBMITTING FORM, MUTATION STATUS:", status);
    
    // Call the API with the form data
    changePassword(data);
  }

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setIsSubmitted(false);
      form.reset();
    }
  };

  if (!isOpen) return null;

  // Success state - show confirmation message
  if (isSubmitted) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
        <div className={cn("relative bg-white w-[28.25rem] rounded-[1.25rem] p-6 border border-[#C8E2CE] shadow-lg", className)}>
          <div className="text-center space-y-4">
            {/* Success Badge */}
            <div className="bg-[#D1FAE5] px-3 py-1.5 rounded-full flex items-center justify-center gap-1.5 text-sm text-green-600 mx-auto w-fit">
              <span className="font-medium">Success!</span>
            </div>

            {/* Success Message */}
            <h2 className="text-lg font-medium text-black">
              Your password has been updated successfully.
            </h2>

            {/* Confirm Button */}
            <button
              onClick={handleClose}
              className="w-[15.5rem] h-[3.5rem] bg-primary rounded-[2.5rem] font-poppins font-semibold text-base text-white hover:bg-primary/90 focus:ring-2 focus:ring-offset-2 focus:ring-primary focus:outline-none mx-auto"
            >
              BACK
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Form state - show the change password form
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      <div className={cn("relative w-full max-w-md mx-auto bg-white rounded-[1.25rem] border border-[#C8E2CE] shadow-lg p-6", className)}>
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute left-6 top-6 rounded-full p-1 bg-gray-100 hover:bg-gray-200 focus:ring-2 focus:ring-primary focus:outline-none"
          disabled={isLoading}
        >
          <X className="w-5 h-5 text-black" />
        </button>

        {/* Title */}
        <div className="text-center">
          <h2 className="text-lg font-semibold text-gray-800 mb-6 mt-4">
            Change Password
          </h2>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col space-y-4"
          >
            {/* Display root error message */}
            {form.formState.errors.root && (
              <div className="bg-red-50 border border-red-300 rounded-lg p-4 mb-2">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <X className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800">
                      {form.formState.errors.root.message}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Current Password Field */}
            <FormField
              control={form.control}
              name="password_current"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    Current Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* New Password Field */}
            <FormField
              control={form.control}
              name="password_new"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    New Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* Confirm New Password Field */}
            <FormField
              control={form.control}
              name="password_confirm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    Confirm New Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* Buttons */}
            <div className="flex justify-between items-center gap-4 pt-4">
              {/* Cancel Button */}
              <button
                type="button"
                className="w-full h-[3.5rem] bg-white rounded-[2.5rem] font-medium text-gray-800 border border-gray-300 hover:bg-gray-100 focus:ring-2 focus:ring-gray-400 focus:outline-none"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancel
              </button>
              
              {/* Change Password Button */}
              <button
                type="submit"
                className={cn(
                  "w-full h-[3.5rem] bg-primary rounded-[2.5rem] font-medium text-white hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:outline-none",
                  isLoading ? "cursor-not-allowed" : ""
                )}
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Changing...
                  </div>
                ) : (
                  "Change Password"
                )}
              </button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}; 