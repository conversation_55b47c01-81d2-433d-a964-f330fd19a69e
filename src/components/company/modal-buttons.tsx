import React from "react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useGetUserProfile } from "@/react-query/auth-hooks";

interface ButtonsSectionProps {
  isGreyedOut: boolean;
  onUpgradeClick: () => void;
  onShareClick: () => void;
  isCompanyInHistory: boolean;
  handleRevoke: () => void;
}

export const ButtonsSection: React.FC<ButtonsSectionProps> = ({
  isGreyedOut,
  onUpgradeClick,
  onShareClick,
  isCompanyInHistory,
  handleRevoke,
}) => {
  const router = useRouter();
  const data = useGetUserProfile();

  return (
    <div className="flex flex-col gap-4 md:flex-row justify-between pb-5">
      <Button
        variant="secondary"
        className="w-full md:w-[220px]"
        onClick={() => router.push("/categories")} // Back handler
      >
        BACK
      </Button>
      {isGreyedOut ? (
        <Button
          variant="default"
          className="w-full md:w-[220px]"
          onClick={onUpgradeClick}
        >
          {data.data?.registrationtype === "individual"
            ? "UPGRADE KYC"
            : "UPGRADE KYB"}
        </Button>
      ) : isCompanyInHistory ? (
        <Button
          className="w-full md:w-[220px] border border-red-500 text-red-500 bg-transparent hover:bg-transparent text-red-500 border border-red-500"
          onClick={handleRevoke}
        >
          REVOKE CONSENT
        </Button>
      ) : (
        <Button className="w-full md:w-[220px]" onClick={onShareClick}>
          APPLY TO ONBOARD
        </Button>
      )}
    </div>
  );
};
