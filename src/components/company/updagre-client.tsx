"use client";

import SumSubIframe from "@/components/sumsub-iframe";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { getEnumValue } from "@/types/user";
import { getNextTier } from "@/utils/utils";
import { useRouter, usePathname } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import { APP_QUERY_KEYS } from "@/react-query/query-keys";
import { Loader } from "lucide-react";

interface UpgradeClientProps {
  token: string;
  email: string;
  className?: string;
  onClose?: () => void;
}

export const UpgradeClient = ({
  token,
  email,
  className,
  onClose,
}: UpgradeClientProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { refetch, data, isPending } = useGetUserProfile();

  const level = data?.state || "";
  const nextLevel = getNextTier(level);
  const tierEnum = getEnumValue(nextLevel!);

  if (isPending) {
    return (
      <div className={`flex items-center justify-center w-full h-full ${className || ''}`}>
        <Loader className="animate-spin" />
      </div>
    );
  }

  // if (!tierEnum) {
  //   return <div>No tier enum found</div>;
  // }

  return (
    <SumSubIframe
      className={className}
      token={token}
      email={email!}
      sumsubLevel={tierEnum!}
      onApplicantReviewComplete={async () => {
        // Wait 2 seconds for server to update
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Force invalidate the profile query
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.GET_PROFILE] });
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS] });
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS_BY_TIER] });
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.DOCUMENT_HISTORY] });
        // Reset the query to ensure fresh data
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.GET_PROFILE] });
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS] });
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS_BY_TIER] });
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.DOCUMENT_HISTORY] });
        // Wait for refetch to complete
        await refetch();
        
        // Only navigate to categories if current route doesn't include "/company"
        if (!pathname.includes("/company")) {
          router.push("/categories");
        } else {
          // If route includes "/company", close the modal
          onClose?.();
        }
      }}
    />
  );
};
