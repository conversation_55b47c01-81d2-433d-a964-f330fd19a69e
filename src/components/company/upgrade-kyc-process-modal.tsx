import { FormCard } from "@/components/register/form-card";
import { getSumSubToken } from "@/react-query/query/sumsub-token";
import { getUserProfile } from "@/react-query/query/user";
import { getEnumValue } from "@/types/user";
import { getNextTier } from "@/utils/utils";
import { X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { UpgradeClient } from "./updagre-client";
import { useGetUserProfile } from "@/react-query/auth-hooks";

interface KycProcessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const KycProcessModal: React.FC<KycProcessModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [token, setToken] = useState<string | null>(null);
  const [email, setEmail] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const data = useGetUserProfile();

  const isCompanyUser = data?.data?.registrationtype === "company";

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null); // Clear any previous errors
      try {
        const userData = await getUserProfile();
        const level = userData?.state || "";
        const nextLevel = getNextTier(level);
        const tierEnum = getEnumValue(nextLevel!);

        const token = await getSumSubToken(tierEnum!);
        if (token) {
          setToken(token);
          setEmail(userData?.email);
        } else {
          setError("No token found");
        }
      } catch (error) {
        setError("An error occurred while fetching data" + error);
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchData();
      // Disable body scrolling when modal is open
      document.body.style.overflow = "hidden";
    } else {
      // Re-enable body scrolling when modal is closed
      document.body.style.overflow = "";
    }

    // Cleanup body style on unmount
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      {/* Modal Container */}
      <div className="relative w-[90%] max-w-[32rem] bg-white rounded-[1.25rem] border border-[#C8E2CE] shadow-[1.375rem_2.5rem_2.5rem_rgba(0,0,0,0.05)] p-6 flex flex-col items-center max-h-[90vh] overflow-hidden">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute left-6 top-6 rounded-full p-1 bg-gray-100 hover:bg-gray-200 shadow-md focus:ring-2 focus:ring-primary focus:outline-none"
        >
          <X className="w-5 h-5 text-black" />
        </button>

        {/* Title */}
        <div className="mt-4 text-xl font-poppins font-medium text-black">
          {isCompanyUser ? "Upgrade KYB Process" : "Upgrade KYC Process"}
        </div>

        {/* Scrollable Content Section */}
        <div className="flex-1 w-full overflow-y-hidden scrollbar-none">
          {isLoading ? (
            <p className="text-primary text-base text-center">Loading...</p>
          ) : error ? (
            <p className="text-red-500 text-base text-center">{error}</p>
          ) : (
            <FormCard className="p-5 w-full border-none shadow-none">
              <UpgradeClient
                token={token!}
                email={email!}
                className={"h-auto max-h-[110dvh] overflow-y-auto"}
                onClose={onClose}
              />
            </FormCard>
          )}
        </div>
      </div>
    </div>
  );
};

export default KycProcessModal;
