import type React from "react";
import KycUpgradeCard from "@/components/sections/kyc-upgrade-card";
import type { SUMSUB_LEVEL } from "@/types/user";

interface UpgradeKycModalProps {
  isOpen: boolean;
  companyName: string;
  companyLogo?: string | null;
  tierNumber: SUMSUB_LEVEL;
  onClose: () => void;
  onApplyKyc: () => void;
}

export const UpgradeKycModal: React.FC<UpgradeKycModalProps> = ({
  isOpen,
  companyName,
  companyLogo,
  tierNumber,
  onClose,
  onApplyKyc,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      <div className="relative">
        <KycUpgradeCard
          companyName={companyName}
          companyLogo={companyLogo}
          tierNumber={tierNumber}
          onBack={onClose}
          onApplyKyc={onApplyKyc}
        />
      </div>
    </div>
  );
};
