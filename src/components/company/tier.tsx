import React from "react";
import { Lock } from "lucide-react";
import { SUMSUB_LEVEL } from "@/types/user";

// Map tier numbers to tier names
export const TIER_NAMES: Record<number, string> = {
  0: "Entry",
  1: "Basic",
  2: "Standard",
  3: "Pro",
};

interface CompanyTierProps {
  companyTier: SUMSUB_LEVEL;
  userTier: number;
}

export const CompanyTier: React.FC<CompanyTierProps> = ({
  companyTier,
  userTier,
}) => {
  // Convert companyTier to number to use as index
  const tierNumber = Number(companyTier);
  
  return (
    <div className="flex items-center gap-2 md:gap-4 flex-shrink-0">
      <div className="px-2 py-1 md:px-4 md:py-1.5 border border-primary rounded-[100px]">
        <span className="text-sm md:text-xl text-primary whitespace-nowrap">
          {TIER_NAMES[tierNumber] || `Tier #${companyTier}`}
        </span>
      </div>
      {+companyTier > userTier && (
        <Lock className="w-4 h-4 md:w-6 md:h-6 text-primary mr-2.5" />
      )}
    </div>
  );
};
