import React, { useState } from "react";
import SuccessCard from "@/components/sections/success-modal";
import type { Company } from "@/types/company";
import { useCreateConsentAccount } from "@/react-query/create-consent-hook";
import { useGetUserProfile } from "@/react-query/auth-hooks";

interface ShareKycModalProps {
  isOpen: boolean;
  companyData: Company | null;
  onClose: () => void;
}

export const ShareKycModal: React.FC<ShareKycModalProps> = ({
  isOpen,
  companyData,
  onClose,
}) => {
  const [step, setStep] = useState<"confirmation" | "success" | "error">(
    "confirmation",
  );
  const { mutate, isPending, error } = useCreateConsentAccount();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const data = useGetUserProfile();

  // Function to handle modal close and reset state
  const handleClose = () => {
    setStep("confirmation"); // Reset step to the confirmation step
    setErrorMessage(null); // Clear any error message
    onClose(); // Call parent onClose handler
  };

  // Function to handle the "Confirm" action
  const handleConfirm = () => {
    if (!companyData?.company_id) {
      setErrorMessage("Invalid company data. Please try again.");
      setStep("error");
      return;
    }

    setErrorMessage(null); // Reset error message on retry

    // Use the mutation provided by the hook
    mutate(
      {
        companyId: companyData?.company_id,
        userId: data.data?.user_id,
        applicantId: data.data?.applicant_id,
      },
      {
        onSuccess: () => {
          setStep("success"); // Move to success step on success
        },
        onError: (mutationError: any) => {
          setErrorMessage(mutationError?.message || "Something went wrong!");
          setStep("error"); // Move to error step
        },
      },
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      <div className="relative w-full max-w-md mx-auto bg-white rounded-[1.25rem] border border-[#C8E2CE] shadow-lg p-6">
        {step === "confirmation" && (
          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              Are you sure you want to apply to onboard with{" "}
              <strong>{companyData?.name}</strong>?
            </h2>
            <p className="text-sm text-gray-600 mb-6">
              By confirming, you are giving consent to share your personal
              identity information with <strong>{companyData?.name}</strong>.
              You can revoke this consent at any time in your user profile page.
            </p>
            <div className="flex justify-between items-center gap-4">
              {/* Cancel Button */}
              <button
                className="w-full h-[3.5rem] bg-white rounded-[2.5rem] font-medium text-gray-800 border border-gray-300 hover:bg-gray-100 focus:ring-2 focus:ring-gray-400 focus:outline-none"
                onClick={handleClose}
                disabled={isPending} // Disable button during loading
              >
                Cancel
              </button>
              {/* Confirm Button */}
              <button
                className={`w-full h-[3.5rem] bg-primary rounded-[2.5rem] font-medium text-white hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:outline-none ${
                  isPending ? "cursor-not-allowed" : ""
                }`}
                onClick={handleConfirm}
                disabled={isPending} // Disable button during loading
              >
                {isPending ? "Submitting..." : "Confirm"}
              </button>
            </div>
          </div>
        )}

        {step === "success" && (
          <SuccessCard
            companyName={companyData?.name}
            onClose={handleClose}
            isOpen={isOpen}
          />
        )}

        {step === "error" && (
          <div className="text-center">
            <h2 className="text-lg font-semibold text-red-600 mb-4">Error</h2>
            <p className="text-sm text-gray-600 mb-6">
              {errorMessage ||
                error?.message ||
                "An unexpected error occurred."}
            </p>
            <div className="flex justify-center">
              <button
                className="w-full h-[3.5rem] bg-white rounded-[2.5rem] font-medium text-gray-800 border border-gray-300 hover:bg-gray-100 focus:ring-2 focus:ring-gray-400 focus:outline-none"
                onClick={handleClose}
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
