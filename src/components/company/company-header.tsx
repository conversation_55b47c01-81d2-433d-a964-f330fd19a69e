import React from "react";
import { CompanyTier } from "@/components/company/tier";
import { SUMSUB_LEVEL } from "@/types/user";

interface CompanyHeaderProps {
  logo: string | null;
  name: string;
  description: string;
  tier: SUMSUB_LEVEL;
}

export const CompanyHeader: React.FC<CompanyHeaderProps> = ({
  logo,
  name,
  description,
  tier,
}) => {
  return (
    <div className="flex flex-col md:flex-row items-start md:items-start gap-4 md:gap-16 mb-6 relative">
      {/* Logo */}
      <div className="w-16 h-16 md:w-36 md:h-36 bg-white rounded-lg p-2 flex-shrink-0">
        <img
          src={logo!} // Fallback to a placeholder if the logo is null
          alt={`${name} logo`} // Accessible alt text
          className="w-full h-full object-contain"
          onError={(e) => {
            e.currentTarget.src = "/placeholder.svg"; // Fallback on error
          }}
        />
      </div>

      {/* Title and Subtitle */}
      <div className="flex-1">
        <h1 className="text-[28px] leading-[32px] md:text-3xl font-medium text-black">
          {name}
        </h1>
        {/* Description */}
        <p className="text-base md:text-lg text-default mt-6">{description}</p>
      </div>

      {/* Tier Badge */}
      <div className="flex-shrink-0 absolute top-0 right-0 md:relative">
        <CompanyTier companyTier={tier} userTier={100} />
      </div>
    </div>
  );
};
