import React from "react";

interface RevokeConsentModalProps {
  isOpen: boolean;
  companyName: string;
  fullName: string;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export const RevokeConsentModal: React.FC<RevokeConsentModalProps> = ({
  isOpen,
  companyName,
  fullName,
  onClose,
  onConfirm,
  isLoading = false,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm">
      <div className="relative w-full max-w-md mx-auto bg-white rounded-[1.25rem] border border-red-500 shadow-lg p-6">
        <div className="text-center">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">
            Revoke Consent
          </h2>
          <p className="text-sm text-gray-600 mb-6">
            You have previously expressed desire to onboard with{" "}
            <strong>{companyName}</strong>, and <strong>{companyName}</strong>{" "}
            has active consent to get your full KYC application and contact you.
            By revoking the consent, <strong>{companyName}</strong> will not be
            able to access your KYC application and will stop the process of
            onboarding <strong>{fullName}</strong> as their client. Please
            confirm by clicking Revoke button.
          </p>
          <div className="flex justify-between items-center gap-4">
            {/* Cancel Button */}
            <button
              className="w-full h-[3.5rem] bg-white rounded-[2.5rem] font-medium text-gray-800 border border-gray-300 hover:bg-gray-100 focus:ring-2 focus:ring-gray-400 focus:outline-none"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </button>
            {/* Revoke Button */}
            <button
              className={`w-full h-[3.5rem] bg-red-500 rounded-[2.5rem] font-medium text-white hover:bg-red-600 focus:ring-2 focus:ring-red-400 focus:outline-none ${
                isLoading ? "cursor-not-allowed" : ""
              }`}
              onClick={onConfirm}
              disabled={isLoading}
            >
              {isLoading ? "Processing..." : "Revoke"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
