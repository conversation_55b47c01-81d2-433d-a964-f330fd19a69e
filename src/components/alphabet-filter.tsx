import React from "react";

interface AlphabetFilterProps {
  companies: any[];
  activeLetter?: string;
  onLetterClick: (letter: string | undefined) => void;
}

export function AlphabetFilter({
  companies,
  activeLetter,
  onLetterClick,
}: AlphabetFilterProps) {
  const alphabet = "ABCDEFGHIJKLMNOP".split("");

  const availableLetters = React.useMemo(() => {
    return new Set(companies.map((company) => company.name[0].toUpperCase()));
  }, [companies]);

  return (
    <div className="flex flex-col items-center space-y-1">
      {alphabet.map((letter, index) => (
        <div key={letter} className="relative">
          <button
            className={`font-['Poppins'] font-semibold text-xs leading-[26px] tracking-[0.95em] ${
              availableLetters.has(letter)
                ? activeLetter === letter
                  ? "text-[#1C3F3C]"
                  : "text-[#231F20] hover:text-[#1C3F3C]"
                : "text-[#888E92] cursor-not-allowed"
            } transition-colors`}
            onClick={() =>
              availableLetters.has(letter) && onLetterClick(letter)
            }
            disabled={!availableLetters.has(letter)}
          >
            {letter}
          </button>
          {index === 0 && (
            <div className="absolute -left-5 top-0 w-[30px] h-[30px] border border-[#1C3F3C] rounded-full" />
          )}
        </div>
      ))}
    </div>
  );
}
