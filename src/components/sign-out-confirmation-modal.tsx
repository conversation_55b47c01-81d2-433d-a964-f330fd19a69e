"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface SignOutConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function SignOutConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
}: SignOutConfirmationModalProps) {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed left-[50%] top-[50%] w-[90vw] max-w-[452px] h-auto max-h-[90vh] md:w-[452px] translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white shadow-[22px_40px_40px_0px_rgba(0,0,0,0.05)] border border-[#C8E2CE] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          
          {/* Close Button */}
          <div className="absolute top-[23px] left-[27px]">
            <Dialog.Close asChild>
              <button
                className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close"
                disabled={isLoading}
              >
                <X className="h-5 w-5 text-black stroke-2" />
              </button>
            </Dialog.Close>
          </div>

          {/* Icon */}
          <div className="flex justify-center mt-[56px] mb-[22px] px-4">
            <div className="w-16 h-16 flex items-center justify-center bg-red-100 rounded-full">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                />
              </svg>
            </div>
          </div>

          {/* Confirmation Message */}
          <div className="px-6 md:px-[70px] mb-[40px]">
            <p className="text-[#1C3F3C] text-[18px] font-medium leading-[22px] text-center tracking-[0.02em] font-poppins">
              Are you sure you want to leave?
            </p>
          </div>

          {/* Action Buttons */}
          <div className="px-6 md:px-[102px] space-y-[15px] pb-[40px]">
            {/* YES Button */}
            <Button
              onClick={onConfirm}
              disabled={isLoading}
              className="w-full h-[56px] bg-red-600 text-white font-semibold text-[16px] leading-[24px] rounded-[40px] border border-red-700 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed font-poppins"
            >
              {isLoading ? "Signing out..." : "YES"}
            </Button>

            {/* NO Button */}
            <Button
              onClick={onClose}
              disabled={isLoading}
              variant="secondary"
              className="w-full h-[56px] bg-white text-[#1C3F3C] font-medium text-[16px] leading-[32px] rounded-[40px] shadow-[0px_6px_8.3px_4px_rgba(28,63,60,0.2)] hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed font-poppins"
            >
              NO
            </Button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
} 