"use client";

import { Search } from "lucide-react";
import { cn } from "@/utils/tailwind";

interface SearchInputProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function SearchInput({
  placeholder = "Search",
  value,
  onChange,
  className,
}: SearchInputProps) {
  return (
    <div className="relative">
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className={cn(
          "w-full h-10 pl-4 pr-10 rounded-full focus:outline-none placeholder:text-[#6B7280]",
          className,
        )}
      />
      <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-[#6B7280]" />
    </div>
  );
}
