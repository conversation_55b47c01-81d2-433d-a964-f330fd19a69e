"use client";

import * as React from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useRouter } from "next/navigation";
import UserIcon from "@/assets/icons/UserIcon";
import { SignOutConfirmationModal } from "@/components/sign-out-confirmation-modal";

interface UserDropdownProps {
  kycLevel: number;
  className?: string;
}

export function UserDropdown({ kycLevel, className }: UserDropdownProps) {
  const router = useRouter();
  const [isSignOutModalOpen, setIsSignOutModalOpen] = React.useState(false);
  const [isSigningOut, setIsSigningOut] = React.useState(false);

  const handleSignOutClick = () => {
    setIsSignOutModalOpen(true);
  };

  const handleSignOutConfirm = () => {
    setIsSigningOut(true);
    // Navigate to logout page which handles the actual logout process
    router.push("/logout");
  };

  const handleSignOutCancel = () => {
    setIsSignOutModalOpen(false);
    setIsSigningOut(false);
  };

  return (
    <>
      <DropdownMenu.Root>
        <DropdownMenu.Trigger asChild>
          <UserIcon className="cursor-pointer" />
        </DropdownMenu.Trigger>

        <DropdownMenu.Portal>
          <DropdownMenu.Content
            className="z-50 min-w-[180px] overflow-hidden rounded-xl bg-white p-1 shadow-lg"
            align="end"
            sideOffset={8}
          >
            <DropdownMenu.Item
              className="relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2 text-sm text-[#231F20] outline-none hover:bg-[#F3F4F6]"
              onClick={() => router.push("/profile")}
            >
              Profile
            </DropdownMenu.Item>
            {/***<DropdownMenu.Item className="relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2 text-sm text-[#231F20] outline-none hover:bg-[#F3F4F6]">*/}
            {/*  Security*/}
            {/*</DropdownMenu.Item>*/}
            {/*<DropdownMenu.Separator className="my-1 h-px bg-[#E5E7EB]" />*/}
            {/*<DropdownMenu.Item className="relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2 text-sm text-[#231F20] outline-none hover:bg-[#F3F4F6]">*/}
            {/*  Help Center*/}
            {/*</DropdownMenu.Item>*/}
            <DropdownMenu.Separator className="my-1 h-px bg-[#E5E7EB]" />
            <DropdownMenu.Item
              onClick={handleSignOutClick}
              className="relative flex cursor-pointer select-none items-center rounded-lg px-3 py-2 text-sm text-red-600 outline-none hover:bg-red-50"
            >
              Sign Out
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>

      {/* Sign Out Confirmation Modal */}
      <SignOutConfirmationModal
        isOpen={isSignOutModalOpen}
        onClose={handleSignOutCancel}
        onConfirm={handleSignOutConfirm}
        isLoading={isSigningOut}
      />
    </>
  );
}
