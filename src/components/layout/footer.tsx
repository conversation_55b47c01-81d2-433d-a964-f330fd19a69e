import Link from "next/link";
import { cn } from "@/utils/tailwind";

const navigation = {
  legal: [
    { name: "Privacy Policy", href: "#" },
    { name: "Terms and conditions", href: "#" },
    { name: "Contact", href: "#" },
  ],
  social: [
    {
      name: "Facebook",
      href: "#",
      icon: (props) => (
        <svg
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          {...props}
        >
          <path
            d="M19.5 3H4.5C3.67157 3 3 3.67157 3 4.5V19.5C3 20.3284 3.67157 21 4.5 21H19.5C20.3284 21 21 20.3284 21 19.5V4.5C21 3.67157 20.3284 3 19.5 3Z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M12 21V12M12 12V8.25C12 7.00736 13.0074 6 14.25 6H15M12 12H9"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
    },
    {
      name: "Twitter",
      href: "#",
      icon: (props) => (
        <svg
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          {...props}
        >
          <path
            d="M22 4.01C21 4.5 20.02 4.69 19 4.82C20.07 4.19 20.85 3.21 21.23 2.03C20.24 2.61 19.15 3.01 18 3.22C17.06 2.24 15.84 1.64 14.5 1.64C11.91 1.64 9.82 3.74 9.82 6.32C9.82 6.71 9.85 7.09 9.93 7.45C6.08 7.25 2.63 5.37 0.33 2.49C-0.04 3.23 -0.23 4.08 -0.23 4.98C-0.23 6.69 0.67 8.19 2.07 9.08C1.21 9.06 0.4 8.83 -0.33 8.43C-0.33 8.45 -0.33 8.47 -0.33 8.49C-0.33 10.75 1.25 12.64 3.33 13.09C2.97 13.19 2.59 13.24 2.2 13.24C1.93 13.24 1.66 13.22 1.41 13.17C1.95 15.03 3.69 16.39 5.77 16.43C4.14 17.69 2.13 18.42 -0.01 18.42C-0.36 18.42 -0.71 18.4 -1.05 18.37C1.05 19.7 3.47 20.46 6.07 20.46C14.49 20.46 19.11 13.31 19.11 7.11C19.11 6.9 19.11 6.69 19.1 6.48C20.11 5.77 20.97 4.88 21.64 3.86L22 4.01Z"
            fill="currentColor"
          />
        </svg>
      ),
    },
    {
      name: "Instagram",
      href: "#",
      icon: (props) => (
        <svg
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          {...props}
        >
          <path
            d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16Z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M3 16V8C3 5.23858 5.23858 3 8 3H16C18.7614 3 21 5.23858 21 8V16C21 18.7614 18.7614 21 16 21H8C5.23858 21 3 18.7614 3 16Z"
            stroke="currentColor"
            strokeWidth="2"
          />
          <path
            d="M17.5 6.51L17.51 6.49889"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
    },
    {
      name: "LinkedIn",
      href: "#",
      icon: (props) => (
        <svg
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          {...props}
        >
          <path
            d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M6 9H2V21H6V9Z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4 6C5.10457 6 6 5.10457 6 4C6 2.89543 5.10457 2 4 2C2.89543 2 2 2.89543 2 4C2 5.10457 2.89543 6 4 6Z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
    },
  ],
};

interface FooterProps {
  className?: string;
}

export function Footer({ className }: FooterProps) {
  return (
    <footer>
      <div className={cn("mx-auto py-6 px-4 sm:px-6 lg:px-8", className)}>
        <div className="flex flex-col items-center sm:flex-row sm:justify-between gap-6 sm:gap-0">
          <div className="flex flex-col items-center sm:items-start gap-3 text-center sm:text-left">
            <p className="text-[14px] font-semibold text-[#1C3F3C]">
              Copyright © 2025 trustnexus
            </p>
            <nav className="flex gap-4" aria-label="Footer">
              {navigation.legal.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-[14px] font-medium text-[#888E92] hover:text-[#1C3F3C]"
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
          <div className="flex gap-5 sm:gap-[30px] justify-center">
            {navigation.social.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-[#888E92] hover:text-[#1C3F3C]"
              >
                <span className="sr-only">{item.name}</span>
                <item.icon className="h-6 w-6" aria-hidden="true" />
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}
