import Image from "next/image";
import largeDesktop from "../../../public/large_background_graphics.png";
import smallDesktop from "../../../public/small_background_graphics.png";
import largeMobile from "../../../public/mobile_large_background_graphics.png";
import smallMobile from "../../../public/mobile_small_background_graphics.png";

export function Background() {
  return (
    <div className="absolute inset-0 pointer-events-none -z-10 bg-white">
      {/* Images container - absolute position with right alignment */}
      <div className="absolute top-0 right-0 w-full flex flex-col items-end">
        {/* Small Image - Desktop */}
        <div className="hidden sm:block w-full ">
          <Image src={smallDesktop || "/placeholder.svg"} alt="" priority />
        </div>

        {/* Small Image - Mobile */}
        <div className="block sm:hidden w-full max-w-[400px]">
          <Image
            src={smallMobile || "/placeholder.svg"}
            alt=""
            priority
            className="w-full h-auto object-contain object-right"
          />
        </div>

        {/* Large Image - Desktop */}
        <div className="hidden sm:block w-full max-w-[800px]">
          <Image
            src={largeDesktop || "/placeholder.svg"}
            alt=""
            priority
            className="w-full h-auto object-contain object-right"
          />
        </div>

        {/* Large Image - Mobile */}
        <div className="block sm:hidden w-full max-w-[400px]">
          <Image
            src={largeMobile || "/placeholder.svg"}
            alt=""
            priority
            className="w-full h-auto object-contain object-right"
          />
        </div>
      </div>
    </div>
  );
}
