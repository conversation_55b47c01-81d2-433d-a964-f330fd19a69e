"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { Menu, Search, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Logo } from "@/components/logo";
import { cn } from "@/utils/tailwind";
import { PUBLIC_LINKS } from "@/data/content";
import { usePathname } from "next/navigation";

interface NavbarProps {
  showNavItems: boolean;
  className?: string;
}

export function Navbar({ showNavItems, className }: NavbarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  // Dodajte useEffect da kontroliše overflow na body elementu
  useEffect(() => {
    if (isOpen) {
      // Zaključaj skrolovanje kada je meni otvoren
      document.body.style.overflow = "hidden";
    } else {
      // Vrati normalno skrolovanje kada je meni zatvoren
      document.body.style.overflow = "auto";
    }

    // Cleanup funkcija koja se izvr<PERSON>va kada se komponenta unmount-uje
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]); // Zavisnost samo od isOpen stanja

  console.log("PATHNAME", pathname);

  return (
    <nav className={cn("py-3 lg:py-4 px-4 md:px-6", className)}>
      <div className="container">
        <div className="flex justify-between items-center">
          <Link href="/">
            <Logo className="w-[203px] h-[29px]" />
          </Link>

          {showNavItems && (
            <div className="hidden md:flex items-center gap-6">
              {PUBLIC_LINKS.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "font-poppins font-normal text-base leading-[26px] text-[#1C3F3C] hover:text-[#152e2a]",
                    item.href === pathname &&
                      "bg-primary text-white hover:text-white px-4 py-1 rounded-full"
                  )}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          )}

          <div className="hidden md:flex items-center gap-6">
            {/* <button className="text-[#1C3F3C]">
              <Search className="w-[30px] h-[30px]" />
            </button> */}
            <Link href="/login">
              <Button className="bg-[#1C3F3C] hover:bg-[#152e2a] text-white rounded-full px-4 py-1 h-[30px] text-base font-normal">
                Login
              </Button>
            </Link>
          </div>
          <div className="md:hidden relative z-[101]">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-[#1C3F3C] relative z-[101]"
            >
              {isOpen ? (
                <X className="w-6 h-6" aria-hidden="true" />
              ) : (
                <Menu className="w-6 h-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="md:hidden fixed top-0 bottom-0 h-screen left-0 w-full bg-white shadow-md z-[9999]">
          <div className="flex justify-end pt-2 pr-2 relative">
            <Button
              onClick={() => setIsOpen(!isOpen)}
              size="icon"
              className="px-2 min-w-0 absolute top-2 right-2 z-[10000]"
            >
              <X className="w-6 h-6" aria-hidden="true" />
            </Button>
          </div>
          <div className="px-4 pt-2 pb-3 gap-2 h-full flex flex-col justify-between">
            <div>
              {PUBLIC_LINKS.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="block px-3 py-2 text-[#1C3F3C] hover:bg-gray-50 w-full text-[32px]"
                  onClick={() => setIsOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
            </div>
            <div className="pt-4 pb-3 border-t border-gray-200 mt-auto">
              <Button
                className="w-full bg-[#1C3F3C] hover:bg-[#152e2a] text-white rounded-full"
                asChild
                onClick={() => setIsOpen(false)}
              >
                <Link href="/login" className="block px-3 py-2">
                  Login
                </Link>
              </Button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
