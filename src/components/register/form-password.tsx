"use client";

import { HTMLInputTypeAttribute } from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { Input } from "@/components/controls/input";
import { useFormContext } from "react-hook-form";
interface FormInputProps {
  label: string;
  name: string;
  placeholder: string;
  disabled?: boolean;
  inputType?: HTMLInputTypeAttribute | undefined;
  isPasswordConfirmation?: boolean;
  inputClassname?: string;
  required?: boolean;
}
export default function FormPassword({
  label,
  name,
  placeholder,
  disabled,
  inputType = "password",
  inputClassname,
}: FormInputProps) {
  const { trigger, control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="">
          <FormLabel className="text-base text-left font-normal text-primary">
            {label}
            {/*{required && <span className="text-red-500"> *</span>}*/}
          </FormLabel>
          <FormControl>
            <Input
              className={inputClassname}
              type={inputType}
              {...field}
              disabled={disabled}
              placeholder={placeholder}
              onChange={async (e) => {
                field.onChange(e);
                await trigger(name);
              }}
              onBlur={async () => {
                field.onBlur();
                await trigger(name);
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
