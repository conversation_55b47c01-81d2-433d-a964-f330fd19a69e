"use client";

import { Control, RegisterOptions } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { Checkbox } from "@/components/controls/checkbox";

interface FormInputProps {
  label: string;
  name: string;
  placeholder: string;
  control: Control<any>;
  required?: boolean;
  isPasswordConfirmation?: boolean;
}

export default function FormCheckbox({
  label,
  name,
  control,
  required,
}: FormInputProps) {
  const getInputRules = (required: boolean): RegisterOptions<any, string> => {
    return required ? { required: "This field is required" } : {};
  };

  return (
    <FormField
      rules={getInputRules(required || false)}
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
          <FormControl>
            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
          </FormControl>
          <FormLabel className="text-black text-md font-normal text-left">
            {label}
          </FormLabel>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
