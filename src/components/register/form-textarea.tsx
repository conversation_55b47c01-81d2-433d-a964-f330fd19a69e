import { HTMLInputTypeAttribute } from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { Textarea } from "@/components/controls/textarea";

interface FormInputProps {
  label: string;
  name: string;
  placeholder: string;
  control: Control<any>;
  disabled?: boolean;
  inputType?: HTMLInputTypeAttribute | undefined;
}

export default function FormTextarea({
  label,
  name,
  placeholder,
  control,
  disabled,
}: FormInputProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex h-full flex-col">
          <FormLabel className="text-black mb-2 text-lg font-normal leading-5 text-left">
            {label}
          </FormLabel>
          <FormControl>
            <Textarea
              className="text-md font-normal leading-6 text-left bg-white text-black"
              disabled={disabled}
              placeholder={placeholder}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
