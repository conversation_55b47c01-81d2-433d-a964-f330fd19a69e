"use client";

import { Control, RegisterOptions, useFormContext } from "react-hook-form";
import { SampleDatePicker } from "../calendar";
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";

interface IFormDatePicker {
  label: string;
  name: string;
  placeholder: string;
  control: Control<any>;
  disabled?: boolean;
  date: Date;
  required?: boolean;
}

export const FormDatePicker = ({
  date,
  control,
  disabled,
  name,
  placeholder,
  label,
  required,
}: IFormDatePicker) => {
  const { trigger } = useFormContext();

  const isDateOfBirth = name === "date_of_birth";

  const getDateRules = (required: boolean): RegisterOptions<any, string> => {
    const commonRules = required ? { required: "This field is required" } : {};

    if (isDateOfBirth) {
      return {
        ...commonRules,
        validate: (value: Date) => {
          const age = new Date().getFullYear() - new Date(value).getFullYear();
          if (age < 18) {
            return "You must be at least 18 years old";
          }
          return true;
        },
      };
    }

    return commonRules;
  };

  return (
    <FormField
      rules={getDateRules(required || false)}
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel className="font-normal text-black mb-2 text-md">
            {label}
          </FormLabel>
          <SampleDatePicker
            mode="single"
            selected={field.value}
            onSelect={async (d: any) => {
              field.onChange(d);
              await trigger(name);
            }}
            initialFocus
          />

          <FormMessage />
        </FormItem>
      )}
    />
  );
};
