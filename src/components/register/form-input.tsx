"use client";

import { HTMLInputTypeAttribute } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { InputWithPrefix } from "../controls/input-with-prefix";
import { handleNumberKeyDown } from "@/utils/utils";
import { Input } from "@/components/controls/input";

interface FormInputProps {
  label: string;
  name: string;
  placeholder: string;
  disabled?: boolean;
  required?: boolean;
  inputType?: HTMLInputTypeAttribute | undefined;
  isPasswordConfirmation?: boolean;
  prefix?: string;
  inputClassname?: string;
}

export default function FormInput({
  label,
  name,
  placeholder,
  disabled,
  inputType = "text",
  prefix,
  inputClassname,
}: FormInputProps) {
  const { trigger, control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-base text-left font-normal text-primary">
            {label}
            {/*{isRequired && <span className="text-red-500"> *</span>}*/}
          </FormLabel>
          <FormControl>
            {prefix ? (
              <InputWithPrefix
                prefix={prefix}
                type={inputType}
                disabled={disabled}
                placeholder={placeholder}
                value={field.value?.toString()}
                name={field.name}
                onKeyDown={
                  inputType === "number" ? handleNumberKeyDown : undefined
                }
                onChange={(e) => {
                  field.onChange(e);
                  trigger(name);
                }}
                onBlur={() => {
                  field.onBlur();
                  trigger(name);
                }}
              />
            ) : (
              <Input
                className={inputClassname}
                type={inputType}
                disabled={disabled}
                placeholder={placeholder}
                value={field.value?.toString()}
                name={field.name}
                onKeyDown={
                  inputType === "number" ? handleNumberKeyDown : undefined
                }
                onChange={(e) => {
                  field.onChange(e);
                  trigger(name);
                }}
                onBlur={() => {
                  field.onBlur();
                  trigger(name);
                }}
              />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
