"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { ForgetPasswordSchema } from "../../schemas";
import { Lo<PERSON>, ArrowLeft } from "lucide-react";
import { Button, ButtonProps } from "../ui/button";
import { FormCard } from "@/components/register/form-card";
import { cn } from "@/utils/tailwind";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { Input } from "@/components/controls/input";
import { useForgotPassword } from "@/react-query/auth-hooks";
import { useState } from "react";

interface ForgotPasswordFormProps {
  className?: string;
  submitButtonVariant?: ButtonProps["variant"];
  submitButtonClassname?: string;
  inputClassname?: string;
}

export const ForgotPasswordForm = ({
  className,
  submitButtonVariant = "default",
  submitButtonClassname,
  inputClassname,
}: ForgotPasswordFormProps) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const form = useForm<z.infer<typeof ForgetPasswordSchema>>({
    resolver: zodResolver(ForgetPasswordSchema),
    defaultValues: {
      username: "",
    },
  });

  const { mutate: forgotPassword, status } = useForgotPassword({
    mutationOptions: {
      onSuccess: () => {
        setIsSubmitted(true);
      },
      onError: (error) => {
        console.error("Forgot password error:", error);
      },
    },
  });

  const isLoading = status === "pending";

  function onSubmit(data: z.infer<typeof ForgetPasswordSchema>) {
    forgotPassword({
      email: data.username, // Map `username` to `email` for forgot password mutation
    });
  }

  // Success state - show confirmation message
  if (isSubmitted) {
    return (
      <div className={cn("w-full max-w-md mx-auto", className)}>
        <FormCard className={cn("rounded-lg shadow-md p-6 bg-white", className)}>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              Check your email
            </h3>
            <p className="text-sm text-gray-600">
              We've sent a password reset link to{" "}
              <span className="font-medium">{form.getValues("username")}</span>
            </p>
            <p className="text-xs text-gray-500">
              Didn't receive the email? Check your spam folder or try again.
            </p>
            <div className="flex flex-col space-y-3 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setIsSubmitted(false);
                  form.reset();
                }}
                className="w-full"
              >
                Try again
              </Button>
              <Link
                href="/login"
                className="flex items-center justify-center text-primary hover:text-primary-dark hover:underline text-sm"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to login
              </Link>
            </div>
          </div>
        </FormCard>
      </div>
    );
  }

  // Form state - show the forgot password form
  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <FormCard className={cn("rounded-lg shadow-md p-6 bg-white", className)}>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col space-y-6"
          >
            {/* Email Field */}
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    Email
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      type="email"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                        inputClassname,
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* Forgot Password and Register Section */}
            <div className="flex flex-col items-center space-y-4">
              <div className="text-gray-700">
                Remember your password?{" "}
                <Link
                  href="/login"
                  className="text-primary font-medium hover:underline"
                >
                  Login
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-center">
              <Button
                variant={submitButtonVariant}
                type="submit"
                size="md"
                className={cn(
                  "flex items-center justify-center font-semibold text-sm md:text-base bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark transition-all duration-150",
                  submitButtonClassname,
                )}
                disabled={isLoading}
              >
                {isLoading && <Loader className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading ? "Sending..." : "SEND RESET LINK"}
              </Button>
            </div>
          </form>
        </Form>
      </FormCard>
    </div>
  );
}; 