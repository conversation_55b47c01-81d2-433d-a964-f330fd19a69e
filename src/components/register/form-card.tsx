"use client";

import { cn } from "@/utils/tailwind";

interface IFormCard {
  children?: React.ReactNode;
  className?: string;
}

export const FormCard = ({ children, className }: IFormCard) => {
  return (
    <div
      className={cn(
        "overflow-y-auto border border-border w-full max-w-xl bg-card-foreground sm:backdrop-blur-sm rounded-2xl shadow-lg",
        className
      )}
    >
      {children}
    </div>
  );
};
