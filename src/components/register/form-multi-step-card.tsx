"use client";

import React from "react";
import { cn } from "@/utils/tailwind";
import { CardHeader } from "../common/card";
import ArrowLeft from "@/assets/icons/ArrowLeft";
import { useFormContext } from "react-hook-form";

interface IFormCard {
  children?: React.ReactNode;
  className?: string;
  stepName: string;
}

export const FormCardMultiStepCard = ({
  children,
  className,
  stepName,
}: IFormCard) => {
  const { setValue, getValues, trigger } = useFormContext();

  const currentStep = getValues("step");

  const handleOnPrevious = (e: React.MouseEvent) => {
    e.preventDefault();
    if (currentStep > 1) {
      setValue("step", currentStep - 1);
    }
    trigger("step");
  };

  return (
    <div
      className={cn(
        "overflow-y-auto pb-4 w-full max-w-7xl mx-auto rounded-lg max-h-full",
        className
      )}
    >
      <CardHeader className="w-full p-0">
        <div className="flex items-center gap-6 w-full relative">
          <ArrowLeft
            className="w-[24px] cursor-pointer text-primary"
            onClick={(e) => handleOnPrevious(e)}
          />

          <p className="text-2xl text-primary font-semibold">{stepName}</p>
        </div>
      </CardHeader>

      <div className="mt-3 px-3">{children}</div>
    </div>
  );
};
