import { useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/controls/input";

interface StepProps {
  onPrevStep: () => void;
}

export const PersonalInfoStep = ({ onPrevStep }: StepProps) => {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <div className="space-y-4">
      <div>
        <Input {...register("firstName")} placeholder="First Name" />
        {errors.firstName && (
          <p className="text-red-500 text-sm">
            {errors.firstName.message as string}
          </p>
        )}
      </div>

      <div>
        <Input {...register("lastName")} placeholder="Last Name" />
        {errors.lastName && (
          <p className="text-red-500 text-sm">
            {errors.lastName.message as string}
          </p>
        )}
      </div>

      <div className="flex justify-between">
        <Button type="button" onClick={onPrevStep}>
          Back
        </Button>
        <Button type="submit">Next</Button>
      </div>
    </div>
  );
};
