"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { LoginSchema } from "../../schemas";
import { Loader } from "lucide-react";
import { Button, ButtonProps } from "../ui/button";
import { FormCard } from "@/components/register/form-card";
import { cn } from "@/utils/tailwind";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { Input } from "@/components/controls/input";
import { useLogin } from "@/react-query/auth-hooks";

interface LoginFormProps {
  IBLogin?: boolean;
  className?: string;
  loginButtonVariant?: ButtonProps["variant"];
  loginButtonClassname?: string;
  inputClassname?: string;
}

export const LoginForm = ({
  IBLogin,
  className,
  loginButtonVariant = "default",
  loginButtonClassname,
  inputClassname,
}: LoginFormProps) => {
  const form = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const { mutate: login, status } = useLogin({
    mutationOptions: {
      onError: (error) => {
        console.error("Login error:", error);
      },
    },
  });

  const isLoading = status === "pending";

  function onSubmit(data: z.infer<typeof LoginSchema>) {
    login({
      email: data.username, // Map `username` to `email` for login mutation
      password: data.password,
    });
  }

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <FormCard className={cn("rounded-lg shadow-md p-6 bg-white", className)}>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col space-y-6"
          >
            {/* Username/Email Field */}
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    Email
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      type="email"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                        inputClassname,
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* Password Field */}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                        inputClassname,
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* Forgot Password and Register Section */}
            <div className="flex flex-col items-center space-y-4">
              <Link
                href="/forgot-password"
                className="text-primary hover:text-primary-dark hover:underline"
              >
                Forgot Password?
              </Link>
              <div className="text-gray-700">
                Don&#39;t have an account?{" "}
                <Link
                  href={IBLogin ? "/ib/signup" : "/register"}
                  className="text-primary font-medium hover:underline"
                >
                  Register
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-center">
              <Button
                variant={loginButtonVariant}
                type="submit"
                size="md"
                className={cn(
                  "flex items-center justify-center font-semibold text-sm md:text-base bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark transition-all duration-150",
                  loginButtonClassname,
                )}
                disabled={isLoading}
              >
                {isLoading && <Loader className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading ? "Logging in..." : "LOG IN"}
              </Button>
            </div>
          </form>
        </Form>
      </FormCard>
    </div>
  );
};
