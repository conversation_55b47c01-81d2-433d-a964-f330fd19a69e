"use client";

import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { ResetPasswordSchema } from "../../schemas";
import { Lo<PERSON>, ArrowLeft } from "lucide-react";
import { Button, ButtonProps } from "../ui/button";
import { FormCard } from "@/components/register/form-card";
import { cn } from "@/utils/tailwind";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/common/form";
import { Input } from "@/components/controls/input";
import { useResetPassword } from "@/react-query/auth-hooks";
import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";

interface ResetPasswordFormProps {
  className?: string;
  submitButtonVariant?: ButtonProps["variant"];
  submitButtonClassname?: string;
  inputClassname?: string;
}

const ResetPasswordFormContent = ({
  className,
  submitButtonVariant = "default",
  submitButtonClassname,
  inputClassname,
}: ResetPasswordFormProps) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get("token");

  const form = useForm<z.infer<typeof ResetPasswordSchema>>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      token: token || "",
      password: "",
      password_confirm: "",
    },
  });

  // Update token when it changes in URL
  useEffect(() => {
    if (token) {
      form.setValue("token", token);
    }
  }, [token, form]);

  const { mutate: resetPassword, status } = useResetPassword({
    mutationOptions: {
      onSuccess: () => {
        setIsSubmitted(true);
      },
      onError: (error) => {
        console.error("Reset password error:", error);
      },
    },
  });

  const isLoading = status === "pending";

  function onSubmit(data: z.infer<typeof ResetPasswordSchema>) {
    resetPassword(data);
  }

  // Check if token is missing
  if (!token) {
    return (
      <div className={cn("w-full max-w-md mx-auto", className)}>
        <FormCard className={cn("rounded-lg shadow-md p-6 bg-white", className)}>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              Invalid Reset Link
            </h3>
            <p className="text-sm text-gray-600">
              This password reset link is invalid or has expired.
            </p>
            <div className="flex flex-col space-y-3 pt-4">
              <Link
                href="/forgot-password"
                className="w-full"
              >
                <Button variant="default" className="w-full">
                  Request New Reset Link
                </Button>
              </Link>
              <Link
                href="/login"
                className="flex items-center justify-center text-primary hover:text-primary-dark hover:underline text-sm"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to login
              </Link>
            </div>
          </div>
        </FormCard>
      </div>
    );
  }

  // Success state - show confirmation message
  if (isSubmitted) {
    return (
      <div className={cn("w-full max-w-md mx-auto", className)}>
        <FormCard className={cn("rounded-lg shadow-md p-6 bg-white", className)}>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              Password Reset Successful
            </h3>
            <p className="text-sm text-gray-600">
              Your password has been successfully reset. You can now log in with your new password.
            </p>
            <div className="flex flex-col space-y-3 pt-4">
              <Link href="/login" className="w-full">
                <Button variant="default" className="w-full">
                  Go to Login
                </Button>
              </Link>
            </div>
          </div>
        </FormCard>
      </div>
    );
  }

  // Form state - show the reset password form
  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <FormCard className={cn("rounded-lg shadow-md p-6 bg-white", className)}>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col space-y-6"
          >
            {/* Hidden Token Field */}
            <FormField
              control={form.control}
              name="token"
              render={({ field }) => (
                <input type="hidden" {...field} />
              )}
            />

            {/* New Password Field */}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                        inputClassname,
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* Confirm Password Field */}
            <FormField
              control={form.control}
              name="password_confirm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-semibold text-gray-700">
                    Confirm Password
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      className={cn(
                        "w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                        inputClassname,
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-sm text-red-500" />
                </FormItem>
              )}
            />

            {/* Forgot Password and Register Section */}
            <div className="flex flex-col items-center space-y-4">
              <div className="text-gray-700">
                Remember your password?{" "}
                <Link
                  href="/login"
                  className="text-primary font-medium hover:underline"
                >
                  Login
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-center">
              <Button
                variant={submitButtonVariant}
                type="submit"
                size="md"
                className={cn(
                  "flex items-center justify-center font-semibold text-sm md:text-base bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark transition-all duration-150",
                  submitButtonClassname,
                )}
                disabled={isLoading}
              >
                {isLoading && <Loader className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading ? "Resetting..." : "RESET PASSWORD"}
              </Button>
            </div>
          </form>
        </Form>
      </FormCard>
    </div>
  );
};

export const ResetPasswordForm = (props: ResetPasswordFormProps) => {
  return (
    <Suspense fallback={
      <div className={cn("w-full max-w-md mx-auto", props.className)}>
        <FormCard className={cn("rounded-lg shadow-md p-6 bg-white", props.className)}>
          <div className="flex items-center justify-center py-8">
            <Loader className="h-6 w-6 animate-spin text-primary" />
          </div>
        </FormCard>
      </div>
    }>
      <ResetPasswordFormContent {...props} />
    </Suspense>
  );
}; 