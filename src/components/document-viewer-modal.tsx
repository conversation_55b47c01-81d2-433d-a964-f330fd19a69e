"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { useEffect, useState } from "react";
import { X, Download, ExternalLink } from "lucide-react";

interface DocumentViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  documentUrl: string;
  documentName: string;
}

export function DocumentViewerModal({
  isOpen,
  onClose,
  documentUrl,
  documentName,
}: DocumentViewerModalProps) {
  const [documentType, setDocumentType] = useState<"image" | "pdf" | "unknown">("unknown");
  const [isMobile, setIsMobile] = useState(false);
  const [pdfLoadError, setPdfLoadError] = useState(false);

  useEffect(() => {
    // Check if mobile device
    const checkMobile = () => {
      setIsMobile(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent) || window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (documentUrl) {
      // Reset error state when URL changes
      setPdfLoadError(false);
      
      // Check file extension or URL pattern to determine type
      const lowerUrl = documentUrl.toLowerCase();
      if (lowerUrl.endsWith(".pdf")) {
        setDocumentType("pdf");
      } else if (
        lowerUrl.endsWith(".jpg") ||
        lowerUrl.endsWith(".jpeg") ||
        lowerUrl.endsWith(".png") ||
        lowerUrl.endsWith(".gif") ||
        lowerUrl.endsWith(".webp")
      ) {
        setDocumentType("image");
      } else {
        // Default to PDF if can't determine (could be handled better based on API responses)
        setDocumentType("pdf");
      }
    }
  }, [documentUrl]);

  const handleDownload = () => {
    // Create a temporary anchor element to trigger download
    const link = document.createElement('a');
    link.href = documentUrl;
    link.download = documentName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleOpenInNewTab = () => {
    window.open(documentUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed left-[50%] top-[50%] max-h-[90vh] w-[90vw] max-w-4xl translate-x-[-50%] translate-y-[-50%] rounded-xl bg-white p-4 shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          <div className="flex justify-between items-center mb-4">
            <Dialog.Title className="text-xl font-medium">
              {documentName || "Document"}
            </Dialog.Title>
            <div className="flex items-center gap-2">
              {/* Download button */}
              <button
                onClick={handleDownload}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Download"
                title="Download"
              >
                <Download className="h-5 w-5" />
              </button>
              
              {/* Open in new tab button */}
              <button
                onClick={handleOpenInNewTab}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Open in new tab"
                title="Open in new tab"
              >
                <ExternalLink className="h-5 w-5" />
              </button>
              
              {/* Close button */}
              <Dialog.Close asChild>
                <button
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                  aria-label="Close"
                >
                  <X className="h-5 w-5" />
                </button>
              </Dialog.Close>
            </div>
          </div>
          
          <div className="overflow-auto h-[calc(90vh-120px)]">
            {documentType === "image" ? (
              <img
                src={documentUrl}
                alt={documentName}
                className="max-w-full h-auto mx-auto"
                onError={() => {
                  console.error("Failed to load image");
                }}
              />
            ) : documentType === "pdf" ? (
              <>
                {/* Show different viewer based on device type and error state */}
                {!isMobile && !pdfLoadError ? (
                  <object
                    data={`${documentUrl}#toolbar=1&navpanes=0&scrollbar=1&view=FitH`}
                    type="application/pdf"
                    className="w-full h-[calc(90vh-120px)]"
                    aria-label={documentName}
                    onError={() => {
                      console.error("Failed to load PDF in object tag");
                      setPdfLoadError(true);
                    }}
                  >
                    {/* Fallback to iframe if object fails */}
                    <iframe
                      src={`${documentUrl}#toolbar=1&navpanes=0&scrollbar=1`}
                      className="w-full h-[calc(90vh-120px)]"
                      title={documentName}
                      onError={() => {
                        console.error("Failed to load PDF in iframe");
                        setPdfLoadError(true);
                      }}
                    >
                      <p>Your browser does not support PDF viewing.</p>
                    </iframe>
                  </object>
                ) : (
                  /* Fallback for mobile or error state */
                  <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                    <div className="mb-6">
                      <svg
                        className="w-24 h-24 mx-auto text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {isMobile ? "PDF Preview Not Available on Mobile" : "Unable to Preview PDF"}
                    </h3>
                    
                    <p className="text-sm text-gray-600 mb-6 max-w-sm">
                      {isMobile 
                        ? "PDF preview is not supported on mobile devices. You can download the file or open it in a new tab to view it."
                        : "There was an issue displaying the PDF. You can still download it or open it in a new tab."}
                    </p>
                    
                    <div className="flex flex-col sm:flex-row gap-3">
                      <button
                        onClick={handleDownload}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Download className="h-4 w-4" />
                        Download PDF
                      </button>
                      
                      <button
                        onClick={handleOpenInNewTab}
                        className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Open in New Tab
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="flex justify-center items-center h-full">
                <p>Unsupported document type</p>
              </div>
            )}
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
} 