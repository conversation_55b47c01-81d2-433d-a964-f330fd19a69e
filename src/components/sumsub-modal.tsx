"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { FormCard } from "@/components/register/form-card";
import { useRouter } from "next/navigation";
import SumSubIframe from "@/components/sumsub-iframe";
import { SUMSUB_LEVEL } from "@/types/user";

interface CompanyModalProps {
  token: string;
  email: string;
  sumsubLevel: SUMSUB_LEVEL;
  isOpen: boolean;
  // onClose: () => void;
  // onApplicantReviewComplete?: () => void;
}

export function SumSubModal({
  isOpen,
  token,
  email,
  sumsubLevel,
  // onClose,
  // onApplicantReviewComplete,
}: CompanyModalProps) {
  const router = useRouter();

  const onApplicantReviewComplete = () => {
    router.push("/");
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={() => {}}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/20 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Title>KYC</Dialog.Title>
        <Dialog.Content className="fixed left-[50%] top-[50%] max-h-[85vh] overflow-y-auto w-[90vw] max-w-xl translate-x-[-50%] translate-y-[-50%] rounded-2xl bg-white p-6 shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          <FormCard className="max-w-xl w-full">
            <SumSubIframe
              token={token}
              email={email}
              sumsubLevel={sumsubLevel}
              onApplicantReviewComplete={onApplicantReviewComplete}
            />
          </FormCard>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
