import React from "react";
import { cn } from "@/utils/tailwind";
import { RegistrationType } from "@/types/user";
import KycIcon from "@/assets/icons/KycIcon";
import KybIcon from "@/assets/icons/KybIcon";
import { But<PERSON> } from "@/components/ui/button";
import { useFormContext } from "react-hook-form";

export const SelectRegistrationType = ({
  isPending,
}: {
  isPending: boolean;
}) => {
  const {
    setValue,
    getValues,
    formState: { errors },
    trigger,
  } = useFormContext();

  const step = getValues("step");
  const registrationType = getValues("registrationType").type;

  const handleRegistrationTypeChange = (type: RegistrationType) => {
    setValue("registrationType.type", type);
    trigger("registrationType");
  };

  return (
    <div className="h-full flex flex-col justify-center gap-4 pb-14">
      <p className="text-primary text-[27px] leading-[43px] font-semibold mb-8 sm:mb-20">
        You are registering:
      </p>

      <div className="flex flex-col items-center sm:flex-row gap-4 justify-between">
        <div
          className="flex flex-col gap-2 items-center h-full cursor-pointer"
          onClick={() => handleRegistrationTypeChange(RegistrationType.KYC)}
        >
          <div
            className={cn(
              "w-[146px] sm:w-[204px] min-h-[146px] sm:min-h-[204px] border border-input bg-white rounded-[20px] flex flex-col gap-4 justify-center items-center flex-1 p-7",
              {
                "bg-light-gray": registrationType === RegistrationType.KYC,
              },
            )}
          >
            <KycIcon />
          </div>
          <span className="text-primary text-base font-semibold">Me</span>
        </div>

        <div
          className="w-full flex flex-col gap-2 items-center cursor-pointer"
          onClick={() => handleRegistrationTypeChange(RegistrationType.KYB)}
        >
          <div
            className={cn(
              "w-[146px] sm:w-[204px] min-h-[146px] sm:min-h-[204px] border border-input bg-white rounded-[20px] flex flex-col gap-4 justify-center items-center flex-1 p-7",
              {
                "bg-light-gray": registrationType === RegistrationType.KYB,
              },
            )}
          >
            <KybIcon />
          </div>
          <span className="text-primary text-base font-semibold">Business</span>
        </div>
      </div>

      <div className="flex justify-center mt-8 sm:mt-14">
        <Button
          className="flex items-center gap-2.5 font-semibold text-base max-w-[160px] w-full uppercase"
          size="md"
          disabled={registrationType === undefined || isPending}
          onClick={() => setValue("step", step + 1)}
        >
          Next
        </Button>
      </div>
    </div>
  );
};
