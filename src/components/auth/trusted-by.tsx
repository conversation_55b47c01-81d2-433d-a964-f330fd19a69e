"use client";

import { useEffect, useRef, useState } from "react";
import Image from "next/image";

import innovateUkLogo from "../../../public/trusted-by/innovate-uk-logo-new.png";
import euFundedLogo from "../../../public/trusted-by/eu-funded-logo-new.png";
import researchInnovationLogo from "../../../public/trusted-by/research-innovation-logo-new.png";
import { useMediaQuery } from "usehooks-ts";
import { cn } from "@/utils/tailwind";
import { ChevronLeft, ChevronRight } from "lucide-react";

// Updated partners array with exact Figma dimensions
const partners = [
  {
    name: "Innovate UK",
    logo: innovateUkLogo,
    width: 171,
    height: 55,
  },
  {
    name: "European Union",
    logo: euFundedLogo,
    width: 81,
    height: 82,
  },
  {
    name: "Research & Innovation Foundation",
    logo: researchInnovationLogo,
    width: 187,
    height: 83,
  },
];

export function TrustedBy() {
  const [activeSlide, setActiveSlide] = useState(0);
  const sliderRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQuery("(max-width: 768px)");
  
  // For touch events
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  
  // For auto-rotation
  const [isPaused, setIsPaused] = useState(false);
  
  // Navigation functions
  const nextSlide = () => {
    setActiveSlide((prev) => (prev === partners.length - 1 ? 0 : prev + 1));
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 5000);
  };
  
  const prevSlide = () => {
    setActiveSlide((prev) => (prev === 0 ? partners.length - 1 : prev - 1));
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 5000);
  };
  
  // Auto-advance the slider every 3 seconds on mobile
  useEffect(() => {
    if (!isMobile || isPaused) return;
    
    const interval = setInterval(() => {
      setActiveSlide((prev) => (prev === partners.length - 1 ? 0 : prev + 1));
    }, 3000);
    
    return () => clearInterval(interval);
  }, [isMobile, isPaused]);
  
  // Update slider position when activeSlide changes
  useEffect(() => {
    if (sliderRef.current && isMobile) {
      const slideWidth = sliderRef.current.offsetWidth;
      sliderRef.current.scrollTo({
        left: activeSlide * slideWidth,
        behavior: "smooth",
      });
    }
  }, [activeSlide, isMobile]);
  
  // Handle touch events
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(0);
    setTouchStart(e.targetTouches[0].clientX);
  };
  
  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };
  
  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;
    
    if (isLeftSwipe && activeSlide < partners.length - 1) {
      nextSlide();
    }
    
    if (isRightSwipe && activeSlide > 0) {
      prevSlide();
    }
    
    setTouchStart(0);
    setTouchEnd(0);
  };
  
  return (
    <section className="py-8 md:py-16">
      <div className="container">
        {/* Mobile Layout */}
        {isMobile ? (
          <div className="flex flex-col items-center">
            <h3 className="text-xl text-[#1C3F3C] font-poppins font-normal mb-8 text-center">
              Funded by:
            </h3>
            
            <div className="relative w-full max-w-sm">
              <div
                ref={sliderRef}
                className="flex overflow-hidden w-full"
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
              >
                {partners.map((partner, index) => (
                  <div
                    key={index}
                    className="min-w-full flex justify-center items-center py-4"
                  >
                    <Image
                      src={partner.logo}
                      alt={partner.name}
                      width={120}
                      height={60}
                      className="object-contain max-h-[60px] w-[120px]"
                    />
                  </div>
                ))}
              </div>
              
              {/* Navigation Arrows */}
              <button
                onClick={prevSlide}
                className="absolute top-1/2 left-2 -translate-y-1/2 bg-white/90 rounded-full p-2 shadow-lg z-10 hover:bg-white transition-colors"
                aria-label="Previous slide"
              >
                <ChevronLeft className="w-4 h-4 text-[#1C3F3C]" />
              </button>
              <button
                onClick={nextSlide}
                className="absolute top-1/2 right-2 -translate-y-1/2 bg-white/90 rounded-full p-2 shadow-lg z-10 hover:bg-white transition-colors"
                aria-label="Next slide"
              >
                <ChevronRight className="w-4 h-4 text-[#1C3F3C]" />
              </button>
              
              {/* Navigation Dots */}
              <div className="flex justify-center gap-2 mt-6">
                {partners.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setActiveSlide(index);
                      setIsPaused(true);
                      setTimeout(() => setIsPaused(false), 5000);
                    }}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors duration-200",
                      activeSlide === index ? "bg-[#1C3F3C]" : "bg-gray-300",
                    )}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        ) : (
          /* Desktop Layout - All items spread evenly with fixed image sizes */
          <div className="flex items-center justify-evenly gap-4 md:gap-6">
            <h3 className="text-lg md:text-xl text-[#1C3F3C] font-poppins font-normal whitespace-nowrap">
              Funded by:
            </h3>
            
            {partners.map((partner, index) => (
              <div key={index} className="flex justify-center items-center">
                <Image
                  src={partner.logo}
                  alt={partner.name}
                  width={120}
                  height={60}
                  className="object-contain max-h-[60px] w-[120px]"
                  priority
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}
