import { RegistrationType } from "@/types/user";
import FormInput from "../register/form-input";
import { FormCardMultiStepCard } from "../register/form-multi-step-card";
import FormPassword from "../register/form-password";
import { Button } from "../ui/button";
import Link from "next/link";

export const UserRegistration = ({
  isPending,
  registrationType,
}: {
  isPending: boolean;
  registrationType: RegistrationType;
}) => {
  return (
    <FormCardMultiStepCard
      stepName={
        registrationType === RegistrationType.KYC
          ? "User Registration"
          : "Company Registration"
      }
    >
      <div className="flex flex-col gap-3">
        {/* Only show these fields for KYC registration */}
        {registrationType === RegistrationType.KYC && (
          <>
            <FormInput
              label="First Name"
              name="personalInfo.firstName"
              placeholder="Enter your first name"
            />
            <FormInput
              label="Last Name"
              name="personalInfo.lastName"
              placeholder="Enter your last name"
            />
          </>
        )}
        <FormInput
          label={
            registrationType === RegistrationType.KYC
              ? "Email  "
              : "Company Email"
          }
          name="personalInfo.email"
          placeholder={
            registrationType === RegistrationType.KYC
              ? "Enter your email"
              : "Enter your company email"
          }
        />
        <FormPassword
          label="Password"
          name="personalInfo.password"
          placeholder="Enter your password"
        />
        <FormPassword
          label="Confirm Password"
          name="personalInfo.confirmPassword"
          placeholder="Confirm your password"
        />
      </div>
      <div className="flex flex-col gap-4 justify-center items-center mt-8 sm:mt-14">
        <Button
          type="submit"
          className="flex items-center gap-2.5 font-semibold text-base  uppercase"
          size="md"
          disabled={isPending}
        >
          {registrationType === RegistrationType.KYC
            ? "Register"
            : "Register Company"}
        </Button>
        <span className="text-primary text-base ">
          Already have an account?{" "}
          <Link href="/login" className="text-primary">
            Login
          </Link>
        </span>
      </div>
    </FormCardMultiStepCard>
  );
};
