"use client";

import Image from "next/image";
import SearchIcon from "../../assets/icons/SearchIcon";
import logo from "../../../public/logo.png";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export const AuthHeader = () => {
  return (
    <header className="flex items-center justify-between">
      <Image src={logo} alt="Logo" width={203} height={29} />

      <div className="flex items-center gap-5">
        <SearchIcon className="cursor-pointer" />
        <Button size="md">
          <Link href="/login">Login</Link>
        </Button>
      </div>
    </header>
  );
};
