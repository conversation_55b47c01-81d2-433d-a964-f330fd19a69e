"use client";

import { useEffect, useRef, useState } from "react";
import Image from "next/image";

import partner1 from "../../../public/trusted-by/Innovate-UK-logo 1.png";
import partner2 from "../../../public/trusted-by/Innovate-UK-logo 3.png";
import partner3 from "../../../public/trusted-by/Innovate-UK-logo 4.png";
import partner4 from "../../../public/trusted-by/Innovate-UK-logo 5.png";
import partner5 from "../../../public/trusted-by/Innovate-UK-logo 7.png";
import { useMediaQuery } from "usehooks-ts";
import { cn } from "@/utils/tailwind";

// You'll need to replace these with your actual partner logos
const partners = [
  {
    name: "Fantom Foundation",
    logo: partner2, // Replace with your actual path
  },
  {
    name: "AIK",
    logo: partner1, // Replace with your actual path
  },
  {
    name: "<PERSON><PERSON>",
    logo: partner3, // Replace with your actual path
  },
  {
    name: "LL<PERSON>",
    logo: partner4, // Replace with your actual path
  },
  {
    name: "DS Partners",
    logo: partner5, // Replace with your actual path
  }
];

export function TrustedByFooter() {
  return <></>
  const [activeSlide, setActiveSlide] = useState(0);
  const sliderRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQuery("(max-width: 768px)");

  // For touch events
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  // For auto-rotation
  const [isPaused, setIsPaused] = useState(false);

  // Auto-advance the slider every 3 seconds on mobile
  useEffect(() => {
    if (!isMobile || isPaused) return;

    const interval = setInterval(() => {
      setActiveSlide((prev) => (prev === partners.length - 1 ? 0 : prev + 1));
    }, 3000);

    return () => clearInterval(interval);
  }, [isMobile, isPaused]);

  // Update slider position when activeSlide changes
  useEffect(() => {
    if (sliderRef.current && isMobile) {
      const slideWidth = sliderRef.current.offsetWidth;
      sliderRef.current.scrollTo({
        left: activeSlide * slideWidth,
        behavior: "smooth",
      });
    }
  }, [activeSlide, isMobile]);

  // Handle touch end
  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && activeSlide < partners.length - 1) {
      setActiveSlide(activeSlide + 1);
    }

    if (isRightSwipe && activeSlide > 0) {
      setActiveSlide(activeSlide - 1);
    }

    // Reset values
    setTouchStart(0);
    setTouchEnd(0);

    // Resume auto-rotation after 5 seconds of inactivity
    setTimeout(() => setIsPaused(false), 5000);
  };

  return (
    <section className="py-16 md:py-28 relative">
      <div className="container mx-auto px-4">
        {/* Desktop view */}
        <div className="hidden md:block">
          <p className="text-primary text-center text-xl font-normal underline mb-10">
            Trusted by:
          </p>
          <div className="flex justify-center items-center gap-[60px]">
            {partners.map((partner, index) => (
              <div key={index} className="h-[84px] flex items-center">
                <Image
                  src={partner.logo}
                  alt={partner.name}
                  className="object-contain mix-blend-multiply max-h-full"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Mobile view */}
        {isMobile && (
          <div className="md:hidden">
            <p className="text-primary text-center text-xl font-normal underline mb-8">
              Trusted by:
            </p>
            <div className="relative w-full">
              <div
                ref={sliderRef}
                className="flex overflow-hidden w-full touch-pan-y"
                onTouchStart={(e) => setTouchStart(e.touches[0].clientX)}
                onTouchMove={(e) => setTouchEnd(e.touches[0].clientX)}
                onTouchEnd={handleTouchEnd}
              >
                {partners.map((partner, index) => (
                  <div
                    key={index}
                    className="min-w-full flex justify-center items-center h-[84px]"
                  >
                    <Image
                      src={partner.logo}
                      alt={partner.name}
                      className="object-contain mix-blend-multiply max-h-full"
                    />
                  </div>
                ))}
              </div>

              {/* Navigation Dots */}
              <div className="flex justify-center gap-3 mt-8">
                {partners.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setActiveSlide(index);
                      setIsPaused(true);
                      setTimeout(() => setIsPaused(false), 5000);
                    }}
                    className={cn(
                      "w-3 h-3 rounded-full transition-colors",
                      activeSlide === index ? "bg-primary" : "bg-gray-300"
                    )}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
