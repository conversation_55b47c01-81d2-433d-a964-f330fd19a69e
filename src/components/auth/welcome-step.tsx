"use client";
import { useQueryState } from "nuqs";
import { WelcomeCard } from "./welcome-card";
import SumSubIframe from "../sumsub-iframe";
import { FormCard } from "../register/form-card";
import { cn } from "@/utils/tailwind";
import { RegistrationType, SUMSUB_LEVEL } from "@/types/user";
import { useRouter } from "next/navigation";

interface WelcomeStepCardProps {
  token: string;
  email: string;
  userType: RegistrationType;
}

export enum WelcomeStep {
  welcome = "welcome",
  kyc = "kyc",
}

export const WelcomeStepCard = ({
  token,
  email,
  userType,
}: WelcomeStepCardProps) => {
  const [step, setStep] = useQueryState("step", {
    defaultValue: WelcomeStep.welcome,
  });

  const router = useRouter();

  const renderStep = (step: WelcomeStep) => {
    console.log("step", step);

    switch (step) {
      case WelcomeStep.welcome:
        return (
          <WelcomeCard setStep={setStep} step={step} userType={userType} />
        );
      case WelcomeStep.kyc:
        return (
          <SumSubIframe
            token={token}
            email={email}
            sumsubLevel={
              userType === RegistrationType.KYC
                ? SUMSUB_LEVEL.TIER1
                : SUMSUB_LEVEL.KYB_TIER1
            }
            onApplicantReviewComplete={() => router.push("/categories")}
          />
        );
    }
  };

  return (
    <FormCard className={cn("", step !== WelcomeStep.kyc && "p-5")}>
      {renderStep(step as WelcomeStep)}
    </FormCard>
  );
};
