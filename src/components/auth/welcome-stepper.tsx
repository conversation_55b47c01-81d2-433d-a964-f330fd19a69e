import React from "react";
import { WelcomeStep } from "./welcome-step";
import <PERSON><PERSON>ef<PERSON> from "@/assets/icons/ArrowLeft";
import { cn } from "@/utils/tailwind";

const STEP_ORDER = {
  [WelcomeStep.welcome]: 1,
  [WelcomeStep.kyc]: 2,
} as const;

export const WelcomeStepper = ({
  step,
  setStep,
}: {
  step: WelcomeStep;
  setStep: (step: WelcomeStep) => void;
}) => {
  const currentStepNumber = STEP_ORDER[step] || 1;

  const handlePrevious = () => {
    if (currentStepNumber > 1) {
      setStep(WelcomeStep.welcome);
    }
  };

  return (
    <div className="w-full">
      <div className="flex w-full relative">
        <ArrowLeft
          className="w-[24px] cursor-pointer"
          onClick={handlePrevious}
        />

        <div className="flex-1 flex justify-center items-center w-full">
          <p className="text-[10px] leading-[13px] text-black absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            Step {currentStepNumber}/2
          </p>
        </div>
      </div>

      {/*Steps*/}
      <div className="flex gap-2.5 mt-[21px]">
        {[1, 2].map((stepNumber) => (
          <div
            key={stepNumber}
            className={cn("flex-1 rounded-3xl h-[2px] bg-secondary", {
              "bg-primary": currentStepNumber >= stepNumber,
            })}
          />
        ))}
      </div>
    </div>
  );
};
