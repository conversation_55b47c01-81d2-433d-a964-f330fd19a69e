"use client";
import { useRouter } from "next/navigation";
import React from "react";
import Image from "next/image";
import logo2 from "../../../public/logo2.png";
import { Button } from "@/components/ui/button";
import { WelcomeStep } from "./welcome-step";
import { RegistrationType } from "@/types/user";

interface WelcomeCardProps {
  step: WelcomeStep;
  setStep: (step: WelcomeStep) => void;
  userType: RegistrationType;
}

/**
 * Represents a Welcome Card component that displays welcome information and controls for starting KYC.
 */
export const WelcomeCard = ({ setStep, userType }: WelcomeCardProps) => {
  const router = useRouter();

  const handleStartKyc = (event: React.MouseEvent) => {
    event.preventDefault();
    setStep("kyc" as WelcomeStep);
  };

  return (
    <div className="flex flex-col gap-10 items-center pb-10">
      <h2 className="text-primary text-4xl font-semibold">Welcome!</h2>

      <div className="text-center">
        <p className="text-black mb-3">
          We know. KYC checks are long and tiresome.
        </p>
        <p className="text-black">
          Join the nexus and access everything with just one click!
        </p>
      </div>

      <div className="flex justify-center gap-4 mt-8 sm:mt-14">
        <Button
          className="font-semibold text-base w-[180px] uppercase"
          size="md"
          type="button"
          onClick={handleStartKyc}
        >
          {userType === RegistrationType.KYC ? "Start KYC" : "Start KYB"}
        </Button>

        <Button
          className="font-semibold text-base w-[180px] uppercase"
          size="md"
          type="button"
          variant="secondary"
          onClick={() => router.push("/categories")}
        >
          Skip
        </Button>
      </div>
    </div>
  );
};
