//update imports
import { STEPS_TO_STEP_NAMES } from "@/app/(auth)/register/register";
import { Button } from "@/components/ui/button";
import { useFormContext } from "react-hook-form";

export const FormControls = () => {
  // Since we will use this inside the form,
  // we can get acccess to the form context using useFormContext
  const { control, setValue, getValues } = useFormContext();

  // Get the current step from the form values
  const step = getValues("step");

  // Get the current step name
  const currentStepName = STEPS_TO_STEP_NAMES[step];

  const isFirstStep = Object.keys(STEPS_TO_STEP_NAMES)[0] === currentStepName;
  const isLastStep = step === Object.keys(STEPS_TO_STEP_NAMES).length;

  return (
    <div className="flex w-full gap-6 justify-between">
      <Button
        variant="outline"
        type="button"
        disabled={isFirstStep}
        onClick={() => setValue("step", step - 1)}
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        Previous
      </Button>
      {/* if it's not the last step else try submit the form */}
      {isLastStep ? (
        <Button variant="default" type="submit">
          Save
        </Button>
      ) : (
        <Button
          // Change the button type to submit when it's the last step
          type="button"
          onClick={() => {
            setValue("step", step + 1);
          }}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Next
        </Button>
      )}
    </div>
  );
};
