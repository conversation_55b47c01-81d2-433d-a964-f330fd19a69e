import Link from "next/link";

interface IndustryCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
}

export const IndustryCard = ({
  icon,
  title,
  description,
  href,
}: IndustryCardProps) => {
  return (
    <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="mb-4 text-gray-700">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">{title}</h3>
      <p className="text-gray-600 text-sm mb-4">{description}</p>
      <Link
        href={href}
        className="text-sm font-medium text-gray-800 hover:underline"
      >
        Learn More
      </Link>
    </div>
  );
};
