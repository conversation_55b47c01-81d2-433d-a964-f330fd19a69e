import Link from "next/link";
import { cn } from "@/utils/tailwind";
import React from "react";
import Image, { StaticImageData } from "next/image";

export interface NewIndustryCardProps {
  icon: StaticImageData; // Placeholder for icon, to be added later
  title: string;
  description: string;
  href: string;
  className?: string; // Allow passing additional classes
  highlight?: boolean; // Optional prop for the highlighted card with shadow
}

export function NewIndustryCard({
  icon,
  title,
  description,
  href,
  className,
  highlight = false, // Default to not highlighted
}: NewIndustryCardProps) {
  return (
    <div
      className={cn(
        "bg-white rounded-[40px] border p-6 md:p-8 flex flex-col w-full h-96 relative", // Added relative positioning
        "transition-shadow duration-300", // Added transition for hover effect
        highlight
          ? "border-transparent shadow-[0px_16px_32px_-2px_rgba(32,33,36,0.15)] shadow-[0px_8px_16px_-2px_rgba(32,33,36,0.06)]" // Shadow for highlighted card
          : "border-light-gray hover:shadow-md", // Border and subtle hover shadow for normal cards
        className // Allow overriding styles
      )}
    >
      {/* Icon Placeholder - Positioned top-right */}
      <div className="absolute top-6 right-6 md:top-8 md:right-8 w-16 h-16 md:w-20 md:h-20  rounded-lg flex items-center justify-center text-gray-400">
        <Image
          src={icon}
          alt={title}
          width={200}
          height={200}
          className="size-15"
        />
      </div>
      {/* Spacer to push content below the absolutely positioned icon area */}
      <div className="h-16 md:h-20 mb-4"></div>{" "}
      {/* Adjust height to match icon */}
      {/* Text Content - Starts below the icon area */}
      <div className="flex flex-col items-start gap-2 flex-grow">
        {" "}
        {/* Use flex-grow to push button down */}
        {/* Title */}
        <h3 className="text-primary text-2xl font-semibold leading-loose">
          {title}
        </h3>
        {/* Description */}
        <p className="text-neutral-400 text-lg  leading-normal">
          {description}
        </p>
      </div>
      {/* Bottom section: Button */}
      <Link
        href={href}
        className={cn(
          "mt-4 bg-white rounded-[100px] border border-main-light-color py-1 px-6 md:px-8 self-start", // Align button to start (left) and add top margin
          "text-primary text-center text-lg font-medium leading-none", // Adjusted text styles
          "hover:bg-gray-100 transition-colors duration-200" // Hover effect
        )}
      >
        Learn More
      </Link>
    </div>
  );
}
