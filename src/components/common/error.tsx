"use client";

import { Ghost } from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "./card";
import { Button } from "../ui/button";

interface ErrorProps {
  message?: string;
  reset?: () => void;
}
const ErrorComponent = ({
  message = "Something went wrong",
  reset,
}: ErrorProps) => {
  return (
    <Card className="min-h-[50dvh] h-full flex flex-col items-center justify-center gap-4">
      <CardHeader className="text-center">
        <CardTitle>{message}</CardTitle>
        <CardDescription>{"Sorry we couldn't load this page"}</CardDescription>
      </CardHeader>

      <CardContent>
        <Ghost size={100} className="animate-pulse text-accent" />
      </CardContent>
      {reset && (
        <CardFooter>
          <Button onClick={reset} className="btn btn-primary">
            {"Reload"}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default ErrorComponent;
