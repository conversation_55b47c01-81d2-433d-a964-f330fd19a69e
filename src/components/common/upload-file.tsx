import React, { ChangeEvent, useRef, useState } from "react";
import { Button } from "@/components/ui/button";

export interface UploadFileProps {
  selectedFile?: File;
  label: string;
  onSelectedFileChange: (file: File) => void;
}

export const UploadFile = ({
  selectedFile,
  label,
  onSelectedFileChange,
}: UploadFileProps) => {
  const [file, setFile] = useState<File | null>(selectedFile ?? null);
  const [dragActive, setDragActive] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const openFileExplorer = () => {
    inputRef.current?.click();
  };

  const onSelectedFile = (event: ChangeEvent<HTMLInputElement>): void => {
    const files = event.target ? event.target.files : null;
    if (files?.length) {
      if (files[0]) {
        setFile(files[0]);
        onSelectedFileChange(files[0]);
      }
    }
  };

  const handleDragLeave = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDragOver = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragEnter = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDrop = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setFile(e.dataTransfer.files[0]);
      onSelectedFile(e.dataTransfer.files[0]);
    }
  };

  return (
    <div>
      <p className="text-black">{label}</p>
      <div
        className="border border-gray-300 border-dashed rounded-lg w-full h-[100px] bg-gray-50 flex justify-center items-center mt-2"
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {!file && (
          <>
            <p className="text-black">
              <Button
                variant="text-underline"
                size="text"
                type="button"
                onClick={openFileExplorer}
              >
                Upload a file
              </Button>
              {""} or drag and drop in this box.
            </p>
          </>
        )}

        {file && <p className="text-black">{file.name}</p>}

        <input
          ref={inputRef}
          type="file"
          className="hidden"
          onChange={(e) => onSelectedFile(e)}
        />
      </div>
    </div>
  );
};
