"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import DocumentIcon from "@/assets/icons/DocumentIcon";
import revokeLogo from "./../../public/revoke-logo.png";

interface RevokeConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  companyName: string;
  isLoading?: boolean;
}

export function RevokeConsentModal({
  isOpen,
  onClose,
  onConfirm,
  companyName,
  isLoading = false,
}: RevokeConsentModalProps) {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/70 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed left-[50%] top-[50%] w-[90vw] max-w-[452px] h-auto max-h-[90vh] md:w-[452px] md:h-[464px] translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white shadow-[22px_40px_40px_0px_rgba(0,0,0,0.05)] border border-[#C8E2CE] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          
          {/* Close Button */}
          <div className="absolute top-[23px] left-[27px]">
            <Dialog.Close asChild>
              <button
                className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close"
                disabled={isLoading}
              >
                <X className="h-5 w-5 text-black stroke-2" />
              </button>
            </Dialog.Close>
          </div>

          {/* Revoke Logo */}
          <div className="flex justify-center mt-[56px] mb-[22px] px-4">
            <div className="w-[178px] h-[89px] flex items-center justify-center">
              <img
                src={revokeLogo.src}
                alt="Revoke consent"
                className="max-w-full max-h-full object-contain"
              />
            </div>
          </div>

          {/* Confirmation Message */}
          <div className="px-6 md:px-[70px] mb-[29px]">
            <p className="text-[#1C3F3C] text-[14px] font-medium leading-[18px] text-center tracking-[0.02em] font-poppins">
              Please confirm that you wish to withdraw your consent for {companyName} to access your details.
              <br />
              <br />
              This means that you will no longer be a client of {companyName}.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="px-6 md:px-[102px] space-y-[15px] pb-6 md:pb-0">
            {/* Revoke Button */}
            <Button
              onClick={onConfirm}
              disabled={isLoading}
              className="w-full h-[56px] bg-[#1C3F3D] text-white font-semibold text-[16px] leading-[24px] rounded-[40px] border border-black hover:bg-[#1C3F3D]/90 disabled:opacity-50 disabled:cursor-not-allowed font-poppins"
            >
              {isLoading ? "Revoking..." : "Revoke"}
            </Button>

            {/* Cancel Button */}
            <Button
              onClick={onClose}
              disabled={isLoading}
              variant="secondary"
              className="w-full h-[56px] bg-white text-[#1C3F3C] font-medium text-[16px] leading-[32px] rounded-[40px] shadow-[0px_6px_8.3px_4px_rgba(28,63,60,0.2)] hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed font-poppins"
            >
              Cancel
            </Button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
} 