import { Clock, Construction, Loader2 } from "lucide-react";

interface InProgressProps {
  message?: string;
  icon?: "construction" | "clock" | "loader";
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function InProgress({
  message = "This section is under construction",
  icon = "construction",
  size = "md",
  className = "",
}: InProgressProps) {
  const sizeClasses = {
    sm: "p-4 text-sm",
    md: "p-6 text-base",
    lg: "p-8 text-lg",
  };

  const iconSize = {
    sm: 24,
    md: 32,
    lg: 48,
  };

  const renderIcon = () => {
    switch (icon) {
      case "construction":
        return (
          <Construction size={iconSize[size]} className="text-emerald-600" />
        );
      case "clock":
        return <Clock size={iconSize[size]} className="text-emerald-600" />;
      case "loader":
        return (
          <Loader2
            size={iconSize[size]}
            className="text-emerald-600 animate-spin"
          />
        );
      default:
        return (
          <Construction size={iconSize[size]} className="text-emerald-600" />
        );
    }
  };

  return (
    <div
      className={`flex flex-col items-center justify-center bg-emerald-50 border border-emerald-100 rounded-lg text-center ${sizeClasses[size]} ${className}`}
    >
      <div className="bg-white p-3 rounded-full shadow-sm mb-3">
        {renderIcon()}
      </div>
      <p className="text-gray-700 font-medium">{message}</p>
    </div>
  );
}
