"use client";

import * as React from "react";
import { format, getYear } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { CaptionLayout, SelectSingleEventHandler } from "react-day-picker";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/common/popover";
import { Calendar } from "@/components/calendar/calendar";
import { cn } from "@/utils/tailwind";
import { Button } from "@/components/ui/button";

interface SampleDatePickerProps {
  selected?: Date;
  onSelect?: SelectSingleEventHandler | undefined;
  mode?: "single" | "multiple" | "default" | undefined;
  captionLayout?: CaptionLayout | undefined;
  initialFocus?: boolean;
}

export function SampleDatePicker({
  mode = "single",
  selected,
  onSelect,
  captionLayout = "dropdown-buttons",
  initialFocus,
}: SampleDatePickerProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"secondary"}
          className={cn(
            "justify-start text-left font-normal",
            !selected && "text-muted-foreground",
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selected ? format(selected, "PPP") : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className=" w-auto p-0">
        <Calendar
          mode="single"
          captionLayout={captionLayout}
          selected={selected}
          onSelect={onSelect}
          fromYear={1960}
          toYear={getYear(new Date())}
          initialFocus={initialFocus}
        />
      </PopoverContent>
    </Popover>
  );
}
