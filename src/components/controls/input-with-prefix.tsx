import { Input } from "./input";
import * as React from "react";
import { cn } from "@/utils/tailwind";

type InputWithPrefixProps = React.InputHTMLAttributes<HTMLInputElement> & {
  prefix: string;
};
// eslint-disable-next-line react/display-name
export const InputWithPrefix = React.forwardRef<
  HTMLInputElement,
  InputWithPrefixProps
>(({ ...props }, ref) => {
  return (
    <div className="w-full  space-y-2">
      <div className="relative">
        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 text-sm">
          {props.prefix}
        </div>
        <Input
          {...props}
          ref={ref}
          className={cn(props.prefix.length >= 3 ? "pl-12" : "pl-7")}
        />
      </div>
    </div>
  );
});
