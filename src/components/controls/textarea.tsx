import * as React from "react";
import { cn } from "@/utils/tailwind";

export type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>;

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          "border-input text-primary font-normal placeholder:text-secondary/50 flex h-[52px] w-full rounded-lg border bg-background px-[18px] py-2 min-h-[80px] text-xl transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus:outline-none focus:border-secondary focus:border-2 disabled:cursor-not-allowed disabled:border-2 disabled:border-secondary/40 disabled:text-primary/40 invalid:border-destructive invalid:border-2",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Textarea.displayName = "Textarea";

export { Textarea };
