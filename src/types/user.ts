export enum RegistrationType {
  KYC = "individual",
  KYB = "company",
}

export enum SUMSUB_LEVEL {
  TIER1 = "tier1",
  TIER2 = "tier2",
  TIER3 = "tier3",
  KYB_TIER1 = "kybtier1",
  KYB_TIER2 = "kybtier2",
  KYB_TIER3 = "kybtier3",
  LIVE = "live",
  INTERVIEW = "interview",
}

export interface UserIndividual {
  id: number;
  applicantId: string;
  userId: string;
  email: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  birthDate: string;
  country: string;
  countryOfResidence: string | null;
  taxResidenceCountry: string | null;
  address: string;
  tin: string | null;
}

export interface UserCompany {
  id: number;
  applicantId: string | null;
  userId: string;
  email: string | null;
  phoneNumber: string | null;
  companyName: string | null;
  registrationNumber: string | null;
  country: string | null;
  legalAddress: string | null;
  incorporationDate: string | null;
  taxId: string | null;
  registrationLocation: string | null;
  website: string | null;
  postalAddress: string | null;
  beneficiaryApplicantId?: string | null;
  beneficiaryUserId?: string | null;
}

export interface UserInfo {
  id: number;
  state: string;
  registrationType: string;
  applicantId: string;
  userId: string;
  applicationUrl: string | null;
  interviewCompletedDate: string | null;
}

export interface User {
  user_id: string;
  applicant_id: string;
  email: string;
  password_hash?: string;
  state: SUMSUB_LEVEL | string;
  signup_date: string;
  registrationtype: RegistrationType | string;
  registrationType?: string;
  name?: string;
  roles?: any[];
  companies?: any[];
  token?: string | null;
  userIndividual?: UserIndividual;
  userCompany?: UserCompany | null;
  sumSubUserCompany?: UserCompany | null;
  userInfo?: UserInfo;
  applicationUrl?: string;
  tierNumber?: string;
}

export interface Error {
  code: number;
  message: string;
  source: string;
  severity: number;
  customMessage: string;
}

export interface PaginatedData {
  pageSize: number;
  pageNumber: number;
  totalPages: number;
  totalCount: number;
  result: User[];
}

export interface LoginResponse {
  isSuccess: boolean;
  data: PaginatedData;
  errors: Error[];
  user: User;
}

export interface RegistrationResponse {
  isSuccess: boolean;
  data: User;
  errors: Error[];
}

export const getEnumValue = (value: string): SUMSUB_LEVEL | undefined => {
  if (Object.values(SUMSUB_LEVEL).includes(value as SUMSUB_LEVEL)) {
    return value as SUMSUB_LEVEL;
  }
  return undefined; // Return undefined if the string does not match any enum value
};
