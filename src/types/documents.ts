export interface DocumentResponse {
  isSuccess: boolean;
  data: Document[];
  errors: ErrorDetail[];
}

export interface Document {
  fileS3Path: string;
  fileS3PathThumbnail: string;
  fileName: string;
  idDocDefCountry: string;
  idDocDefIdDocType: string;
  idDocDefIdDocSubType: string;
  creationDate: string; // ISO date string
  modificationDate: string; // ISO date string
}

export interface ErrorDetail {
  code: number;
  message: string;
  source: string;
  severity: number;
  customMessage: string;
}

export interface TieredDocument extends Document {
  documentId: string;
  documentPreviewId: string;
  fileType: string;
  state: string;
}

export interface TierDocuments {
  state: string;
  files: TieredDocument[];
}

export interface TieredDocumentsResponse {
  isSuccess: boolean;
  data: {
    applicantId: string;
    tiers: TierDocuments[];
  };
  errors: ErrorDetail[] | null;
}
