import { SUMSUB_LEVEL } from "@/types/user";

export interface Company {
  s3ImageUrl: string | null;
  s3LogoUrl: string | null;
  company_id: number;
  name: string;
  logo: any;
  slug: string;
  description: string;
  tier: SUMSUB_LEVEL;
  required_individual_tier: SUMSUB_LEVEL;
  required_corporate_tier: SUMSUB_LEVEL;
}

export interface CategoryData {
  [key: string]: Company[];
}

export interface CompanyGroup {
  letter: string;
  companies: Company[];
}
