import { z } from "zod";
import { PASSWORD_REGEX, PASSWORD_REGEX_MESSAGE } from "@/schemas/constants";

export const LoginSchema = z.object({
  username: z.string().email({
    message: "Email is required",
  }),
  password: z.string().min(8, {
    message:
      "Password must be 8 to 15 characters long, contain four character types: lowercase letters, uppercase letters, numbers and symbols (#, @, !, etc.)",
  }),
});

export type LoginInputs = z.infer<typeof LoginSchema>;

export const ForgetPasswordSchema = z.object({
  username: z.string().email({
    message: "Email is required",
  }),
});

export const EditProfileSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email({
    message: "Email is required",
  }),
  phone_number: z.string().min(5, "Phone number is required"),
  country: z.string().min(1, "Country is required"),
});

export const ResetPasswordSchema = z
  .object({
    token: z.string({
      message: "Token is required",
    }),
    password: z
      .string({
        required_error: "Password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
    password_confirm: z
      .string({
        required_error: "Confirm password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
  })
  .superRefine(({ password, password_confirm }, ctx) => {
    if (password !== password_confirm) {
      ctx.addIssue({
        code: "custom",
        message: "Passwords do not match",
        path: ["password_confirm"],
      });
    }
  });

export type ResetPasswordInputs = z.infer<typeof ResetPasswordSchema>;

export const ChangeTaPasswordSchema = z
  .object({
    display_name: z.string({
      message: "display_name is required",
    }),
    account_id: z.union([z.number(), z.string(), z.undefined()], {
      message: "account_id is required",
    }),
    old_password: z
      .string({
        required_error: "Confirm password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
    password: z
      .string({
        required_error: "Password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
    password_confirm: z
      .string({
        required_error: "Confirm password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
  })
  .superRefine(({ password, password_confirm, old_password }, ctx) => {
    if (password !== password_confirm) {
      ctx.addIssue({
        code: "custom",
        message: "Passwords do not match",
        path: ["password_confirm"],
      });
    }
    if (password === old_password) {
      ctx.addIssue({
        code: "custom",
        message: "Passwords need to be different",
        path: ["old_password"],
      });
    }
  });

export type ChangeTaPasswordInputs = z.infer<typeof ChangeTaPasswordSchema>;

export const ChangePasswordSchema = z
  .object({
    password_current: z.string({
      message: "Old password is required",
    }),
    password_new: z
      .string({
        required_error: "New password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
    password_confirm: z
      .string({
        required_error: "Confirm password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
  })
  .superRefine(({ password_new, password_confirm, password_current }, ctx) => {
    if (password_new !== password_confirm) {
      ctx.addIssue({
        code: "custom",
        message: "Passwords do not match",
        path: ["password_confirm"],
      });
    }
    if (password_current === password_new) {
      ctx.addIssue({
        code: "custom",
        message: "New password must be different from the current password",
        path: ["password_new"],
      });
    }
  });

export const InternalTransferSchema = z.object({
  wallet_from: z.number({
    message: "From account is required",
  }),
  wallet_to: z.number({
    message: "To account is required",
  }),
  amount: z.string({
    message: "Amount is required",
  }),
});

export const NewTradingAccountSchema = z.object({
  trading_accounts: z.array(
    z.object({
      account_type: z.string().optional(),
      backend: z.string({
        message: "Backend is required",
      }),
      currency: z.string({
        message: "Currency is required",
      }),
      leverage: z.number().optional(),
      user_data: z
        .object({
          net_based: z.string(),
        })
        .optional(),
    }),
  ),
});

export type NewTradingAccountInputs = z.infer<typeof NewTradingAccountSchema>;

// amount: "1",
//     method: 37,
//     override_gateway: "safecharge",
//     trading_account: 90,
export const WithdrawalSchema = z
  .object({
    amount: z
      .string({
        message: "Amount is required",
      })
      .min(1, {
        message: "Amount is required",
      }),
    meta: z.any().optional().nullable(),
    method: z
      .number({
        message: "Method is required",
      })
      .min(1),

    trading_account: z
      .number({
        message: "Account is required",
      })
      .min(1),
    leverage: z.number().optional(),
    payment_gateway_account: z.number().optional(),
    gateway: z.string(),
  })
  .superRefine(({ gateway, payment_gateway_account }, ctx) => {
    if (gateway === "bank" && !payment_gateway_account) {
      ctx.addIssue({
        code: "custom",
        message: "Gateway is required",
        path: ["payment_gateway_account"],
      });
    }
  });

export type WithdrawalInputs = z.infer<typeof WithdrawalSchema>;

export const uploadIBDocumentSchema = z.object({
  documentName: z.string(),
  file: z
    //Rest of validations done via react dropzone
    .instanceof(File)
    .refine((file) => file.size !== 0, "Please upload an image"),
  name: z.string(),
});

export type UploadIBDocumentInputs = z.infer<typeof uploadIBDocumentSchema>;

export const IBRequestPaymentSchema = z.object({
  amount: z.number({
    message: "Amount is required",
  }),
  method: z.number({
    message: "Method is required",
  }),
});

export type IBRequestPaymentInputs = z.infer<typeof IBRequestPaymentSchema>;

export const NewTrackingLinkSchema = z.object({
  name: z
    .string({
      message: "Name is required",
    })
    .min(1, {
      message: "Name is required",
    }),
  url: z
    .string({
      message: "URL is required",
    })
    .min(1, {
      message: "URL is required",
    }),
  description: z.string({
    message: "Description is required",
  }),
  target_type: z.number({
    message: "Type is required",
  }),
  whitelabel: z.string().optional(),
});

export type NewTrackingLinkInputs = z.infer<typeof NewTrackingLinkSchema>;
