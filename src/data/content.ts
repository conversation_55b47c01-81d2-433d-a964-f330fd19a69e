export const PUBLIC_LINKS = [
  { href: "/", label: "Home" },
  { href: "/why-us", label: "Why Us" },
  { href: "/solutions", label: "Solutions" },
  { href: "/marketplace", label: "Marketplace" },
  { href: "/contact", label: "Contact" },
  { href: "/faq", label: "FAQs" },
];

export const features = [
  {
    number: "01",
    title: "Launching DLT",
    description:
      "When an FI first onboards a DLT-based KYC/AML compliance system, a client will have to complete onboarding just up of their digital profile (Client Profile).",
  },
  {
    number: "02",
    title: "Client Engagement",
    description: "",
  },
  {
    number: "03",
    title: "Separate Transaction",
    description: "",
  },
];

export const categories = [
  {
    title: "SOFTWARE, APPS & PLATFORMS",
    icon: "/placeholder.svg",
  },
  {
    title: "SPORTS & FITNESS",
    icon: "/placeholder.svg",
  },
  {
    title: "HUMANITARIAN & CHARITY ORGANIZATIONS",
    icon: "/placeholder.svg",
  },
  {
    title: "INSURANCE COMPANIES",
    icon: "/placeholder.svg",
  },
];

export const cards = [
  {
    number: "01",
    title: "Onboarded Once, Verified Everywhere",
    description:
      "Dramatically reduce operational costs associated with traditional KYC checks, saving millions annually while maintaining regulatory compliance.",
  },
  {
    number: "02",
    title: "Onboarded Once, Verified Everywhere",
    description:
      "Dramatically reduce operational costs associated with traditional KYC checks, saving millions annually while maintaining regulatory compliance.",
  },
  {
    number: "03",
    title: "Onboarded Once, Verified Everywhere",
    description:
      "Dramatically reduce operational costs associated with traditional KYC checks, saving millions annually while maintaining regulatory compliance.",
  },
];

export const faqs = [
  {
    question: "Why should I choose trustnexus?",
    answer: "",
  },
  {
    question: "Do I need to change banks?",
    answer: "",
  },
  {
    question: "How does signing up work?",
    answer: "",
  },
];

export const footerLinks = [
  { href: "#", label: "Privacy Policy" },
  { href: "#", label: "Terms and conditions" },
  { href: "#", label: "Contact" },
];

export const socialLinks = [
  { href: "#", label: "facebook" },
  { href: "#", label: "twitter" },
  { href: "#", label: "linkedin" },
  { href: "#", label: "instagram" },
];
