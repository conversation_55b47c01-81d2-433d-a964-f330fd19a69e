import { useQuery, useQueryClient } from "@tanstack/react-query";
import { APP_QUERY_KEYS } from "@/react-query/query-keys";
import { getDocumentsByTier } from "@/react-query/query/documents";
import { TieredDocumentsResponse } from "@/types/documents";

interface UseGetDocumentsByTierOptions {
  enabled?: boolean;
  refetchInterval?: number;
  refetchOnWindowFocus?: boolean;
}

export const useGetDocumentsByTier = (
  applicantId?: string,
  options?: UseGetDocumentsByTierOptions
) => {
  const queryClient = useQueryClient();

  const query = useQuery<TieredDocumentsResponse>({
    queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS_BY_TIER, applicantId],
    queryFn: () => getDocumentsByTier(applicantId!),
    enabled: !!applicantId && (options?.enabled ?? true),
    refetchInterval: options?.refetchInterval,
    refetchOnWindowFocus: options?.refetchOnWindowFocus,
  });

  // Invalidate queries helper functions
  const invalidateDocumentsByTier = async () => {
    await queryClient.invalidateQueries({
      queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS_BY_TIER, applicantId],
    });
  };

  const invalidateAllDocumentQueries = async () => {
    await queryClient.invalidateQueries({
      queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS_BY_TIER],
    });
    await queryClient.invalidateQueries({
      queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS],
    });
  };

  return {
    ...query,
    invalidateDocumentsByTier,
    invalidateAllDocumentQueries,
  };
};

// Helper function to get documents for a specific tier and company
export const useGetDocumentsForCompanyTier = (
  applicantId?: string,
  companyId?: number,
  userType?: 'individual' | 'company'
) => {
  const { data, ...rest } = useGetDocumentsByTier(applicantId);

  // Process data to match documents with company tier
  const processedData = data ? {
    ...data,
    companiesWithDocuments: data.data.tiers.map(tier => {
      const tierNumber = parseInt(tier.state.replace(/[^\d]/g, "")) || 0;
      
      return {
        tierNumber,
        tierName: tier.state,
        documents: tier.files,
        // This would be matched with company data based on their required tier
        matchesCompanyTier: (companyTier: number) => {
          return tierNumber === companyTier;
        }
      };
    })
  } : null;

  return {
    data: processedData,
    ...rest,
  };
}; 