import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createConsentAccountForCompany } from "@/react-query/query/consent";
import { toast } from "sonner";
import { APP_QUERY_KEYS } from "@/react-query/query-keys";

interface CreateConsentMutationVariables {
  companyId: number;
  userId: string | undefined;
  applicantId: string | undefined;
}

interface CreateConsentMutationResponse {
  user_id: string;
  company_id: number;
  action_date: string;
}

export const useCreateConsentAccount = () => {
  const queryClient = useQueryClient();

  return useMutation<
    CreateConsentMutationResponse,
    Error,
    CreateConsentMutationVariables
  >({
    mutationFn: async ({
      companyId,
      userId,
      applicantId,
    }: CreateConsentMutationVariables) => {
      const response = await createConsentAccountForCompany(
        companyId,
        userId,
        applicantId,
      );
      if (!response) {
        throw new Error("Failed to revoke consent.");
      }
      return { ...response };
    },
    mutationKey: [APP_QUERY_KEYS.SHARE_CONSENT],
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [APP_QUERY_KEYS.DOCUMENT_HISTORY],
      });
      toast.success("Successfully applied!");
    },
    onError: (error) => {
      console.error("Share consent error ->", error);
      toast.error(error?.message || "Failed to share consent.");
    },
  });
};
