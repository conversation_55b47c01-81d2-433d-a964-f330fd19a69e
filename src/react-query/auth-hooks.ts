import { RegistrationInputs } from "@/app/(auth)/register/register";

import { useMutation, useQuery } from "@tanstack/react-query";

import { WelcomeStep } from "@/components/auth/welcome-step";
import { UseMutationOptions } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { APP_QUERY_KEYS } from "./query-keys";
import { login, register, forgotPassword, resetPassword, changePassword } from "./query/auth";
import { getUserProfile } from "./query/user";

interface RegisterResponseData {
  user_id: string;
  email: string;
  state: string;
  registrationtype: string;
}

interface RegisterResponse {
  isSuccess: boolean;
  message: string;
  data: RegisterResponseData | null;
}

interface RegisterOptions {
  mutationOptions?: Omit<
    UseMutationOptions<RegisterResponse, Error, RegistrationInputs>,
    "mutationKey" | "mutationFn"
  >;
}

interface UserData {
  user_id: string;
  email: string;
  password_hash: string;
  signup_date: string;
  state: string;
  registrationtype: string;
  applicant_id?: string;
  name?: string;
  roles?: { role_id: number; role_name: string }[];
  companies?: {
    company_id: number;
    logo: string;
    image: string;
    description: string;
    name: string;
    category: string;
    required_individual_tier: number;
    required_corporate_tier: number;
    webhook_target_url: string;
    s3LogoUrl: string;
    s3ImageUrl: string;
  }[];
  token?: string;
}

interface LoginResponse {
  isSuccess: boolean;
  message?: string;
  data: UserData | null;
  errors?: {
    code: number;
    message: string;
    source: string;
    severity: number;
    customMessage: string;
  }[];
}

interface LoginInputs {
  email: string;
  password: string;
}

interface LoginOptions {
  mutationOptions?: Omit<
    UseMutationOptions<LoginResponse, Error, LoginInputs>,
    "mutationKey" | "mutationFn"
  >;
  redirectPath?: string;
}

interface ForgotPasswordInputs {
  email: string;
}

interface ForgotPasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: any;
}

interface ForgotPasswordOptions {
  mutationOptions?: Omit<
    UseMutationOptions<ForgotPasswordResponse, Error, ForgotPasswordInputs>,
    "mutationKey" | "mutationFn"
  >;
}

interface ResetPasswordInputs {
  token: string;
  password: string;
  password_confirm: string;
}

interface ResetPasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: any;
}

interface ResetPasswordOptions {
  mutationOptions?: Omit<
    UseMutationOptions<ResetPasswordResponse, Error, ResetPasswordInputs>,
    "mutationKey" | "mutationFn"
  >;
}

interface ChangePasswordInputs {
  password_current: string;
  password_new: string;
  password_confirm: string;
}

interface ChangePasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: any;
}

interface ChangePasswordOptions {
  mutationOptions?: Omit<
    UseMutationOptions<ChangePasswordResponse, Error, ChangePasswordInputs>,
    "mutationKey" | "mutationFn"
  >;
}

export const useRegister = (options?: RegisterOptions) => {
  const router = useRouter();
  const loginMutation = useLogin({ redirectPath: `/welcome?step=${WelcomeStep.welcome}` });
  
  return useMutation<RegisterResponse, Error, RegistrationInputs>({
    mutationKey: [APP_QUERY_KEYS.REGISTER],
    mutationFn: register,
    onSuccess: async (data, variables) => {
      console.log("Registration response", data);

      if (data.isSuccess && data.data) {
        toast.success(data.message);
        
        // Automatically authenticate user after registration
        try {
          await loginMutation.mutateAsync({
            email: variables.personalInfo.email,
            password: variables.personalInfo.password,
          });
          
          // Navigation is handled by the login mutation's onSuccess
        } catch (error) {
          console.error("Auto-login failed after registration", error);
          // Still navigate to welcome even if auto-login fails
          router.push(`/welcome?step=${WelcomeStep.welcome}`);
        }
      } else {
        toast.error(data.message || "Registration failed");
      }
    },
    onError: (error) => {
      toast.error(error?.message || "Registration failed");
    },
    ...options,
  });
};

export const useGetUserProfile = () => {
  return useQuery({
    queryKey: [APP_QUERY_KEYS.GET_PROFILE],
    queryFn: () => getUserProfile(),
  });
};

export const useLogin = (options?: LoginOptions) => {
  const router = useRouter();
  const redirectPath = options?.redirectPath || "/categories";

  return useMutation<LoginResponse, Error, LoginInputs>({
    mutationKey: [APP_QUERY_KEYS.LOGIN],
    mutationFn: login,
    onSuccess: (data) => {
      console.log("LOGIN DATA", data);

      if (data.isSuccess && data.data) {
        console.log("Login successful", data);
        toast.success("Login successful!");
        router.push(redirectPath);
      } else {
        toast.error(data.message || "Login failed!");
      }
    },
    onError: (error) => {
      console.log(error);
      toast.error(error?.message || "An error occurred during login.");
    },
    ...options?.mutationOptions,
  });
};

export const useForgotPassword = (options?: ForgotPasswordOptions) => {
  return useMutation<ForgotPasswordResponse, Error, ForgotPasswordInputs>({
    mutationKey: [APP_QUERY_KEYS.FORGOT_PASSWORD],
    mutationFn: forgotPassword,
    onSuccess: (data) => {
      console.log("FORGOT PASSWORD DATA", data);

      if (data.isSuccess) {
        console.log("Forgot password successful", data);
        toast.success(data.message || "Password reset link sent successfully!");
      } else {
        toast.error(data.message || "Failed to send password reset link!");
      }
    },
    onError: (error) => {
      console.log(error);
      toast.error(error?.message || "An error occurred while sending reset link.");
    },
    ...options?.mutationOptions,
  });
};

export const useResetPassword = (options?: ResetPasswordOptions) => {
  return useMutation<ResetPasswordResponse, Error, ResetPasswordInputs>({
    mutationKey: [APP_QUERY_KEYS.RESET_PASSWORD],
    mutationFn: resetPassword,
    onSuccess: (data) => {
      console.log("RESET PASSWORD DATA", data);

      if (data.isSuccess) {
        console.log("Reset password successful", data);
        toast.success(data.message || "Password reset successfully!");
      } else {
        toast.error(data.message || "Failed to reset password!");
      }
    },
    onError: (error) => {
      console.log(error);
      toast.error(error?.message || "An error occurred while resetting password.");
    },
    ...options?.mutationOptions,
  });
};

export const useChangePassword = (options?: ChangePasswordOptions) => {
  return useMutation<ChangePasswordResponse, Error, ChangePasswordInputs>({
    mutationKey: [APP_QUERY_KEYS.CHANGE_PASSWORD],
    mutationFn: changePassword,
    ...options?.mutationOptions,
  });
};
