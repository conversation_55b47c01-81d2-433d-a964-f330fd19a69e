import { useQuery, UseQueryOptions } from "@tanstack/react-query";
import { getUserCookies } from "@/utils/get-token";
import { getConsentCompanyForUser } from "@/react-query/query/consent";
import { Company } from "@/types/company";
import { APP_QUERY_KEYS } from "@/react-query/query-keys";

interface FetchDocumentHistoryOptions {
  queryOptions?: Omit<
    UseQueryOptions<Company[], Error>, // API response and error types
    "queryKey" | "queryFn"
  >;
}

export const useFetchDocumentHistory = ({
  queryOptions,
}: FetchDocumentHistoryOptions = {}) => {
  const fetchDocumentHistory = async (): Promise<Company[]> => {
    const { userId } = await getUserCookies();
    if (!userId) {
      throw new Error("User not logged in. Please authenticate.");
    }
    return getConsentCompanyForUser(userId);
  };

  return useQuery<Company[], Error>({
    queryKey: [APP_QUERY_KEYS.DOCUMENT_HISTORY],
    queryFn: fetchDocumentHistory,
    ...queryOptions,
  });
};
