import {
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from "@tanstack/react-query";
import { getUserCookies } from "@/utils/get-token";
import { toast } from "sonner";
import { revokeConsentAccountForCompany } from "@/react-query/query/consent";
import { APP_QUERY_KEYS } from "@/react-query/query-keys";

interface RevokeConsentMutationResponse {
  success: boolean;
}

interface RevokeConsentMutationOptions {
  mutationOptions?: Omit<
    UseMutationOptions<RevokeConsentMutationResponse, Error, number>, // Adjust types based on mutation
    "mutationKey" | "mutationFn"
  >;
}

export const useRevokeConsent = ({
  mutationOptions,
}: RevokeConsentMutationOptions = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      companyId: number,
    ): Promise<RevokeConsentMutationResponse> => {
      const { userId } = await getUserCookies();
      if (!userId) {
        throw new Error("User not logged in. Please authenticate.");
      }

      const success = await revokeConsentAccountForCompany(userId, companyId);
      if (!success) {
        throw new Error("Failed to revoke consent.");
      }
      return { success }; // Return mutation response
    },
    mutationKey: [APP_QUERY_KEYS.REVOKE_CONSENT], // Define a consistent mutation key
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: [APP_QUERY_KEYS.DOCUMENT_HISTORY],
      });
      toast.success("Consent successfully revoked!");
    },
    onError: (error) => {
      console.error("Revoke consent error ->", error);
      toast.error(error?.message || "Failed to revoke consent.");
    },
    ...mutationOptions, // Override default handlers if needed
  });
};
