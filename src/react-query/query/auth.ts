"use server";

import { RegistrationInputs } from "@/app/(auth)/register/register";
import { handleErrorResponse } from "@/utils/error-handling";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { EncryptJWT, jwtDecrypt } from "jose";

// Setup encryption secret - decode the hex string to bytes
function hexToBytes(hex: string) {
  const bytes = new Uint8Array(Math.floor(hex.length / 2));
  for (let i = 0; i < bytes.length; i++) {
    bytes[i] = parseInt(hex.substring(i * 2, i * 2 + 2), 16);
  }
  return bytes;
}

// Get secret key from environment variable
const secret = hexToBytes(process.env.AUTH_SECRET || "");

// Session types
interface SessionData {
  [key: string]: any;
  token: string;
  expiresAt: number;
  userId?: string;
}

async function encryptSession(session: SessionData) {
  const jwt = await new EncryptJWT(session)
    .setProtectedHeader({ alg: "dir", enc: "A256GCM" })
    .setIssuedAt()
    .setExpirationTime("7d")
    .encrypt(secret);

  return jwt;
}

async function decryptSession(token: string): Promise<SessionData> {
  const { payload } = await jwtDecrypt(token, secret);
  return payload as SessionData;
}

export async function getSession() {
  const cookiesStore = await cookies();
  const sessionToken = cookiesStore.get("session")?.value;
  
  if (!sessionToken) return null;

  try {
    const session = await decryptSession(sessionToken);
    return {
      ...session,
      isExpired: Date.now() > session.expiresAt,
    };
  } catch (error) {
    console.error("Failed to decrypt session:", error);
    return null;
  }
}

export async function refreshAccessToken(fromMiddleware = false) {
  try {
    const session = await getSession();
    const cookiesStore = await cookies();
    const existingToken = cookiesStore.get("token")?.value;
    
    if (!session || !session.token || !session.userId) {
      if (!fromMiddleware) {
        await logout();
      }
      return {
        isSuccess: false,
        message: "No session data available for refresh",
        data: null,
        shouldLogout: true
      };
    }
    
    // Check if token is expired
    if (Date.now() > session.expiresAt) {
      try {
        // Use the refresh token endpoint with the stored userId
        const headers: HeadersInit = {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "69420",
        };
        
        if (existingToken) {
          headers["Authorization"] = `Bearer ${existingToken}`;
        }
        
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/Token/refresh-token`,
          {
            headers,
            method: "POST",
            body: JSON.stringify({
              user_id: session.userId,
            }),
          },
        );
        
        if (!response?.ok) {
          if (!fromMiddleware) {
            await logout();
          }
          return {
            isSuccess: false,
            message: "Session expired, please log in again",
            data: null,
            shouldLogout: true
          };
        }
        
        const data = await response.json();
        
        if (data?.isSuccess && data?.data) {
          const userData = data.data;
          
          // Remove 'Bearer ' prefix if it exists
          let newToken = userData.token || "";
          if (newToken.startsWith("Bearer ")) {
            newToken = newToken.substring(7);
          }
          
          // Parse token expiration if possible
          const expiration = getTokenExpiration(newToken);
          const expiresIn = expiration ? (expiration * 1000 - Date.now()) / 1000 : 1800;
          
          // Update session with new token
          await setEncryptedSessionCookie({
            token: newToken,
            expiresIn: expiresIn,
            userId: session.userId,
          });
          
          // Update the token cookie for backward compatibility
          const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
          cookiesStore.set("token", newToken, {
            httpOnly: true,
            secure: true,
            expires: expires,
            sameSite: "lax",
            path: "/",
          });
          
          return {
            isSuccess: true,
            message: "Token refreshed",
            data: userData,
            shouldLogout: false
          };
        }
        
        if (!fromMiddleware) {
          await logout();
        }
        return {
          isSuccess: false,
          message: "Token refresh failed",
          data: null,
          shouldLogout: true
        };
      } catch (error) {
        if (!fromMiddleware) {
          await logout();
        }
        return {
          isSuccess: false,
          message: "Token refresh failed due to network error",
          data: null,
          shouldLogout: true
        };
      }
    }
    
    // Token is still valid
    return {
      isSuccess: true,
      message: "Token is still valid",
      data: { token: session.token },
      shouldLogout: false
    };
  } catch (error: any) {
    if (!fromMiddleware) {
      await logout();
    }
    return {
      isSuccess: false,
      message: error?.message || "Token refresh failed",
      data: null,
      shouldLogout: true
    };
  }
}

async function setEncryptedSessionCookie({
  token,
  expiresIn,
  userId,
}: {
  token: string;
  expiresIn: number;
  userId?: string;
}) {
  const expiresAt = Date.now() + expiresIn * 1000;
  
  const sessionData: SessionData = { 
    token,
    expiresAt,
  };
  
  if (userId) sessionData.userId = userId;
  
  const encrypted = await encryptSession(sessionData);

  const cookiesStore = await cookies();
  cookiesStore.set("session", encrypted, {
    httpOnly: true,
    secure: true,
    sameSite: "lax",
    maxAge: 60 * 60 * 24 * 7, // 7 days
    path: "/",
  });
}

export async function maybeRefreshToken(fromMiddleware = false) {
  const session = await getSession();
  
  if (!session?.token) {
    return { isSuccess: false, shouldLogout: true };
  }

  try {
    // If token is expired or about to expire (within 5 minutes), refresh it
    if (Date.now() > session.expiresAt - 300000) {
      return await refreshAccessToken(fromMiddleware);
    }
    
    return {
      isSuccess: true,
      message: "Token is still valid",
      data: { token: session.token },
      shouldLogout: false
    };
  } catch (error) {
    console.error("Token refresh check failed:", error);
    return { isSuccess: false, shouldLogout: true };
  }
}

function getTokenExpiration(token: string): number | null {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp || null;
  } catch (error) {
    console.error("Failed to parse token:", error);
    return null;
  }
}

interface RegisterResponseData {
  user_id: string;
  email: string;
  state: string;
  registrationtype: string;
}

interface RegisterResponse {
  isSuccess: boolean;
  message: string;
  data: RegisterResponseData | null;
}

export const register = async (
  inputs: RegistrationInputs,
): Promise<RegisterResponse> => {
  try {
    const payload = {
      registrationType: inputs.registrationType.type,
      firstName: inputs.personalInfo.firstName,
      lastName: inputs.personalInfo.lastName,
      email: inputs.personalInfo.email,
      password: inputs.personalInfo.password,
      confirmPassword: inputs.personalInfo.confirmPassword,
    };
    
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Accounts`,
      {
        headers: {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "69420",
        },
        method: "POST",
        body: JSON.stringify(payload),
      },
    );

    const responseJson = await response.json();

    if (!responseJson?.isSuccess) {
      return {
        isSuccess: false,
        message: responseJson?.errors?.[0]?.message || "Registration failed",
        data: null,
      };
    }

    if (responseJson?.data) {
      const responseData = responseJson.data as RegisterResponseData;
      const cookiesStore = await cookies();
      const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      cookiesStore.set("user_id", responseData.user_id, {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("email", responseData.email, {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("state", responseData.state, {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("registrationtype", responseData.registrationtype, {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });

      return {
        isSuccess: true,
        message: "Registration successful",
        data: responseData,
      };
    }

    return {
      isSuccess: false,
      message: "Registration failed - no data received",
      data: null,
    };
  } catch (error: any) {
    console.log("Error during registration", error);

    return {
      isSuccess: false,
      message: error?.message ?? "Something went wrong while registering",
      data: null,
    };
  }
};

interface LoginInputs {
  email: string;
  password: string;
}

interface UserData {
  user_id: string;
  email: string;
  password_hash: string;
  signup_date: string;
  state: string;
  registrationtype: string;
  applicant_id?: string;
  name?: string;
  roles?: { role_id: number; role_name: string }[];
  companies?: {
    company_id: number;
    logo: string;
    image: string;
    description: string;
    name: string;
    category: string;
    required_individual_tier: number;
    required_corporate_tier: number;
    webhook_target_url: string;
    s3LogoUrl: string;
    s3ImageUrl: string;
  }[];
  token?: string;
  refreshToken?: string;
}

interface LoginResponse {
  isSuccess: boolean;
  message?: string;
  data: UserData | null;
  errors?: {
    code: number;
    message: string;
    source: string;
    severity: number;
    customMessage: string;
  }[];
}

export const login = async (payload: LoginInputs): Promise<LoginResponse> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Token`,
      {
        headers: {
          "Content-Type": "application/json",
        },
        method: "POST",
        body: JSON.stringify({
          email: payload.email,
          password: payload.password,
        }),
      },
    );

    const data = await response.json();
    console.log("Login response", data);
    
    if (!response?.ok) {
      return {
        isSuccess: false,
        message: data?.errors?.[0]?.message || `Login failed: ${response.statusText}`,
        data: null,
        errors: data?.errors || [{
          code: response.status,
          message: response.statusText,
          source: "API",
          severity: 1,
          customMessage: "Login request failed"
        }]
      };
    }

    if (data?.isSuccess && data?.data) {
      const userData = data.data;
      const cookiesStore = await cookies();
      const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      // Remove 'Bearer ' prefix from token if it exists
      let tokenValue = userData.token || "";
      if (tokenValue.startsWith("Bearer ")) {
        tokenValue = tokenValue.substring(7);
      }
      
      // Parse token expiration if possible
      const expiration = getTokenExpiration(tokenValue);
      const expiresIn = expiration ? (expiration * 1000 - Date.now()) / 1000 : 1800;
      
      await setEncryptedSessionCookie({
        token: tokenValue,
        expiresIn: expiresIn,
        userId: userData.user_id,
      });

      // Also keep existing cookie storage for backward compatibility
      cookiesStore.set("user_id", userData.user_id || "", {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("email", userData.email || "", {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("state", userData.state || "", {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("registrationtype", userData.registrationtype || "", {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("applicant_id", userData.applicant_id || "", {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });
      cookiesStore.set("token", tokenValue, {
        httpOnly: true,
        secure: true,
        expires: expires,
        sameSite: "lax",
        path: "/",
      });

      return {
        isSuccess: true,
        message: "Login successful",
        data: userData,
      };
    }

    return {
      isSuccess: false,
      message: data?.errors?.[0]?.message || "Invalid credentials",
      data: null,
      errors: data?.errors,
    };
  } catch (error: any) {
    console.error("Error during login", error);

    return {
      isSuccess: false,
      message: error?.message || "Login failed",
      data: null,
    };
  }
};

export const logout = async () => {
  try {
  const cookiesStore = await cookies();
  
  // Delete all auth cookies
  cookiesStore.delete("session");
  cookiesStore.delete("user_id");
  cookiesStore.delete("email");
  cookiesStore.delete("state");
  cookiesStore.delete("registrationtype");
  cookiesStore.delete("applicant_id");
  cookiesStore.delete("token");
  
  redirect("/");
  } catch (error) {
    console.error("Logout error:", error);
    redirect("/");
  }
};

// Forgot Password Types and Function
interface ForgotPasswordInputs {
  email: string;
}

interface ForgotPasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: any;
}

export const forgotPassword = async (payload: ForgotPasswordInputs): Promise<ForgotPasswordResponse> => {
  try {
    // TODO: Replace with actual API endpoint when ready
    // For now, simulate API call with a delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate successful response
    return {
      isSuccess: true,
      message: "Password reset link sent successfully",
      data: null,
    };
    
    /* 
    // Actual implementation when API is ready:
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/forgot-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "ngrok-skip-browser-warning": "69420",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Failed to send reset link");
    }

    const data = await response.json();
    return data;
    */
  } catch (error: any) {
    console.error("Error during forgot password", error);
    return {
      isSuccess: false,
      message: error?.message || "Failed to send password reset link",
      data: null,
    };
  }
};

// Reset Password Types and Function
interface ResetPasswordInputs {
  token: string;
  password: string;
  password_confirm: string;
}

interface ResetPasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: any;
}

export const resetPassword = async (payload: ResetPasswordInputs): Promise<ResetPasswordResponse> => {
  try {
    // TODO: Replace with actual API endpoint when ready
    // For now, simulate API call with a delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate successful response
    return {
      isSuccess: true,
      message: "Password reset successfully",
      data: null,
    };
    
    /* 
    // Actual implementation when API is ready:
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/reset-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "ngrok-skip-browser-warning": "69420",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Failed to reset password");
    }

    const data = await response.json();
    return data;
    */
  } catch (error: any) {
    console.error("Error during reset password", error);
    return {
      isSuccess: false,
      message: error?.message || "Failed to reset password",
      data: null,
    };
  }
};

// Change Password Types and Function
interface ChangePasswordInputs {
  password_current: string;
  password_new: string;
  password_confirm: string;
}

interface ChangePasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: any;
}

export const changePassword = async (payload: ChangePasswordInputs): Promise<ChangePasswordResponse> => {
  try {
    // Get current session to get user_id and token
    const session = await getSession();
    const cookiesStore = await cookies();
    const token = cookiesStore.get("token")?.value;
    
    if (!session?.userId || !token) {
      return {
        isSuccess: false,
        message: "Authentication required. Please log in again.",
        data: null,
      };
    }

    // Map the form data to the API expected format
    const apiPayload = {
      password: payload.password_new,
      confirmPassword: payload.password_confirm,
      user_id: session.userId,
    };

    console.log("Change password API call:", {
      url: `${process.env.NEXT_PUBLIC_API_URL}/Accounts/update-password`,
      payload: { ...apiPayload, password: "[REDACTED]", confirmPassword: "[REDACTED]" }
    });

    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/Accounts/update-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "ngrok-skip-browser-warning": "69420",
        "Authorization": `Bearer ${token}`,
      },
      body: JSON.stringify(apiPayload),
    });

    console.log("Change password response status:", response.status);

    // Show error for any non-200 status
    if (response.status !== 200) {
      let errorData;
      try {
        const responseText = await response.text();
        if (responseText) {
          errorData = JSON.parse(responseText);
        } else {
          errorData = { message: `HTTP ${response.status}: ${response.statusText}` };
        }
      } catch (parseError) {
        console.error("Failed to parse error response:", parseError);
        errorData = { message: `HTTP ${response.status}: ${response.statusText}` };
      }
      
      console.error("Change password API error:", errorData);
      
      // Handle specific HTTP status codes
      let errorMessage;
      if (response.status === 405) {
        errorMessage = "API endpoint not available. Please check the server configuration.";
      } else if (response.status === 401) {
        errorMessage = "Authentication failed. Please log in again.";
      } else if (response.status === 403) {
        errorMessage = "Access denied. You don't have permission to change password.";
      } else {
        errorMessage = errorData?.errors?.[0]?.message || errorData?.message || `Server error: ${response.status}`;
      }
      
      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log("Change password API success:", data);
    
    if (data?.isSuccess) {
      return {
        isSuccess: true,
        message: "Password changed successfully",
        data: data.data,
      };
    } else {
      throw new Error(data?.errors?.[0]?.message || data?.message || "Failed to change password");
    }
  } catch (error: any) {
    console.error("Error during change password", error);
    throw error;
  }
};
