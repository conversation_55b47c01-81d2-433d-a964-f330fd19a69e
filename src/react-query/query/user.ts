import { RegistrationResponse } from "@/types/user";
import { handleErrorResponse } from "@/utils/error-handling";
import { getAuthHeaders, getUserCookies } from "@/utils/get-token";

export const getUserProfile = async () => {
  try {
    const { userId } = await getUserCookies();
    const headers = await getAuthHeaders();

    if (!userId) {
      throw new Error("No user ID found");
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Accounts/${userId}`,
      {
        headers,
        cache: "no-store",
      }
    );

    if (!response.ok) {
      return handleErrorResponse(response);
    }

    const data = (await response.json()) as RegistrationResponse;
    console.log("getUserProfile", data);

    return data.data;
  } catch (error: any) {
    throw new Error(error?.message || "Failed to fetch user profile");
  }
};
