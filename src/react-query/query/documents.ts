import { handleErrorResponse } from "@/utils/error-handling";
import { getAuthHeaders, getUserCookies } from "@/utils/get-token";
import { DocumentResponse, TieredDocumentsResponse } from "@/types/documents";

export const getDocuments = async (aplicantId: string) => {
  try {
    const { userId } = await getUserCookies();
    const headers = await getAuthHeaders();

    if (!userId) {
      throw new Error("No user ID found");
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Accounts/get-documents-for-applicantid/${aplicantId}`,
      {
        headers,
        cache: "no-store",
      },
    );

    if (!response.ok) {
      return handleErrorResponse(response);
    }

    const data = (await response.json()) as DocumentResponse;
    console.log("getDocuments", data);

    return data.data;
  } catch (error: any) {
    throw new Error(error?.message || "Failed to fetch documents");
  }
};

export const getDocumentsByTier = async (applicantId: string) => {
  try {
    const { userId } = await getUserCookies();
    const headers = await getAuthHeaders();

    if (!userId) {
      throw new Error("No user ID found");
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Accounts/get-documents-for-applicantid-all-tiers/${applicantId}`,
      {
        headers,
        cache: "no-store",
      },
    );

    if (!response.ok) {
      return handleErrorResponse(response);
    }

    const data = (await response.json()) as TieredDocumentsResponse;
    console.log("getDocumentsByTier", data);

    return data;
  } catch (error: any) {
    throw new Error(error?.message || "Failed to fetch documents by tier");
  }
};
