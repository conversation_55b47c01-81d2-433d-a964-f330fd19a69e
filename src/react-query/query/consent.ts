import { Company } from "@/types/company";
import { handleErrorResponse } from "@/utils/error-handling";
import { getAuthHeaders } from "@/utils/get-token";

interface CreateConsentAccountPayload {
  user_id: string | undefined;
  applicant_id: string | undefined;
  company_id: number;
}

interface ConsentAccountResponse {
  isSuccess: boolean;
  data: {
    user_id: string;
    company_id: number;
    action_date: string;
  };
  errors?: Array<{
    code: number;
    message: string;
    source?: string;
    severity?: number;
    customMessage?: string;
  }>;
}

export const createConsentAccountForCompany = async (
  companyId: number,
  userId: string | undefined,
  applicantId: string | undefined,
): Promise<ConsentAccountResponse["data"]> => {
  try {
    // Validate user ID
    if (!userId) {
      throw new Error("No token found");
    }

    // Define the request payload
    const payload: CreateConsentAccountPayload = {
      user_id: userId,
      company_id: companyId,
      applicant_id: applicantId,
    };

    // Get auth headers
    const headers = await getAuthHeaders();

    // Perform the API request
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Accounts/create-consent-account-company`,
      {
        method: "POST",
        headers: {
          ...headers,
          "Content-Type": "application/json-patch+json",
        },
        body: JSON.stringify(payload),
        cache: "no-store",
      },
    );

    // Handle non-200 HTTP responses
    if (!response.ok) {
      return handleErrorResponse(response);
    }

    // Parse the response body
    const data = (await response.json()) as ConsentAccountResponse;
    console.log("createConsentAccountForCompany", data);

    // Check if the operation was successful
    if (!data.isSuccess) {
      throw new Error("Consent account creation failed.");
    }

    return data.data;
  } catch (error: any) {
    throw new Error(error?.message || "Failed to create consent account.");
  }
};

interface ConsentCompanyResponse {
  isSuccess: boolean;
  data: Company[];
  errors?: Array<{
    code: number;
    message: string;
    source?: string;
    severity?: number;
    customMessage?: string;
  }>;
}

export const getConsentCompanyForUser = async (
  userId: string,
): Promise<Company[]> => {
  try {
    // Validate user ID
    if (!userId) {
      throw new Error("No user ID provided");
    }

    // Get auth headers
    const headers = await getAuthHeaders();

    // Perform the API request
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Accounts/get-consent-company-for-userid/${userId}`,
      {
        method: "GET",
        headers: {
          ...headers,
          accept: "text/plain",
        },
        cache: "no-store",
      },
    );

    // Handle non-200 HTTP responses
    if (!response.ok) {
      return handleErrorResponse(response);
    }

    // Parse the response body
    const data = (await response.json()) as ConsentCompanyResponse;
    console.log("getConsentCompanyForUser", data);

    // Check if the operation was successful
    if (!data.isSuccess) {
      throw new Error("Failed to retrieve consent company data.");
    }

    return data.data; // Return the list of consent companies for the user
  } catch (error: any) {
    throw new Error(
      error?.message || "Failed to fetch consent company information.",
    );
  }
};

interface RevokeConsentPayload {
  user_id: string;
  company_id: number;
}

interface RevokeConsentResponse {
  isSuccess: boolean;
  data: boolean;
  errors?: Array<{
    code: number;
    message: string;
    source?: string;
    severity?: number;
    customMessage?: string;
  }>;
}

export const revokeConsentAccountForCompany = async (
  userId: string,
  companyId: number,
): Promise<boolean> => {
  try {
    // Validate input parameters
    if (!userId || !companyId) {
      throw new Error("Invalid input. User ID and Company ID are required.");
    }

    // Define the request payload
    const payload: RevokeConsentPayload = {
      user_id: userId,
      company_id: companyId,
    };

    // Get auth headers
    const headers = await getAuthHeaders();

    // Perform the API request
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/Accounts/revoke-consent-account-company`,
      {
        method: "POST",
        headers: {
          ...headers,
          "Content-Type": "application/json-patch+json",
        },
        body: JSON.stringify(payload),
        cache: "no-store",
      },
    );

    // Handle non-200 HTTP responses
    if (!response.ok) {
      return handleErrorResponse(response);
    }

    // Parse and return the response
    const data = (await response.json()) as RevokeConsentResponse;
    console.log("revokeConsentAccountForCompany", data);

    // Check if the operation was successful
    if (!data.isSuccess) {
      throw new Error("Failed to revoke consent account for company.");
    }

    return data.data;
  } catch (error: any) {
    throw new Error(
      error?.message ||
        "An error occurred while revoking consent for the company.",
    );
  }
};
