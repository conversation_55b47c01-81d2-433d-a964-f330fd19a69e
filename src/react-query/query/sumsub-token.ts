"use server";

import { SUMSUB_LEVEL } from "@/types/user";
import { getAuthHeaders, getUserId } from "@/utils/get-token";
import { createHmac } from "node:crypto";

type AccessTokenResponse = {
  token: string;
  expiresIn: number;
};

const SUMSUB_BASE_URL = "https://api.sumsub.com";
const DEFAULT_TTL_SECS = 600;

function generateSignature(
  timestamp: string,
  method: string,
  url: string,
): string {
  return createHmac("sha256", process.env.SUMSUB_SECRET!)
    .update(timestamp + method + url)
    .digest("hex");
}

async function createSignedRequest(url: string, method: string = "POST") {
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const signature = generateSignature(timestamp, method, url);

  return {
    method,
    headers: {
      Accept: "application/json",
      "X-App-Token": process.env.SUMSUB_TOKEN!,
      "X-App-Access-Ts": timestamp,
      "X-App-Access-Sig": signature,
    },
  };
}

async function createAccessToken(
  externalUserId: string,
  levelName: string,
  ttlInSecs: number = DEFAULT_TTL_SECS,
): Promise<AccessTokenResponse> {
  const url = `/resources/accessTokens?userId=${encodeURIComponent(externalUserId)}&ttlInSecs=${ttlInSecs}&levelName=${encodeURIComponent(levelName)}`;
  const requestConfig = await createSignedRequest(url);

  const response = await fetch(`${SUMSUB_BASE_URL}${url}`, requestConfig);
  console.log("response", response);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return response.json();
}

export async function getSumSubToken(levelName: SUMSUB_LEVEL) {
  const externalUserId = await getUserId();
  const headers = await getAuthHeaders();

  console.log("External user", externalUserId);
  if (!externalUserId) {
    handleErrorResponse({ message: "User not found" });
  }

  try {
    const url = `${process.env.NEXT_PUBLIC_API_URL}/Sumsub/get-tier-sdk-userid-level-token?userId=${externalUserId}&levelName=${levelName}`;
    console.log("URL", url);
    const response = await fetch(url, {
      headers,
    });

    const data = await response.json();
    const jsonData = JSON.parse(data.data);
    console.log("jsonData", jsonData);
    return jsonData?.token || null;
  } catch (error: any) {
    return handleErrorResponse(error);
  }
}

function handleErrorResponse(error: any) {
  if (error instanceof Response) {
    console.error("Error response:", error.statusText);
  } else {
    console.error("Error:", error.message);
  }
}
