import { useQuery } from "@tanstack/react-query";
import { getAuthHeaders } from "@/utils/get-token";
import { NotificationDto } from "@/types/notification";
import { APP_QUERY_KEYS } from "./query-keys";

export const useGetUserNotifications = (userId: string | null) => {
  return useQuery({
    queryKey: [APP_QUERY_KEYS.GET_NOTIFICATIONS, userId],
    queryFn: async (): Promise<NotificationDto[]> => {
      if (!userId) {
        throw new Error("User ID is required");
      }

      const headers = await getAuthHeaders();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/Notifications/user/${userId}`,
        {
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data || [];
    },
    enabled: !!userId,
  });
}; 