import { useState, useEffect } from "react";
import { handleErrorResponse } from "@/utils/error-handling";
import { getAuthHeaders } from "@/utils/get-token";

interface Company {
  s3LogoUrl: string | undefined;
  company_id: number;
  logo: string;
  image: string;
  description: string;
  name: string;
  category: string;
  required_individual_tier: number;
  required_corporate_tier: number;
  webhook_target_url: string;
}

interface FetchCompaniesResponse {
  isSuccess: boolean;
  data: {
    pageSize: number;
    pageNumber: number;
    totalPages: number;
    totalCount: number;
    result: Company[];
  };
  errors: null | string[];
}

const API_URL = process.env.NEXT_PUBLIC_API_URL!;

export const useFetchCompanies = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCompanies = async () => {
      setLoading(true);
      setError(null);

      try {
        const headers = await getAuthHeaders();
        
        const response = await fetch(`${API_URL}/Company`, {
          method: "GET",
          headers: {
            ...headers,
            Accept: "text/plain",
          },
        });

        if (!response.ok) {
          return handleErrorResponse(response);
        }

        const data: FetchCompaniesResponse = await response.json();

        if (data.isSuccess) {
          setCompanies(data.data.result);
        } else {
          setError("Failed to fetch companies.");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error");
      } finally {
        setLoading(false);
      }
    };

    fetchCompanies();
  }, []);

  return { companies, loading, error };
};
