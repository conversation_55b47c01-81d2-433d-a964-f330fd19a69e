import { useState, useEffect, useCallback } from 'react';
import notificationService from '../services/notificationService';

type Notification = {
  message: string;
  title: string;
  type: string;
  timestamp: Date;
};

export const useSignalR = (baseUrl: string, userId: string | null) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [currentInterviewNotification, setCurrentInterviewNotification] = useState<Notification | null>(null);
  const [isInterviewModalOpen, setIsInterviewModalOpen] = useState(false);

  useEffect(() => {
    if (!userId) return;
    
    const connectToSignalR = async () => {
      try {
        await notificationService.startConnection(baseUrl, userId);
        setIsConnected(true);
      } catch (error) {
        console.error('Failed to connect to SignalR:', error);
        setIsConnected(false);
      }
    };

    connectToSignalR();

    notificationService.onNotification((notification) => {
      console.log('New notification received:', notification);
      
      // Add to notifications list
      setNotifications(prev => [notification, ...prev]);
      
      // Check if it's an interview notification and show modal
      if (notification.type === 'interview') {
        setCurrentInterviewNotification(notification);
        setIsInterviewModalOpen(true);
      }
    });

    return () => {
      notificationService.stopConnection();
      notificationService.removeNotificationHandler();
    };
  }, [baseUrl, userId]);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const closeInterviewModal = useCallback(() => {
    setIsInterviewModalOpen(false);
    setCurrentInterviewNotification(null);
  }, []);

  const handleAcceptInterview = useCallback(async () => {
    // TODO: Implement API call for accepting interview
    console.log('Interview accepted:', currentInterviewNotification);
    // This should make an API call to accept the interview
    // For now, we'll just close the modal
    closeInterviewModal();
  }, [currentInterviewNotification, closeInterviewModal]);

  const handleRejectInterview = useCallback(async () => {
    // TODO: Implement API call for rejecting interview
    console.log('Interview rejected:', currentInterviewNotification);
    // This should make an API call to reject the interview
    // For now, we'll just close the modal
    closeInterviewModal();
  }, [currentInterviewNotification, closeInterviewModal]);

  return { 
    notifications,
    isConnected,
    clearNotifications,
    // Interview modal state
    currentInterviewNotification,
    isInterviewModalOpen,
    closeInterviewModal,
    handleAcceptInterview,
    handleRejectInterview
  };
};
