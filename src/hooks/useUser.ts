"use client";

import { useQuery } from "@tanstack/react-query";
import { getUserProfile } from "../react-query/query/user";
import { APP_QUERY_KEYS } from "../react-query/query-keys";
import { User } from "@/types/user";

export const useUser = () => {
  const {
    data: user,
    isLoading,
    error,
    isError,
  } = useQuery<User>({
    queryKey: [APP_QUERY_KEYS.GET_PROFILE],
    queryFn: getUserProfile,
    retry: false,
    staleTime: 300000, // 5 minutes
  });

  return {
    user,
    isLoading,
    isError,
    error,
    isAuthenticated: !!user?.user_id,
  };
};
