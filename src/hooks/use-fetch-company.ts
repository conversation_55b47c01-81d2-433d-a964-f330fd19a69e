import { useEffect, useState } from "react";
import { Company } from "@/types/company";
import { api } from "@/utils/axios-utils";

interface CompanyResponse {
  isSuccess: boolean;
  data: Company;
  errors?: Array<any>;
}

const API_URL = process.env.NEXT_PUBLIC_API_URL!;

export function useFetchCompany(companyId: string | Array<string> | undefined) {
  const [companyData, setCompanyData] = useState<Company | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (companyId) {
      const fetchCompanyData = async () => {
        setLoading(true);
        setError(null);

        try {
          // Using the api utility which automatically includes auth headers
          const response = await api.get<CompanyResponse>(`/Company/${companyId}`);
          setCompanyData(response.data); // Set company data from the API response
        } catch (err) {
          console.error("Error fetching company data", err);
          setError("Failed to load company data");
        } finally {
          setLoading(false);
        }
      };

      fetchCompanyData();
    }
  }, [companyId]);

  return { companyData, loading, error };
}
