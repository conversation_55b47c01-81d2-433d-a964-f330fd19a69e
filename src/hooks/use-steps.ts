"use client";

import { useEffect, useState } from "react";
import { Step, StepName } from "../../mocks/steps";

export const useSteps = (
  data: {
    stepNames: StepName[];
    steps: Step[];
  },
  userProgress?: {
    [key: string]: unknown;
  }
) => {
  const [currentStep, setCurrentStep] = useState(
    () => (userProgress?.step_index as number) || 0
  );
  const [stepNames, setStepNames] = useState<
    | {
        name: string;
        finished: boolean;
      }[]
    | undefined
  >(data?.stepNames);

  useEffect(() => {
    setStepNames(data?.stepNames);
  }, [data?.stepNames]);

  const allFields = data?.steps;
  const defaultValues = userProgress
    ? { ...userProgress }
    : allFields?.reduce((acc, curr) => {
        acc[curr.id] = curr.fields.reduce((acc, curr) => {
          acc[curr.id] = curr.type === "string" ? "" : undefined;
          return acc;
        }, {});
        return acc;
      }, {});

  const handleNextStep = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const handlePreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const currentStepData = data?.steps[currentStep] ?? [];
  const isLastStep = currentStep === (data?.steps?.length ?? 0) - 1;

  // Get the name of the current step
  const currentStepName = stepNames?.[currentStep]?.name ?? ``;
  const nextStepName = stepNames?.[currentStep + 1]?.name ?? ``;

  return {
    stepNames,
    isLastStep,
    currentStepData,
    currentStep,
    currentStepName, // Add the current step name here
    nextStepName,
    setCurrentStep,
    defaultValues,
    handleNextStep,
    handlePreviousStep,
  };
};
