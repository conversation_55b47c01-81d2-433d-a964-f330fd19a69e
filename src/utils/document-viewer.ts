/**
 * Utility functions for handling document viewing across different browsers and devices
 */

/**
 * Detects if the current browser is Safari
 */
export const isSafari = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const ua = window.navigator.userAgent.toLowerCase();
  const isSafariBrowser = ua.includes('safari') && !ua.includes('chrome') && !ua.includes('android');
  
  // Additional check for iOS Safari
  const isIOSSafari = /iP(ad|od|hone)/i.test(window.navigator.userAgent) && 
    /WebKit/i.test(window.navigator.userAgent) && 
    !/(CriOS|FxiOS|OPiOS|mercury)/i.test(window.navigator.userAgent);
  
  return isSafariBrowser || isIOSSafari;
};

/**
 * Detects if the current device is mobile
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent) || 
    window.innerWidth < 768;
};

/**
 * Checks if the browser can display PDFs inline
 */
export const canDisplayPDFInline = (): boolean => {
  // Most modern desktop browsers support inline PDF viewing
  // Mobile browsers and some specific cases don't
  if (isMobileDevice()) return false;
  
  // Safari on macOS sometimes has issues with certain PDF URLs
  if (isSafari()) {
    // Check if it's desktop Safari (which usually works)
    return !isMobileDevice();
  }
  
  return true;
};

/**
 * Prepares a URL for PDF viewing with appropriate parameters
 */
export const preparePDFUrl = (url: string): string => {
  // Remove any existing hash parameters
  const baseUrl = url.split('#')[0];
  
  // Add parameters for better compatibility
  // toolbar=1 shows the PDF toolbar
  // navpanes=0 hides the navigation panes
  // scrollbar=1 shows the scrollbar
  return `${baseUrl}#toolbar=1&navpanes=0&scrollbar=1&view=FitH`;
};

/**
 * Handles opening a document with fallback strategies
 */
export const openDocument = (
  url: string, 
  fileName: string,
  options?: {
    onSuccess?: () => void;
    onError?: (error: Error) => void;
    forceNewTab?: boolean;
  }
) => {
  const { onSuccess, onError, forceNewTab = false } = options || {};
  
  try {
    if (forceNewTab || !canDisplayPDFInline()) {
      // Open in new tab for mobile or when forced
      window.open(url, '_blank', 'noopener,noreferrer');
      onSuccess?.();
    } else {
      // For desktop, return prepared URL for iframe/embed viewing
      const preparedUrl = preparePDFUrl(url);
      onSuccess?.();
      return preparedUrl;
    }
  } catch (error) {
    console.error('Error opening document:', error);
    onError?.(error as Error);
  }
};

/**
 * Downloads a document
 */
export const downloadDocument = (url: string, fileName: string) => {
  try {
    // Create a temporary anchor element
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.target = '_blank';
    
    // Some browsers require the link to be in the DOM
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading document:', error);
    // Fallback to opening in new tab
    window.open(url, '_blank');
  }
};

/**
 * Checks if a URL is from S3 and might have CORS issues
 */
export const isS3Url = (url: string): boolean => {
  return url.includes('s3.amazonaws.com') || url.includes('s3-') || url.includes('.s3.');
};

/**
 * Adds CORS proxy if needed (only for development)
 */
export const addCorsProxyIfNeeded = (url: string): string => {
  // Only use in development and if it's an S3 URL
  if (process.env.NODE_ENV === 'development' && isS3Url(url)) {
    // You can use a CORS proxy service here if needed
    // Example: return `https://cors-anywhere.herokuapp.com/${url}`;
    return url;
  }
  return url;
}; 