"use client";

import axios, { AxiosInstance, AxiosRequestConfig } from "axios";
import { getAuthHeaders } from "./get-token";

// Create a base API client instance with default settings
const createAxiosClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    headers: {
      "Content-Type": "application/json",
      "ngrok-skip-browser-warning": "69420",
    },
  });

  // Add a request interceptor to include auth token
  client.interceptors.request.use(
    async (config) => {
      // Clone the config to avoid mutating the original
      const newConfig = { ...config };
      
      try {
        // Get authentication headers
        const headers = await getAuthHeaders();
        
        // Add authorization header if it exists
        if (headers["Authorization"]) {
          newConfig.headers = newConfig.headers || {};
          newConfig.headers["Authorization"] = headers["Authorization"];
        }
      } catch (error) {
        console.error("Error adding auth token to request:", error);
      }
      
      return newConfig;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  
  return client;
};

// Export a singleton instance
export const axiosClient = createAxiosClient();

// Helper functions for API calls
export const api = {
  get: <T>(url: string, config?: AxiosRequestConfig) => 
    axiosClient.get<T>(url, config).then(response => response.data),
    
  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    axiosClient.post<T>(url, data, config).then(response => response.data),
    
  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    axiosClient.put<T>(url, data, config).then(response => response.data),
    
  delete: <T>(url: string, config?: AxiosRequestConfig) => 
    axiosClient.delete<T>(url, config).then(response => response.data),
}; 