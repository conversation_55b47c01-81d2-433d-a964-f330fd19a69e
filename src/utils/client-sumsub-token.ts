import { SUMSUB_LEVEL } from "@/types/user";
import { getAuthHeaders, getUserId } from "@/utils/get-token";

/**
 * Client-side function to get SumSub token with optional custom userId (for company beneficiaries)
 * This function can be used from client components to handle interview tokens for company users
 */
export async function getClientSumSubToken(levelName: SUMSUB_LEVEL, customUserId: string, beneficiaryApplicantId: string) {
  try {
    // Use provided userId or fallback to getting from cookies
    const externalUserId = customUserId
    const headers = await getAuthHeaders();

    console.log("External user ID for client token:", externalUserId);
    
    if (!externalUserId) {
      throw new Error("User not found");
    }

    const url = `${process.env.NEXT_PUBLIC_API_URL}/Sumsub/get-tier-sdk-userId-applicantId-level-token?userId=${externalUserId}&levelName=${levelName}&applicantId=${beneficiaryApplicantId}`;
    console.log("Client SumSub URL:", url);
    
    const response = await fetch(url, {
      headers,
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const jsonData = JSON.parse(data.data);
    console.log("Client SumSub token data:", jsonData);
    
    return jsonData?.token || null;
  } catch (error: any) {
    console.error("Client SumSub token error:", error);
    throw new Error(error?.message || "Failed to get SumSub token");
  }
} 