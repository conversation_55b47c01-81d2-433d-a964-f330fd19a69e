"use server";
import { RegistrationType } from "@/types/user";
import { cookies } from "next/headers";
import { maybeRefreshToken } from "@/react-query/query/auth";

export const getUserId = async () => {
  const cookieStore = await cookies();
  const userId = cookieStore.get("user_id");
  return userId?.value;
};

export const getEmail = async () => {
  const cookieStore = await cookies();
  const email = cookieStore.get("email");
  return email?.value;
};

export const getUserLevel = async () => {
  const cookieStore = await cookies();
  const level = cookieStore.get("access_level");
  return level?.value;
};

export const getToken = async () => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token");
  return token?.value;
};

export const isTokenExpired = async (token: string | undefined): Promise<boolean> => {
  if (!token) return true;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expTimestamp = payload.exp * 1000; 
    const currentTime = Date.now();
    
    return currentTime > expTimestamp - 300000;
  } catch (error) {
    return true;
  }
};

export const getAuthHeaders = async () => {
  let token = await getToken();
  const userId = await getUserId();
  
  if (token && userId && await isTokenExpired(token)) {
    const refreshResult = await maybeRefreshToken();
    
    const hasNewToken = refreshResult && 'data' in refreshResult && !!refreshResult.data?.token;
    const shouldLogout = refreshResult && 'shouldLogout' in refreshResult && refreshResult.shouldLogout;
    
    if (refreshResult?.isSuccess && hasNewToken && 'data' in refreshResult) {
      token = refreshResult.data.token;
    } else if (shouldLogout) {
      return {
        "Content-Type": "application/json",
        "ngrok-skip-browser-warning": "69420",
      };
    }
  }
  
  const headers: HeadersInit = {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": "69420",
  };
  
  if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  }
  
  return headers;
};

export const isRegistered = async () => {
  const userId = await getUserId();
  const email = await getEmail();
  return userId && email;
};

export const isAuthenticated = async () => {
  const userId = await getUserId();
  const email = await getEmail();
  const token = await getToken();

  return userId && email && token;
};

export const getUserCookies = async () => {
  const cookieStore = await cookies();
  const userId = cookieStore.get("user_id")?.value;
  const email = cookieStore.get("email")?.value;
  const level = cookieStore.get("state")?.value as any | undefined;
  const registrationType = cookieStore.get("registrationtype")?.value as
    | RegistrationType
    | undefined;
  const token = cookieStore.get("token")?.value;
  return { userId, email, level, registrationType, token };
};
