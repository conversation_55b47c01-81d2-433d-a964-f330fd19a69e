export const handleErrorResponse = async (response: Response) => {
  const contentType = response.headers.get("content-type");

  if (contentType && contentType.includes("application/json")) {
    const json = await response.json();
    if (json.humanized_message) {
      throw new Error(json.humanized_message);
    } else if (json.non_field_errors) {
      throw new Error(json.non_field_errors[0]);
    } else {
      throw new Error("An unknown error occurred");
    }
  } else if (contentType && contentType.includes("application/xml")) {
    const text = await response.text();
    const xml = parseXML(text);
    if (xml) {
      const errorMessage = extractErrorMessageFromXML(xml);
      throw new Error(errorMessage);
    } else {
      throw new Error("Failed to parse XML error response");
    }
  } else {
    // Non-JSON and non-XML error
    throw new Error(`Unexpected response: ${response.statusText}`);
  }
};

export const extractErrorMessageFromXML = (xml: Document): string => {
  const errorNode = xml.querySelector("error") || xml.querySelector("message");
  if (errorNode) {
    return errorNode.textContent || "Unknown XML error";
  }
  return "An unknown XML error occurred";
};

export const parseXML = (xmlString: string): Document | null => {
  try {
    const parser = new DOMParser();
    return parser.parseFromString(xmlString, "application/xml");
  } catch (err) {
    console.error("Error parsing XML: ", err);
    return null;
  }
};
