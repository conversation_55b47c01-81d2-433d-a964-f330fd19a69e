type AnyObject<T = any> = { [key: string]: T };
// Generic function to remove empty values from an object
export const removeEmptyValuesFromObject = <T extends AnyObject>(
  obj: T | undefined | null, // Allow obj to be undefined or null
  isEmpty: (value: any) => boolean = (value) =>
    value === null || value === undefined || value === "",
): T => {
  // If the object is undefined or null, return it as is
  if (obj === undefined || obj === null) {
    return obj as unknown as T;
  }

  // Remove first-level empty values
  const cleanedObject = Object.fromEntries(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    Object.entries(obj).filter(([_, value]) => !isEmpty(value)),
  ) as T;

  // Recursively check for nested objects and arrays
  return Object.fromEntries(
    Object.entries(cleanedObject).map(([key, value]) => {
      if (Array.isArray(value)) {
        return [
          key,
          value.map((v) =>
            typeof v === "object" ? removeEmptyValuesFromObject(v, isEmpty) : v,
          ),
        ];
      }
      if (typeof value === "object" && value !== null) {
        return [key, removeEmptyValuesFromObject(value, isEmpty)];
      }
      return [key, value];
    }),
  ) as T;
};

export function handleNumberKeyDown(
  event: React.KeyboardEvent<HTMLInputElement>,
): void {
  const invalidKeys = ["e", "E", "+", "-"];
  if (invalidKeys.includes(event.key)) {
    event.preventDefault();
  }
}

export const getNextTier = (value: string): string | null => {
  const match = value.match(/(.*?)(\d+)$/); // Match the string and the number at the end
  if (!match) return null; // Return null if no number is found

  const prefix = match[1]; // The part before the number (e.g., "tier")
  const number = parseInt(match[2], 10); // Parse the number (e.g., "1")
  const nextNumber = number + 1; // Increment the number

  return `${prefix}${nextNumber}`; // Return the updated string
};
