"use server";

import { getAuthHeaders } from "./get-token";

/**
 * Makes an authenticated API request with the authorization token from cookies
 * 
 * @param url The URL to make the request to
 * @param options Request options (method, body, etc.)
 * @returns The response from the API
 */
export async function fetchWithAuth(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const headers = await getAuthHeaders();
  
  // Merge provided headers with auth headers
  const mergedHeaders = {
    ...headers,
    ...(options.headers || {}),
  };
  
  return fetch(url, {
    ...options,
    headers: mergedHeaders,
  });
}

/**
 * Makes an authenticated GET request
 */
export async function getWithAuth<T>(url: string): Promise<T> {
  const response = await fetchWithAuth(url, { method: 'GET' });
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Makes an authenticated POST request
 */
export async function postWithAuth<T>(url: string, data: any): Promise<T> {
  const response = await fetchWithAuth(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Makes an authenticated PUT request
 */
export async function putWithAuth<T>(url: string, data: any): Promise<T> {
  const response = await fetchWithAuth(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * Makes an authenticated DELETE request
 */
export async function deleteWithAuth<T>(url: string): Promise<T> {
  const response = await fetchWithAuth(url, { method: 'DELETE' });
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
} 