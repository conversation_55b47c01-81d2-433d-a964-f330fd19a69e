import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';

export class NotificationService {
  private static instance: NotificationService;
  private connection: HubConnection | null = null;
  private handlers: Map<string, (data: any) => void> = new Map();
  private userId: string | null = null;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  public async startConnection(baseUrl: string, userId: string): Promise<void> {
    if (this.connection) {
      await this.stopConnection();
    }

    this.userId = userId;

    this.connection = new HubConnectionBuilder()
      .withUrl(`${baseUrl}/notificationHub`)
      .withAutomaticReconnect()
      .configureLogging({
        log: (logLevel, message) => {
          // Filter out connection negotiation and abort errors
          const shouldSuppressLog = message.includes('stopped during negotiation') ||
                                   message.includes('AbortError') ||
                                   message.includes('Failed to start the connection') ||
                                   message.includes('The connection was stopped during negotiation');
          
          if (!shouldSuppressLog && logLevel >= LogLevel.Warning) {
            console.log(`SignalR [${LogLevel[logLevel]}]: ${message}`);
          }
        }
      })
      .build();

    this.connection.on('ReceiveNotification', (notification) => {
      const handler = this.handlers.get('notification');
      if (handler) {
        handler(notification);
      }
    });

    try {
      await this.connection.start();
      console.log('SignalR Connected');
      
      // Pridruži se grupi za ovog korisnika
      if (this.connection.state === 'Connected') {
        await this.connection.invoke('JoinUserGroup', userId);
      }
    } catch (err) {
      // Only log errors that are not related to connection negotiation or AbortError
      if (err instanceof Error) {
        const isNegotiationError = err.message.includes('stopped during negotiation') || 
                                  err.name === 'AbortError' ||
                                  err.message.includes('Cannot send data if the connection is not in the \'Connected\' State');
        
        if (!isNegotiationError) {
          console.error('SignalR Connection Error: ', err);
        }
      }
    }
  }

  public async stopConnection(): Promise<void> {
    if (this.connection) {
      try {
        // Only try to leave the user group if connection is in Connected state
        if (this.userId && this.connection.state === 'Connected') {
          await this.connection.invoke('LeaveUserGroup', this.userId);
        }
        await this.connection.stop();
        console.log('SignalR Disconnected');
      } catch (err) {
        console.error('SignalR Disconnection Error: ', err);
      } finally {
        // Clean up references regardless of connection state
        this.connection = null;
        this.userId = null;
      }
    }
  }

  public onNotification(callback: (notification: any) => void): void {
    this.handlers.set('notification', callback);
  }

  public removeNotificationHandler(): void {
    this.handlers.delete('notification');
  }
}

export default NotificationService.getInstance();
