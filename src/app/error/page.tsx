"use client";

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import { XCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

function ErrorPageContent() {
  const searchParams = useSearchParams();
  const message = searchParams.get('message') || 'An unexpected error occurred';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Something went wrong
        </h1>
        <p className="text-gray-600 mb-6">
          {message}
        </p>
        <div className="flex flex-col gap-3">
          <Button onClick={() => window.history.back()} className="flex items-center gap-2 justify-center">
            <ArrowLeft className="h-4 w-4" />
            Go Back
          </Button>
          <Button variant="outline" onClick={() => window.location.href = '/'}>
            Go to Home
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function ErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <ErrorPageContent />
    </Suspense>
  );
} 