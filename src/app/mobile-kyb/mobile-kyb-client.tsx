"use client";

import { SUMSUB_LEVEL } from '@/types/user';
import SumSubIframe from '@/components/sumsub-iframe';
import { FormCard } from '@/components/register/form-card';

interface MobileKYBClientProps {
  token: string;
  email: string;
  tier: string;
  returnUrl: string;
  sumsubLevel: SUMSUB_LEVEL;
}

/**
 * Simple mobile KYB client - just renders SumsubIframe like other pages
 */
export function MobileKYBClient({
  token,
  email,
  tier,
  returnUrl,
  sumsubLevel,
}: MobileKYBClientProps) {

  /**
   * Handle verification completion and redirect to mobile app
   */
  const handleVerificationComplete = () => {
    console.log('🏁 Mobile KYB verification complete');
    
    try {
      // Parse the return URL and add result parameters
      const url = new URL(returnUrl);
      url.searchParams.set('status', 'completed');
      url.searchParams.set('tier', tier);
      url.searchParams.set('timestamp', Date.now().toString());

      const finalUrl = url.toString();
      console.log('🔗 Redirecting to mobile app:', finalUrl);

      // Redirect to mobile app via deep link
      window.location.href = finalUrl;

    } catch (error) {
      console.error('❌ Failed to redirect to mobile app:', error);
      // Fallback: try simple redirect without URL parsing
      window.location.href = returnUrl;
    }
  };

  return (
    <FormCard className="p-5">
      <SumSubIframe
        token={token}
        email={email}
        sumsubLevel={sumsubLevel}
        onApplicantReviewComplete={handleVerificationComplete}
      />
    </FormCard>
  );
} 