import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { SUMSUB_LEVEL } from '@/types/user';
import { MobileKYBClient } from './mobile-kyb-client';

// Force dynamic rendering since we use cookies
export const dynamic = 'force-dynamic';

/**
 * Get SumSub token for mobile KYB using the mobile auth token
 */
async function getMobileSumSubToken(levelName: SUMSUB_LEVEL, authToken: string, userId: string) {
  try {
    const url = `${process.env.NEXT_PUBLIC_API_URL}/Sumsub/get-tier-sdk-userid-level-token?userId=${userId}&levelName=${levelName}`;
    console.log('🏢 Mobile KYB SumSub URL:', url);
    
    // Ensure proper Bearer token format (remove existing Bearer prefix if present)
    const cleanToken = authToken.replace('Bearer ', '');
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${cleanToken}`,
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': '69420',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    const jsonData = JSON.parse(data.data);
    console.log('✅ Mobile KYB SumSub token received');
    return jsonData?.token || null;
  } catch (error: any) {
    console.error('❌ Mobile KYB SumSub token error:', error);
    return null;
  }
}

/**
 * Extract user ID from JWT token
 */
function extractUserIdFromToken(token: string): string | null {
  try {
    // Remove 'Bearer ' prefix if present
    const cleanToken = token.replace('Bearer ', '');
    
    // Decode JWT payload (base64) using Node.js Buffer
    const payload = cleanToken.split('.')[1];
    const decodedPayload = JSON.parse(Buffer.from(payload, 'base64').toString());
    
    return decodedPayload.userid || decodedPayload.user_id || decodedPayload.sub || null;
  } catch (error) {
    console.error('❌ Failed to extract user ID from token:', error);
    return null;
  }
}

/**
 * Mobile KYB verification page - Simple implementation like existing KYC pages
 */
export default async function MobileKYBPage() {
  try {
    const cookieStore = await cookies();
    
    // Get parameters from cookies set by the API route
    const tier = cookieStore.get('mobile-kyb-tier')?.value;
    const email = cookieStore.get('mobile-user-email')?.value;
    const returnUrl = cookieStore.get('mobile-return-url')?.value;
    const authToken = cookieStore.get('mobile-auth-token')?.value;

    console.log('🏢 Mobile KYB page loaded:', { tier, email, returnUrl: !!returnUrl });

    // Validate required parameters
    if (!tier || !email || !returnUrl || !authToken) {
      console.error('❌ Missing required parameters for mobile KYB');
      redirect('/error?message=Invalid mobile KYB session');
    }

    // Validate tier is a KYB tier
    if (!tier.startsWith('kybtier')) {
      console.error('❌ Invalid tier for KYB:', tier);
      redirect('/error?message=Invalid tier for KYB verification');
    }

    // Convert tier string to SUMSUB_LEVEL enum
    let sumsubLevel: SUMSUB_LEVEL;
    switch (tier) {
      case 'kybtier1':
        sumsubLevel = SUMSUB_LEVEL.KYB_TIER1;
        break;
      case 'kybtier2':
        sumsubLevel = SUMSUB_LEVEL.KYB_TIER2;
        break;
      case 'kybtier3':
        sumsubLevel = SUMSUB_LEVEL.KYB_TIER3;
        break;
      default:
        console.error('❌ Unsupported KYB tier:', tier);
        redirect('/error?message=Unsupported KYB tier');
    }

    // Extract user ID from the auth token
    const userId = extractUserIdFromToken(authToken);
    if (!userId) {
      console.error('❌ Failed to extract user ID from token');
      redirect('/error?message=Invalid authentication token');
    }

    // Get SumSub token using mobile auth
    const sumsubToken = await getMobileSumSubToken(sumsubLevel, authToken, userId);

    if (!sumsubToken) {
      console.error('❌ Failed to get SumSub token for mobile KYB');
      redirect('/error?message=Failed to initialize verification');
    }

    console.log('✅ Mobile KYB initialized successfully');

    return (
      <div className="w-full h-full container py-12">
        <div className="h-full flex items-center justify-center border-t border-black border-b">
          <MobileKYBClient
            token={sumsubToken}
            email={email}
            tier={tier}
            returnUrl={returnUrl}
            sumsubLevel={sumsubLevel}
          />
        </div>
      </div>
    );

  } catch (error: any) {
    console.error('❌ Mobile KYB page error:', error);
    redirect('/error?message=Failed to load verification page');
  }
} 