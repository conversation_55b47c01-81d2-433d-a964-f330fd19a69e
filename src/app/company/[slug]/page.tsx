"use client";

import { useParams } from "next/navigation";
import { Header } from "@/components/categories/navbar";
import { FeaturedCategories } from "@/components/categories/featured-categories";
import { Background } from "@/components/categories/background";
import { Footer } from "@/components/layout/footer";
import { CompanyDetails } from "@/components/company/company-details";
import { useFetchCompany } from "@/hooks/use-fetch-company";
import { useState } from "react";

export default function CompanyLandingPage() {
  const params = useParams(); // Retrieve company_id from the route (slug)
  const { companyData, loading, error } = useFetchCompany(params.slug); // Use custom hook
  const [selectedCategorySlug, setSelectedCategorySlug] = useState<
    string | null
  >(null);

  const handleCategorySelect = (slug: string | null) => {
    setSelectedCategorySlug(slug || null);
  };

  return (
    <div className="container md:px-8 min-h-screen flex flex-col">
      <Background />
      <Header hideSearchBar={true} />
      <main className="container flex-1 overflow-hidden flex flex-col">
        <div className="md:px-8 flex flex-col flex-1">
          <FeaturedCategories onCategorySelect={handleCategorySelect} />
          {loading ? (
            <p className="text-center text-gray-500 text-base md:text-lg py-4">
              Loading company details...
            </p>
          ) : error ? (
            <p className="text-center text-red-500 text-base md:text-lg py-4">
              {error}
            </p>
          ) : (
            /* Pass Zustand's userTier dynamically to the CompanyDetails component */
            <CompanyDetails companyData={companyData} />
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
