import Image from "next/image";
import logo from "../../../public/logo.svg";
import React from "react";
import { Button } from "@/components/ui/button";

interface InfoStepProps {
  onNext: () => void;
}

const InfoStep = ({ onNext }: InfoStepProps) => {
  const handleOnNext = (event: React.MouseEvent): void => {
    event.preventDefault();
    onNext();
  };

  return (
    <div className="flex flex-col gap-14 items-center py-20">
      <div className="flex flex-col gap-3">
        <h2 className="text-primary text-center text-2xl font-semibold">
          Verification for
        </h2>
        <Image src={logo} alt="Logo" width={285} height={41} />
      </div>

      <p className="text-primary text-lg text-center">
        You&apos;re about to submit sensitive data to trustnexus. It you
        received this link from a suspicious source, please close this page and
        notify us immediately.
      </p>

      <Button
        className="font-semibold text-base w-[160px] uppercase"
        size="md"
        type="button"
        onClick={(event) => handleOnNext(event)}
      >
        Continue
      </Button>
    </div>
  );
};

export default InfoStep;
