import { KycVerification } from "@/app/kyc/kyc-verification";
import { getSumSubToken } from "@/react-query/query/sumsub-token";
import { getUserProfile } from "@/react-query/query/user";
import { SUMSUB_LEVEL } from "@/types/user";

export const dynamic = "force-dynamic";

const KycPage = async () => {
  const token = await getSumSubToken(SUMSUB_LEVEL.TIER1);
  const userData = await getUserProfile();

  if (!token || !userData) {
    return null;
  }

  return (
    <div className="w-full h-full container py-12">
      <div className="h-full flex items-center justify-center border-t border-black border-b">
        {token && userData && (
          <KycVerification
            token={token}
            email={userData.email}
            sumsubLevel={SUMSUB_LEVEL.TIER1}
          />
        )}
      </div>
    </div>
  );
};
export default KycPage;
