"use client";

import { useState } from "react";
import { FormCard } from "@/components/register/form-card";
import InfoStep from "@/app/kyc/info-step";
import SumSubIframe from "@/components/sumsub-iframe";
import ArrowLeft from "@/assets/icons/ArrowLeft";
import { useRouter } from "next/navigation";
import { SUMSUB_LEVEL } from "@/types/user";

enum Steps {
  Info,
  KYC,
}

interface KYCVerificationProps {
  token: string;
  email: string;
  sumsubLevel: SUMSUB_LEVEL;
}

export const KycVerification = ({
  token,
  email,
  sumsubLevel,
}: KYCVerificationProps) => {
  const [activeStep, setActiveStep] = useState<Steps>(Steps.Info);
  const router = useRouter();

  if (!token || !email) {
    return null;
  }

  const onNextStep = () => {
    setActiveStep(Steps.KYC);
  };

  const onPreviousStep = () => {
    if (activeStep === Steps.KYC) {
      setActiveStep(Steps.Info);
    } else {
      router.back();
    }
  };

  return (
    <FormCard>
      <button onClick={onPreviousStep}>
        <ArrowLeft />
      </button>
      {activeStep === Steps.Info && <InfoStep onNext={onNextStep} />}
      {activeStep === Steps.KYC && (
        <SumSubIframe
          token={token}
          email={email}
          sumsubLevel={sumsubLevel}
          onApplicantReviewComplete={() => router.push("/categories")}
        />
      )}
    </FormCard>
  );
};
