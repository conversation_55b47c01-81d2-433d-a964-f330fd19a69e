import React from "react";
import { Background } from "@/components/categories/background";
import ProfileSidenav from "@/components/profile/sidenav";
import { Header } from "@/components/categories/navbar";

const ProfileLayout = ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  return (
    <section className="container px-4 md:px-8 min-h-screen">
      <Background />
      <Header />

      <div className="flex flex-col md:flex-row gap-4 md:gap-8">
        {/* ProfileSidenav */}
        <div className="w-full md:w-[25%] md:flex-shrink-0">
          <ProfileSidenav />
        </div>

        {/* Main content */}
        <div className="w-full md:flex-grow">{children}</div>
      </div>
    </section>
  );
};
export default ProfileLayout;
