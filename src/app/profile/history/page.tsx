"use client";

import React, { useState } from "react";
import DocumentIcon from "@/assets/icons/DocumentIcon";
import { But<PERSON> } from "@/components/ui/button";
import { useFetchDocumentHistory } from "@/react-query/consent-history-hook";
import { useRevokeConsent } from "@/react-query/revoke-consent-hook";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { useGetDocuments } from "@/react-query/documents-hook";
import { useGetDocumentsByTier } from "@/react-query/documents-by-tier-hook";
import { TIER_NAMES } from "@/components/company/tier";
import { DocumentViewerModal } from "@/components/document-viewer-modal";
import { RevokeConsentModal } from "@/components/revoke-consent-modal";
import ViewIcon from "@/assets/icons/ViewIcon";
import { SUMSUB_LEVEL } from "@/types/user";

const DocumentsPage = () => {
  // Fetch document history
  const { data: histories = [], isLoading, error } = useFetchDocumentHistory();
  const { data: userProfile } = useGetUserProfile();
  const applicantId = userProfile?.applicant_id;

  const { data: documents = [] } = useGetDocuments(applicantId);
  
  // Fetch documents by tier
  const { 
    data: tieredDocuments, 
    isLoading: loadingTieredDocs,
    invalidateDocumentsByTier,
    invalidateAllDocumentQueries 
  } = useGetDocumentsByTier(applicantId);
  
  // State for expanded cards
  const [expandedCards, setExpandedCards] = useState<Set<number>>(new Set());
  
  // State for document viewer modal
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<{
    url: string;
    name: string;
  } | null>(null);
  
  // State for revoke consent modal
  const [isRevokeModalOpen, setIsRevokeModalOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<{
    id: number;
    name: string;
  } | null>(null);
  
  // Revoke consent mutation
  const {
    mutateAsync: revokeConsent,
    isPending: revoking,
    error: revokeError,
  } = useRevokeConsent({
    mutationOptions: {
      onError: (err) => {
        console.error("Failed to revoke consent:", err.message);
      },
    },
  });

  // Toggle card expansion
  const toggleCardExpansion = (companyId: number) => {
    setExpandedCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(companyId)) {
        newSet.delete(companyId);
      } else {
        newSet.add(companyId);
      }
      return newSet;
    });
  };

  // Handle opening document viewer
  const handleOpenDocument = (documentUrl: string, documentName: string) => {
    setSelectedDocument({ url: documentUrl, name: documentName });
    setIsViewerOpen(true);
  };

  // Helper function to check if file is a PDF
  const isPdf = (fileName: string) => {
    return fileName?.toLowerCase().endsWith('.pdf');
  };

  // Get the tier name based on the tier number
  const getTierName = (state?: string) => {
    const tierNumber = getTierNumber(state);
    return TIER_NAMES[tierNumber] || `Tier #${tierNumber}`;
  };

  // Tier helper functions
  const getTierNumber = (state?: string) => {
    if (!state) return 0;
    return parseInt(state.replace(/[^\d]/g, "")) || 0;
  };

  // Helper function to get documents for a company based on tier
  const getDocumentsForCompany = (companyTier: SUMSUB_LEVEL) => {
    if (!tieredDocuments?.data?.tiers) return [];
    const tierPrefix = userProfile?.registrationtype === "company" ? "kybtier" : "tier";
    
    // Extract tier number from enum value (e.g., "tier1" -> 1, "kybtier2" -> 2)
    const tierNumber = companyTier;
    const tierKey = `${tierPrefix}${tierNumber}`;
    const tierData = tieredDocuments.data.tiers.find(tier => tier.state === tierKey);
    
    return tierData?.files || [];
  };

  // Handle opening revoke consent modal
  const handleRevoke = (companyId: number, companyName: string) => {
    setSelectedCompany({
      id: companyId,
      name: companyName,
    });
    setIsRevokeModalOpen(true);
  };

  // Handle confirming revoke consent
  const handleConfirmRevoke = async () => {
    if (!selectedCompany) return;
    
    try {
      await revokeConsent(selectedCompany.id);
      // Invalidate document queries after revoking consent
      await invalidateAllDocumentQueries();
      // Close the modal
      setIsRevokeModalOpen(false);
      setSelectedCompany(null);
    } catch (err) {
      console.error("Revoke consent failed:", err); // Handle custom logic here
    }
  };

  // Handle closing revoke modal
  const handleCloseRevokeModal = () => {
    setIsRevokeModalOpen(false);
    setSelectedCompany(null);
  };

  return (
    <section className="w-full flex flex-col gap-3 pb-10">
      <div className="md:bg-white shadow-sm rounded-xl md:p-5 md:px-6">
        {/* Page Heading */}
        <h2 className="text-xl md:text-4xl text-black font-normal border-b border-b-light-gray pb-6">
          Active Companies
        </h2>
        {/* Display loading state */}
        {(isLoading || loadingTieredDocs) && <p className="text-center text-gray-500">Loading...</p>}
        {/* Display errors */}
        {error && (
          <p className="text-center text-red-500">
            {error.message || "Failed to load document history."}
          </p>
        )}
        {revokeError && (
          <p className="text-center text-red-500">
            {revokeError.message || "Failed to revoke consent."}
          </p>
        )}
        {/* Display history items */}
        {!isLoading && !loadingTieredDocs && !error && histories.length > 0 && (
          <div className="grid grid-cols-1 gap-5 mt-6">
            {histories.map((document) => {
              const requiredTier =
                userProfile?.registrationtype === "company"
                  ? document.required_corporate_tier
                  : document.required_individual_tier;
              const isExpanded = expandedCards.has(document.company_id);
              const companyDocuments = getDocumentsForCompany(requiredTier);
              
              return (
                <div
                  className="bg-white rounded-lg shadow-md overflow-hidden"
                  key={document.company_id}
                >
                  <div className="p-5">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-2">
                        {/* Document Icon */}
                        <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gray-200 overflow-hidden">
                          {(document.s3LogoUrl || document.s3ImageUrl) ? (
                            <img
                              src={(document.s3LogoUrl || document.s3ImageUrl) as string}
                              alt={`${document.name} logo`}
                              className="w-full h-full object-contain rounded-full"
                              onError={(e) => {
                                // Fallback to DocumentIcon if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                const fallback = target.nextElementSibling as HTMLElement;
                                if (fallback) fallback.style.display = 'flex';
                              }}
                            />
                          ) : null}
                          <div 
                            className={`w-full h-full flex items-center justify-center ${
                              (document.s3LogoUrl || document.s3ImageUrl) ? 'hidden' : ''
                            }`}
                          >
                            <DocumentIcon />
                          </div>
                        </div>

                        {/* Title */}
                        <div className="flex flex-col">
                          <p className="text-black text-sm md:text-base font-medium">
                            {document.name}
                          </p>
                        </div>
                      </div>

                      {/* Tier */}
                      <div className="rounded-full border border-[#1C3F3C] text-[#231F20] py-1 px-3">
                        {TIER_NAMES[requiredTier] || `Tier #${requiredTier}`}
                      </div>
                    </div>

                    <p className="text-gray-600 text-sm font-normal mt-3">
                      Document attached: {companyDocuments.length}
                    </p>

                    <div className="flex justify-between items-center mt-5">
                      {/* Details Button */}
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleCardExpansion(document.company_id)}
                          className="rounded-full px-4 py-1 font-normal text-sm"
                        >
                          {isExpanded ? "Hide" : "Details"}
                        </Button>

                        {/* Revoke Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRevoke(document.company_id, document.name)}
                          disabled={revoking}
                          className={`border-red-500 text-red-500 rounded-full px-4 py-1 font-normal text-sm ${
                            revoking ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                        >
                          {revoking ? "Revoking..." : "Revoke"}
                        </Button>
                      </div>

                      {/* Document Status */}
                      <p
                        className={`text-sm font-medium ${
                          true ? "text-green-500" : "text-red-500"
                        }`}
                      >
                        {true ? "VALID" : "Pending"}
                      </p>
                    </div>
                  </div>

                  {/* Expanded Document Table */}
                  {isExpanded && (
                    <div className="border-t border-gray-200">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Doc.
                              </th>
                              <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type
                              </th>
                              {/* <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                File Name
                              </th> */}
                              <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Country
                              </th>
                              <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Preview
                              </th>
                              <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                              </th>
                              <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Updated
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {companyDocuments.map((doc, index) => {
                              return (
                                <tr key={index} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                      <div className="w-8 h-8 flex items-center justify-center">
                                        <DocumentIcon className="w-6 h-6 text-gray-400" />
                                      </div>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {doc.idDocDefIdDocType || "-"}
                                  </td>
                                  {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {doc.fileName || "-"}
                                  </td> */}
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {doc.idDocDefCountry || "-"}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-500 font-medium">
                                    VALID
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <button
                                      onClick={() => handleOpenDocument(doc.fileS3Path, doc.fileName || "Document")}
                                      className="text-gray-400 hover:text-blue-500 transition-colors"
                                    >
                                      <ViewIcon className="w-5 h-5" />
                                    </button>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {doc.creationDate 
                                      ? new Date(doc.creationDate).toLocaleDateString()
                                      : '-'
                                    }
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {doc.modificationDate 
                                      ? new Date(doc.modificationDate).toLocaleDateString()
                                      : '-'
                                    }
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
        {/* Display no data state */}
        {!isLoading && !loadingTieredDocs && !error && histories.length === 0 && (
          <p className="text-center text-gray-500">
            No document history found.
          </p>
        )}
      </div>
      
      {/* Document Viewer Modal */}
      {selectedDocument && (
        <DocumentViewerModal
          isOpen={isViewerOpen}
          onClose={() => setIsViewerOpen(false)}
          documentUrl={selectedDocument.url}
          documentName={selectedDocument.name}
        />
      )}

      {/* Revoke Consent Modal */}
      {selectedCompany && (
        <RevokeConsentModal
          isOpen={isRevokeModalOpen}
          onClose={handleCloseRevokeModal}
          onConfirm={handleConfirmRevoke}
          companyName={selectedCompany.name}
          isLoading={revoking}
        />
      )}
    </section>
  );
};

export default DocumentsPage;
