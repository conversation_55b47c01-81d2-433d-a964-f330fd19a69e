"use client";

import React, { useState, useRef, useEffect } from "react";
import DocumentIcon from "@/assets/icons/DocumentIcon";
import ViewIcon from "@/assets/icons/ViewIcon";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { useGetDocuments } from "@/react-query/documents-hook";
import { DocumentViewerModal } from "@/components/document-viewer-modal";
import { IoEllipsisVertical, IoEyeOutline, IoDownloadOutline, IoTrashOutline } from "react-icons/io5";

const DocumentsPage = () => {
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<{
    url: string;
    name: string;
  } | null>(null);
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { data: userProfile, isLoading: isProfileLoading } =
    useGetUserProfile();
  const applicantId = userProfile?.applicant_id;

  const {
    data: documents = [],
    isLoading: isDocumentsLoading,
    isError: isDocumentsError,
  } = useGetDocuments(applicantId);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleOpenDocument = (documentUrl: string, documentName: string) => {
    // Prevent any default browser behavior
    if (window.event) {
      window.event.preventDefault();
      window.event.stopPropagation();
    }
    
    setSelectedDocument({ url: documentUrl, name: documentName });
    setIsViewerOpen(true);
    setActiveDropdown(null);
  };

  const handleDownloadDocument = (documentUrl: string, documentName: string) => {
    // Create a temporary anchor element to trigger download
    const link = document.createElement('a');
    link.href = documentUrl;
    link.download = documentName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    setActiveDropdown(null);
  };

  const handleRemoveDocument = (documentName: string) => {
    // Dummy function for now
    console.log(`Remove document: ${documentName}`);
    // alert(`Remove functionality for "${documentName}" will be implemented soon.`);
    setActiveDropdown(null);
  };

  const toggleDropdown = (index: number) => {
    setActiveDropdown(activeDropdown === index ? null : index);
  };

  const handleCloseViewer = () => {
    setIsViewerOpen(false);
  };
  
  // Helper function to check if file is a PDF
  const isPdf = (fileName: string) => {
    return fileName?.toLowerCase().endsWith('.pdf');
  };

  if (isProfileLoading || isDocumentsLoading) {
    return <p>Loading...</p>;
  }

  if (isDocumentsError) {
    return <p>An error occurred while fetching documents.</p>;
  }

  return (
    <section className="w-full flex flex-col gap-3 pb-10">
      {/*My documents section*/}
      <div className="bg-white shadow-sm rounded-xl p-5 px-6">
        <h2 className="text-xl md:text-4xl text-black font-normal border-b border-b-light-gray pb-6">
          My Documents
        </h2>

        {/* Check if documents exist */}
        {documents.length > 0 ? (
          documents
          .filter(document => document.idDocDefIdDocType !== "SELFIE")
          .map((document, index) => (
            <div
              className="flex justify-between items-center border-b border-b-light-gray py-3"
              key={index}
            >
              {/* Thumbnail */}
              {document.fileS3PathThumbnail && !isPdf(document.fileName || '') ? (
                <img
                  className="w-12 h-12 object-cover rounded-md mr-4"
                  src={document.fileS3PathThumbnail}
                  alt={document.fileName || "Document thumbnail"}
                />
              ) : (
                <div className="w-12 h-12 mr-4 flex items-center justify-center bg-gray-100 rounded-md">
                  <DocumentIcon className="w-8 h-8" />
                </div>
              )}

              {/* Document Details */}
              <div className="flex flex-col flex-grow">
                {/* File Name */}
                <p className="text-black text-base font-medium">
                  {document.fileName || "Untitled Document"}
                </p>

                {/* Document Type */}
                <p className="text-gray-500 text-sm">
                  {document.idDocDefIdDocType || "Unknown Type"}
                </p>

                {/* Modification Date */}
                {document.modificationDate && (
                  <p className="text-gray-400 text-xs">
                    Last Modified:{" "}
                    {new Date(document.modificationDate).toLocaleDateString()}
                  </p>
                )}
              </div>

              {/* Status */}
              <div className="flex items-center mr-4">
                <p className="text-sm font-medium text-green-500">
                  VALID
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-4 relative" ref={activeDropdown === index ? dropdownRef : undefined}>
                <button
                  onClick={() => toggleDropdown(index)}
                  className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <IoEllipsisVertical className="w-5 h-5" />
                </button>

                {/* Dropdown Menu */}
                {activeDropdown === index && (
                  <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="py-1">
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleOpenDocument(document.fileS3Path, document.fileName || "Document");
                        }}
                        className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <IoEyeOutline className="w-4 h-4" />
                        Preview
                      </button>
                      <button
                        onClick={() => handleDownloadDocument(document.fileS3Path, document.fileName || "Document")}
                        className="flex items-center gap-3 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <IoDownloadOutline className="w-4 h-4" />
                        Download
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <p className="text-center text-gray-500">No documents found.</p>
        )}
      </div>

      {/* Document Viewer Modal */}
      {selectedDocument && (
        <DocumentViewerModal
          isOpen={isViewerOpen}
          onClose={handleCloseViewer}
          documentUrl={selectedDocument.url}
          documentName={selectedDocument.name}
        />
      )}
    </section>
  );
};

export default DocumentsPage;
