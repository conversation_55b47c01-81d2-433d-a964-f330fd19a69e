"use client";

import UserIcon from "@/assets/icons/UserIcon";
import { LogoSmall } from "@/components/logo-small";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader, Phone, Mail, MapPin, Medal } from "lucide-react";
import { IoMailOutline, IoKeyOutline, IoChevronForward } from "react-icons/io5";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { ChangePasswordModal } from "@/components/profile/change-password-modal";

export default function ProfilePage() {
  const { data: user, isPending } = useGetUserProfile();
  const [timezone, setTimezone] = useState<string>("");
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] = useState(false);
  
  useEffect(() => {
    // Get user's timezone
    const tz = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const offset = new Date().getTimezoneOffset();
    const hours = Math.abs(Math.floor(offset / 60));
    const minutes = Math.abs(offset % 60);
    const sign = offset < 0 ? "+" : "-";
    
    // Format: "America/New_York (GMT-4)"
    const formattedTz = `${tz} (GMT${sign}${hours}${minutes > 0 ? `:${minutes}` : ""})`;
    setTimezone(formattedTz);
  }, []);

  if (isPending) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <Loader className="animate-spin" />
      </div>
    );
  }

  // Format signup date
  const formatSignupDate = (dateString: string | undefined) => {
    if (!dateString) return "Not available";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric'
    }).replace(/\//g, '.');
  };

  return (
    <section className="w-full flex flex-col gap-6 pb-10 px-4 md:px-0">
      {/* Page Title */}
      <h1 className="text-2xl md:text-4xl text-black font-normal">
        {user?.registrationtype === "company" ? "Company Profile" : "Personal Information"}
      </h1>

      {/* Personal information section - Figma style */}
      <div className="bg-white shadow-[0px_1px_8px_0px_rgba(0,0,0,0.15)] rounded-xl p-4 md:p-6">
        {/* Profile Header */}
        <div className="flex flex-col gap-2 pb-5 border-b border-[#E7E7E7]">
          <h2 className="text-xl md:text-2xl text-[#0F1113] font-semibold">
            {user?.registrationtype === "company" 
              ? user?.userCompany?.companyName || user?.name || "Company Name"
              : user?.name || "User Name"
            }
          </h2>
          <p className="text-xs md:text-sm text-[#888E92] font-medium">
            Signup date: {formatSignupDate(user?.signup_date)}
          </p>
        </div>

        {/* Contact Information Row */}
        <div className="flex flex-col md:flex-row gap-4 md:gap-8 pt-5 pb-5">
          {/* Phone */}
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 md:w-7 md:h-7 bg-[#C8E2CE] rounded flex items-center justify-center">
              <Phone size={14} className="text-[#0D0E0D]" />
            </div>
            <p className="text-[#0D0E0D] text-xs md:text-sm">
              {user?.registrationtype === "company" 
                ? user?.userCompany?.phoneNumber || "+****************"
                : user?.userIndividual?.phoneNumber || "+****************"
              }
            </p>
          </div>

          {/* Email */}
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 md:w-7 md:h-7 bg-[#C8E2CE] rounded flex items-center justify-center">
              <Mail size={14} className="text-[#0D0E0D]" />
            </div>
            <p className="text-[#0D0E0D] text-xs md:text-sm">
              {user?.registrationtype === "company" 
                ? user?.userCompany?.email || user?.email || "<EMAIL>"
                : user?.email || "<EMAIL>"
              }
            </p>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 md:gap-y-5 gap-x-8">
          {/* Country of Residence / Country */}
          <div className="flex flex-col gap-0.5">
            <p className="text-[#888E92] text-[10px] md:text-xs">
              {user?.registrationtype === "company" ? "Country" : "Country of Residence"}
            </p>
            <p className="text-[#0D0E0D] text-sm md:text-base">
              {user?.registrationtype === "company" 
                ? user?.userCompany?.country || "Not provided"
                : user?.userIndividual?.country || "Not provided"
              }
            </p>
          </div>

          {/* Address / Legal Address */}
          <div className="flex flex-col gap-0.5">
            <p className="text-[#888E92] text-[10px] md:text-xs">
              {user?.registrationtype === "company" ? "Legal Address" : "Address"}
            </p>
            <p className="text-[#0D0E0D] text-sm md:text-base">
              {user?.registrationtype === "company" 
                ? user?.userCompany?.legalAddress || "Not provided"
                : user?.userIndividual?.address || "Not provided"
              }
            </p>
          </div>

          {/* Tier Level */}
          <div className="flex flex-col gap-0.5">
            <p className="text-[#888E92] text-[10px] md:text-xs">Tier Level</p>
            <div className="flex items-center gap-1">
              <Medal size={14} className="text-[#0D0E0D]" />
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.state === "tier3" || user?.state === "kybtier3" ? "Pro" : 
                 user?.state === "tier2" || user?.state === "kybtier2" ? "Standard" : 
                 user?.state === "tier1" || user?.state === "kybtier1" ? "Basic" : "Basic"}
              </p>
            </div>
          </div>

          {/* TIN / Tax ID */}
          <div className="flex flex-col gap-0.5">
            <p className="text-[#888E92] text-[10px] md:text-xs">
              TIN
            </p>
            <p className="text-[#0D0E0D] text-sm md:text-base">
              {user?.registrationtype === "company" 
                ? user?.userCompany?.taxId || "Not provided"
                : user?.userIndividual?.tin || "Not provided"
              }
            </p>
          </div>

          {/* Tax Residency (only for individual) */}
          {user?.registrationtype !== "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Tax Residency</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userIndividual?.taxResidenceCountry || "Not provided"}
              </p>
            </div>
          )}

          {/* Birth Date (only for individual) */}
          {user?.registrationtype !== "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Birth Date</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userIndividual?.birthDate 
                  ? new Date(user.userIndividual.birthDate).toLocaleDateString('en-US', { 
                      day: '2-digit',
                      month: '2-digit', 
                      year: 'numeric'
                    }).replace(/\//g, '.')
                  : "Not provided"
                }
              </p>
            </div>
          )}

          {/* Registration Number (only for company) */}
          {user?.registrationtype === "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Registration Number</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userCompany?.registrationNumber || "Not provided"}
              </p>
            </div>
          )}

          {/* Registration Location (only for company) */}
          {user?.registrationtype === "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Registration Location</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userCompany?.registrationLocation || "Not provided"}
              </p>
            </div>
          )}

          {/* Website (only for company) */}
          {user?.registrationtype === "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Website</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userCompany?.website || "Not provided"}
              </p>
            </div>
          )}

          {/* Postal Address (only for company) */}
          {user?.registrationtype === "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Postal Address</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userCompany?.postalAddress || "Not provided"}
              </p>
            </div>
          )}

          {/* Incorporation Date (only for company) */}
          {user?.registrationtype === "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Incorporation Date</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userCompany?.incorporationDate 
                  ? new Date(user.userCompany.incorporationDate).toLocaleDateString('en-US', { 
                      day: '2-digit',
                      month: '2-digit', 
                      year: 'numeric'
                    }).replace(/\//g, '.')
                  : "Not provided"
                }
              </p>
            </div>
          )}

          {/* Time zone */}
          <div className="flex flex-col gap-0.5">
            <p className="text-[#888E92] text-[10px] md:text-xs">Time zone</p>
            <p className="text-[#0D0E0D] text-sm md:text-base">
              {timezone || "Loading..."}
            </p>
          </div>

          {/* Company Name (if company registration) */}
          {/* {user?.registrationtype === "company" && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">Company Name</p>
              <p className="text-[#0D0E0D] text-sm md:text-base">
                {user?.userCompany?.companyName || user?.name || "Company Name"}
              </p>
            </div>
          )} */}

          {/* Suitability test - Show only for tier3 or kybtier3 users */}
          {(user?.state === "tier3" || user?.state === "kybtier3") && (
            <div className="flex flex-col gap-0.5">
              <p className="text-[#888E92] text-[10px] md:text-xs">{user?.registrationtype === "company" ? "Company Profile Questionnaire" : "Suitability test"}</p>
              <div className="flex items-center gap-2">
                <div className="inline-flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-full">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-xs md:text-sm font-medium">Appropriate</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/*Sign in method*/}
      <div className="bg-white shadow-sm rounded-xl p-4 md:p-5 md:px-6">
        <h2 className="text-xl md:text-4xl text-black font-normal mb-4 md:mb-6">
          Sign in method
        </h2>

        {/* Email Card */}
        <div className="flex items-center bg-white rounded-xl shadow-sm border border-gray-100 p-3 md:p-4 mb-3 md:mb-4">
          <div className="w-9 h-9 md:w-11 md:h-11 rounded-full flex items-center justify-center mr-3 md:mr-4" style={{ backgroundColor: '#C8E2CE' }}>
            <IoMailOutline size={20} className="md:w-6 md:h-6" style={{ color: '#1C3F3C' }} />
          </div>
          <div className="flex-1">
            <div className="text-xs text-gray-600 font-medium mb-0.5 md:mb-1">Email</div>
            <div className="text-sm md:text-base text-black font-semibold truncate">{user?.email}</div>
          </div>
          <button className="ml-2 flex items-center group">
            <span className="sr-only">Change email</span>
            <IoChevronForward size={20} className="text-gray-400 group-hover:text-primary transition-colors" />
          </button>
        </div>

        {/* Password Card */}
        <div className="flex items-center bg-white rounded-xl shadow-sm border border-gray-100 p-3 md:p-4 mb-3 md:mb-4">
          <div className="w-9 h-9 md:w-11 md:h-11 rounded-full flex items-center justify-center mr-3 md:mr-4" style={{ backgroundColor: '#C8E2CE' }}>
            <IoKeyOutline size={20} className="md:w-6 md:h-6" style={{ color: '#1C3F3C' }} />
          </div>
          <div className="flex-1">
            <div className="text-xs text-gray-600 font-medium mb-0.5 md:mb-1">Password</div>
            <div className="text-sm md:text-base text-black font-semibold">**********</div>
          </div>
          <button 
            className="ml-2 flex items-center group"
            onClick={() => setIsChangePasswordModalOpen(true)}
          >
            <span className="sr-only">Change password</span>
            <IoChevronForward size={20} className="text-gray-400 group-hover:text-primary transition-colors" />
          </button>
        </div>
      </div>

      {/* Change Password Modal */}
      <ChangePasswordModal
        isOpen={isChangePasswordModalOpen}
        onClose={() => setIsChangePasswordModalOpen(false)}
      />
    </section>
  );
}

