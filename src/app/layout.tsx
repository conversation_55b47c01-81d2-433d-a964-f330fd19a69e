import "./globals.css";
import { Toaster } from "sonner";
import QueryProvider from "@/providers/query-provider";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import NotificationsProvider from "@/providers/notifications-provider";

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html>
      <body className="h-full font-poppins">
        <QueryProvider>
          <Toaster closeButton richColors />
          <NuqsAdapter>{children}</NuqsAdapter>
          <NotificationsProvider />
        </QueryProvider>
      </body>
    </html>
  );
}
