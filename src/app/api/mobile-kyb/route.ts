import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * API route to handle mobile KYB authentication and redirect
 * Called by mobile app to authenticate user and redirect to KYB verification
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract parameters from mobile app
    const tier = searchParams.get('tier');
    const token = searchParams.get('token');
    const email = searchParams.get('email');
    const returnUrl = searchParams.get('returnUrl');

    console.log('🏢 Mobile KYB request:', { tier, email, returnUrl });

    // Validate required parameters
    if (!tier || !token || !email || !returnUrl) {
      console.error('❌ Missing required parameters');
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Validate tier is a KYB tier
    if (!tier.startsWith('kybtier')) {
      console.error('❌ Invalid tier for KYB:', tier);
      return NextResponse.json(
        { error: 'Invalid tier for KYB verification' },
        { status: 400 }
      );
    }

    // Set authentication cookie for the web session
    const cookieStore = await cookies();
    cookieStore.set('mobile-auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60, // 1 hour
    });

    // Store return URL and other parameters in cookies for the verification page
    cookieStore.set('mobile-return-url', returnUrl, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60, // 1 hour
    });

    cookieStore.set('mobile-kyb-tier', tier, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60, // 1 hour
    });

    cookieStore.set('mobile-user-email', email, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60, // 1 hour
    });

    // Redirect to the mobile KYB verification page
    const redirectUrl = new URL('/mobile-kyb', request.url);
    
    console.log('✅ Redirecting to mobile KYB page:', redirectUrl.toString());
    
    return NextResponse.redirect(redirectUrl);

  } catch (error: any) {
    console.error('❌ Mobile KYB API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 