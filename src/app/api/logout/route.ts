import { NextResponse } from "next/server";

export async function GET() {
  const response = NextResponse.redirect(new URL("/", process.env.NEXTAUTH_URL));
  
  // Delete all auth cookies by setting them to expire immediately
  response.cookies.set("session", "", { maxAge: 0 });
  response.cookies.set("user_id", "", { maxAge: 0 });
  response.cookies.set("email", "", { maxAge: 0 });
  response.cookies.set("state", "", { maxAge: 0 });
  response.cookies.set("registrationtype", "", { maxAge: 0 });
  response.cookies.set("applicant_id", "", { maxAge: 0 });
  response.cookies.set("token", "", { maxAge: 0 });
  
  return response;
} 