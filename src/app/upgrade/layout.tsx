import React from "react";
import { Background } from "@/components/categories/background";
import ProfileSidenav from "@/components/profile/sidenav";
import { Header } from "@/components/categories/navbar";

const UpgradeKycLayout = ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  return (
    <section className="container md:px-8 min-h-screen">
      <Background />
      <Header />

      <div className="flex gap-12">
        <div className="h-full w-full">{children}</div>
      </div>
    </section>
  );
};
export default UpgradeKycLayout;
