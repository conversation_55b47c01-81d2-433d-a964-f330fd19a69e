import { getSumSubToken } from "@/react-query/query/sumsub-token";
import { getEnumValue } from "@/types/user";
import { getNextTier } from "@/utils/utils";
import { FormCard } from "@/components/register/form-card";
import { UpgradeClient } from "./updagre-client";
import { getUserProfile } from "@/react-query/query/user";

export const dynamic = "force-dynamic";

const UpgradeKycPage = async () => {
  const userData = await getUserProfile();

  const level = userData?.state || "";
  const nextLevel = getNextTier(level);
  const tierEnum = getEnumValue(nextLevel!);

  const token = await getSumSubToken(tierEnum!);

  if (!token) {
    return <div>No token found</div>;
  }

  return (
    <div className="flex justify-center mt-10">
      <FormCard className="p-5">
        <UpgradeClient token={token} email={userData?.email} />
      </FormCard>
    </div>
  );
};

export default UpgradeKycPage;
