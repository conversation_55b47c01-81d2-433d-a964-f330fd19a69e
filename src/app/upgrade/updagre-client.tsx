"use client";

import SumSubIframe from "@/components/sumsub-iframe";
import { useGetUserProfile } from "@/react-query/auth-hooks";
import { getEnumValue } from "@/types/user";
import { getNextTier } from "@/utils/utils";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import { APP_QUERY_KEYS } from "@/react-query/query-keys";
import { Loader } from "lucide-react";

interface UpgradeClientProps {
  token: string;
  email: string;
}

export const UpgradeClient = ({ token, email }: UpgradeClientProps) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { refetch, data, isPending } = useGetUserProfile();

  const level = data?.state || "";
  const nextLevel = getNextTier(level);
  const tierEnum = getEnumValue(nextLevel!);

  if (isPending) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <Loader className="animate-spin" />
      </div>
    );
  }

  if (!tierEnum) {
    return <div>No tier enum found</div>;
  }

  return (
    <SumSubIframe
      token={token}
      email={email!}
      sumsubLevel={tierEnum}
      onApplicantReviewComplete={async () => {
        // Wait 2 seconds for server to update
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Force invalidate the profile query
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.GET_PROFILE] });
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS] });
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS_BY_TIER] });
        await queryClient.invalidateQueries({ queryKey: [APP_QUERY_KEYS.DOCUMENT_HISTORY] });
        // Reset the query to ensure fresh data
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.GET_PROFILE] });
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS] });
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.GET_DOCUMENTS_BY_TIER] });
        await queryClient.resetQueries({ queryKey: [APP_QUERY_KEYS.DOCUMENT_HISTORY] });
        // Wait for refetch to complete
        await refetch();
        // Navigate after profile is updated
        router.push("/categories");
      }}
    />
  );
};
