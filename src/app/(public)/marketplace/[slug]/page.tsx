"use client";
import React, { useState, useEffect } from "react";
import { INDUSTRIES_DATA } from "@/data/industries";
import { notFound, useParams } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight, ChevronLeft, ChevronRight } from "lucide-react";
import { NewIndustryCard } from "@/components/cards/marketplace/new-industry-card";
import { cn } from "@/utils/tailwind";

const MarketplaceDetailPage = () => {
  const params = useParams();
  const slug = params.slug;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(3);

  const currentIndustry = INDUSTRIES_DATA.find((item) => item.slug === slug);

  if (!currentIndustry) {
    notFound();
  }

  const otherIndustries = INDUSTRIES_DATA.filter((item) => item.slug !== slug);
  const totalItems = otherIndustries.length;

  useEffect(() => {
    const updateItemsPerView = () => {
      if (window.innerWidth < 768) {
        setItemsPerView(1);
      } else if (window.innerWidth < 1024) {
        setItemsPerView(2);
      } else {
        setItemsPerView(3);
      }
      setCurrentIndex(0);
    };

    updateItemsPerView();
    window.addEventListener("resize", updateItemsPerView);
    return () => window.removeEventListener("resize", updateItemsPerView);
  }, []);

  const canGoPrev = totalItems > itemsPerView && currentIndex > 0;
  const canGoNext =
    totalItems > itemsPerView && currentIndex < totalItems - itemsPerView;

  const prevSlide = () => {
    setCurrentIndex((prev) => Math.max(0, prev - itemsPerView));
  };

  const nextSlide = () => {
    setCurrentIndex((prev) =>
      Math.min(totalItems - itemsPerView, prev + itemsPerView)
    );
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-12 md:pt-24 md:pb-16 lg:pt-32 lg:pb-20">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-24 items-center mb-16 md:mb-24 lg:mb-32">
        <div className="order-2 lg:order-1">
          <div className="mb-3 md:mb-4">
            <span className="text-primary text-sm md:text-base font-medium uppercase tracking-wider">
              Marketplace / {currentIndustry.title}
            </span>
          </div>
          <h1 className="text-primary text-4xl md:text-5xl lg:text-6xl font-semibold leading-tight md:leading-[1.2] mb-4 md:mb-6">
            {currentIndustry.title}
          </h1>
          <p className="text-primary text-base md:text-lg leading-relaxed md:leading-7 mb-8 md:mb-10 max-w-xl">
            {currentIndustry.description}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 md:gap-5">
            <Button
              size="lg"
              className="bg-primary hover:bg-primary/90 text-white rounded-full text-base md:text-lg font-semibold inline-flex items-center justify-center gap-2 group w-full sm:w-auto px-7 py-3.5"
              asChild
            >
              <Link href="/register">
                REGISTER ACCOUNT
                <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-white border-primary/30 hover:border-primary/50 text-primary hover:bg-primary/5 rounded-full text-base md:text-lg font-medium inline-flex items-center justify-center gap-2 w-full sm:w-auto px-7 py-3.5"
              asChild
            >
              <Link href="/contact">
                BOOK A DEMO
              </Link>
            </Button>
          </div>
        </div>

        <div className="order-1 lg:order-2 flex justify-center lg:justify-end items-center h-48 md:h-64 lg:h-auto">
          <Image
            src={currentIndustry.icon}
            alt={`${currentIndustry.title} icon`}
            width={200}
            height={200}
            className="w-32 h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 object-contain"
          />
        </div>
      </div>

      {totalItems > 0 && (
        <div className="relative">
          <h2 className="text-primary text-3xl md:text-4xl font-semibold text-center mb-12 md:mb-16">
            Explore Other Industries
          </h2>

          <div className="overflow-hidden">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
              }}
            >
              {otherIndustries.map((industry, index) => (
                <div
                  key={index}
                  className="flex-shrink-0 px-5"
                  style={{ width: `${100 / itemsPerView}%` }}
                >
                  <NewIndustryCard
                    icon={industry.icon}
                    title={industry.title}
                    description={industry.subtitle}
                    href={industry.href}
                  />
                </div>
              ))}
            </div>
          </div>

          {totalItems > itemsPerView && (
            <>
              <Button
                variant="outline"
                size="icon"
                onClick={prevSlide}
                disabled={!canGoPrev}
                className={cn(
                  "absolute top-1/2 -translate-y-1/2 left-[-16px] md:left-[-35px] z-10 px-2 md:px-2 min-w-0",
                  "bg-white border border-gray-200 shadow-sm hover:bg-gray-50 text-primary rounded-full h-9 w-9",
                  !canGoPrev && "opacity-50 cursor-not-allowed"
                )}
                aria-label="Previous slide"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={nextSlide}
                disabled={!canGoNext}
                className={cn(
                  "absolute top-1/2 -translate-y-1/2 right-[-15px] md:right-[-35px] z-10 min-w-0",
                  "bg-white border border-gray-200 shadow-sm hover:bg-gray-50 text-primary rounded-full h-9 w-9",
                  !canGoNext && "opacity-50 cursor-not-allowed"
                )}
                aria-label="Next slide"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default MarketplaceDetailPage;
