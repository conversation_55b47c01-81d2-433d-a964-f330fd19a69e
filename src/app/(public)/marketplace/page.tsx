import { INDUSTRIES_DATA } from "@/data/industries";
// import { IndustryCard } from "@/components/cards/marketplace/industry-card"; // Comment out old card
import { NewIndustryCard } from "@/components/cards/marketplace/new-industry-card"; // Import new card
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

const MarketPlacePage = () => {
  return (
    <div className="relative overflow-x-hidden min-h-screen flex flex-col">
      <div className="absolute inset-0 top-0 h-[600px] md:h-[800px] -z-10" />
      <div className="absolute top-[-100px] md:top-[50px] right-[-300px] md:right-[-150px] lg:right-[0] xl:right-[100px] w-[600px] h-[1000px] md:w-[700px] md:h-[1200px] lg:w-[900px] lg:h-[1500px] bg-Main-Light-Color transform rotate-[15deg] md:rotate-[25deg] -z-10 opacity-50 md:opacity-100" />

      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative z-10 flex-1">
        <div className="text-center max-w-3xl mx-auto mb-16 md:mb-24">
          <h1 className="text-primary text-4xl md:text-6xl font-semibold leading-tight md:leading-[70px] mb-4 md:mb-6">
            Industries you can access
          </h1>
          <p className="text-primary text-lg leading-7">
            We have created a curated network of service providers that form the Trustnexus portal and allow you to become their client with a single click.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {INDUSTRIES_DATA.map((industry, index) => (
            <NewIndustryCard
              key={industry.title}
              icon={industry.icon}
              // icon={industry.icon} // Keep icon prop commented/logic for placeholder
              href={industry.href}
              title={industry.title}
              description={industry.subtitle!}
              // Example: highlight the second card (index 1)
              highlight={index === 1}
            />
          ))}
        </div>

        <div className="text-center mt-24 md:mt-32">
          <h2 className="text-primary text-4xl md:text-6xl font-normal leading-tight md:leading-[114px] mb-8 md:mb-10">
            Onboarded Once, Verified Everywhere
          </h2>
          <Button
            size="lg"
            className="bg-primary hover:bg-primary/90 text-white rounded-[40px] text-xl font-semibold leading-loose inline-flex items-center gap-2.5 group w-fit"
            asChild
          >
            <Link href="/register">
              REGISTER ACCOUNT
              <ArrowRight className="w-6 h-6 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </main>
    </div>
  );
};

export default MarketPlacePage;
