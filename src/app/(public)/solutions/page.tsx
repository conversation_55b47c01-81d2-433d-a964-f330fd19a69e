import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/utils/tailwind";
import { ArrowUpRight } from "lucide-react";
import Image from "next/image";

// Import solution icons as static imports
import individualIcon from "../../../../public/solutions/Individual.png";
import companyIcon from "../../../../public/solutions/Company.png";
import systemIcon from "../../../../public/solutions/System.png";
import { TrustedByFooter } from "@/components/auth/trusted-by-footer";

const SolutionsPage = () => {
  return (
    <div className="relative w-full min-h-screen">
      <div className="relative z-10">
        {/* For Individuals Section */}
        <section className="w-full px-4 md:px-6 pt-16 md:pt-40 md:max-w-6xl md:mx-auto">
          <div className="md:hidden flex justify-center mb-12">
            <div className="relative w-36 h-44 md:w-44 md:h-48">
              <Image 
                src={individualIcon}
                alt="KYC Icon" 
                fill
                className="object-contain"
              />
            </div>
          </div>

          <div className="md:grid md:grid-cols-2 md:gap-8 items-center">
            <div className="space-y-6 md:space-y-8">
              <h2 className="text-primary font-poppins text-4xl md:text-5xl lg:text-6xl font-semibold leading-tight">
                For Individuals
                <br />
                KYC
              </h2>
              <p className="text-primary font-poppins text-lg font-medium leading-7 w-full max-w-sm md:max-w-lg">
                Tired of uploading your passport and utility bill for a million providers? Perform your last KYC today.
              </p>
              <Link href="/register" className="inline-block w-full md:w-auto">
                <Button 
                  className={cn(
                    "bg-primary text-white rounded-full px-7 py-3 md:py-4",
                    "text-lg md:text-xl font-semibold uppercase flex items-center justify-center gap-2 font-poppins",
                    "w-full md:w-auto"
                  )}
                >
                  Register
                  <ArrowUpRight className="h-5 w-5 md:h-6 md:w-6 text-[--border]" />
                </Button>
              </Link>
            </div>
            <div className="hidden md:flex justify-center">
              <div className="relative w-36 h-44 md:w-44 md:h-48">
                <Image 
                  src={individualIcon}
                  alt="KYC Icon" 
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </section>

        {/* For Businesses Section */}
        <section className="w-full px-4 md:px-6 pt-16 md:pt-32 md:max-w-6xl md:mx-auto">
          <div className="md:hidden flex justify-center mb-12">
            <div className="relative w-36 h-44 md:w-44 md:h-48">
              <Image 
                src={companyIcon}
                alt="KYB Icon" 
                fill
                className="object-contain"
              />
            </div>
          </div>

          <div className="md:grid md:grid-cols-2 md:gap-8 items-center">
            <div className="md:order-2 space-y-6 md:space-y-8">
              <h2 className="text-primary font-poppins text-4xl md:text-5xl lg:text-6xl font-semibold leading-tight">
                For Businesses
                <br />
                KYB
              </h2>
              <p className="text-primary font-poppins text-lg font-medium leading-7 w-full max-w-sm md:max-w-lg">
                Tired of constantly sending your documents and UBO details to auditors, banks and law firms? Create your business e-ID and onboard to any service with one click.
              </p>
              <Link href="/register" className="inline-block w-full md:w-auto">
                <Button 
                  className={cn(
                    "bg-primary text-white rounded-full px-7 py-3 md:py-4",
                    "text-lg md:text-xl font-semibold uppercase flex items-center justify-center gap-2 font-poppins",
                    "w-full md:w-auto"
                  )}
                >
                  Register
                  <ArrowUpRight className="h-5 w-5 md:h-6 md:w-6 text-[--border]" />
                </Button>
              </Link>
            </div>
            <div className="hidden md:flex justify-center md:order-1">
              <div className="relative w-36 h-44 md:w-44 md:h-48">
                <Image 
                  src={companyIcon}
                  alt="KYB Icon" 
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Software as a Service Section */}
        <section className="w-full px-4 md:px-6 pt-16 md:pt-32 md:max-w-6xl md:mx-auto">
          <div className="md:hidden flex justify-center mb-12">
            <div className="relative w-36 h-44 md:w-44 md:h-48">
              <Image 
                src={systemIcon}
                alt="SaaS Icon" 
                fill
                className="object-contain"
              />
            </div>
          </div>

          <div className="md:grid md:grid-cols-2 md:gap-8 items-center">
            <div className="space-y-6 md:space-y-8">
              <h2 className="text-primary font-poppins text-4xl md:text-5xl lg:text-6xl font-semibold leading-tight">
                Software-as-a-
                <br />
                Service
              </h2>
              <p className="text-primary font-poppins text-lgleading-7 w-full max-w-sm md:max-w-lg">
                More than just onboarding.
                <br />
                Onboard your clients safely, securely and hassle free with end-to-end KYC & KYB services through the Trustnexus portal.
              </p>
              <Link href="/contact" className="inline-block w-full md:w-auto">
                <Button 
                  className={cn(
                    "bg-primary text-white rounded-full px-7 py-3 md:py-4",
                    "text-lg md:text-xl font-semibold uppercase flex items-center justify-center gap-2 font-poppins",
                    "w-full md:w-auto"
                  )}
                >
                  Book a Demo
                  <ArrowUpRight className="h-5 w-5 md:h-6 md:w-6 text-[--border]" />
                </Button>
              </Link>
            </div>
            <div className="hidden md:flex justify-center">
              <div className="relative w-36 h-44 md:w-44 md:h-48">
                <Image 
                  src={systemIcon}
                  alt="SaaS Icon" 
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="w-full px-4 md:px-6 pt-16 md:pt-32 text-center">
          <div className="md:max-w-6xl md:mx-auto">
            <h2 className="text-primary font-poppins text-4xl md:text-5xl lg:text-6xl font-normal leading-tight tracking-tight md:tracking-tighter mx-auto">
              Ready to experience the future of KYC?
            </h2>
            <p className="text-primary font-poppins text-base md:text-lg font-normal mt-4 md:mt-6 mx-auto max-w-xs md:max-w-2xl">
              Reach out to us to learn more about Trustnexus and how we can empower you or your organization
            </p>
            
            <Link href="/contact" className="inline-block mt-8 md:mt-12 w-full md:w-auto">
              <Button 
                className={cn(
                  "border border-black bg-white text-black rounded-full px-4 py-3 md:py-4",
                  "text-lg md:text-xl font-semibold uppercase flex items-center justify-center font-poppins",
                  "w-full md:w-auto",
                  "transition-all duration-200 hover:bg-black hover:text-white"
                )}
              >
                Contact us
              </Button>
            </Link>
          </div>
        </section>

        <TrustedByFooter />
      </div>
    </div>
  );
};

export default SolutionsPage;
