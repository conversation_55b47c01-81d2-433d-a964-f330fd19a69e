"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Minus, Plus } from "lucide-react";
import { cn } from "@/utils/tailwind";
import Link from "next/link";

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQSection {
  category: string;
  items: FAQItem[];
}

const faqData: FAQSection[] = [
  {
    category: "For Individuals",
    items: [
      {
        question: "What is trustnexus and how does it help me?",
        answer:
          "Trustnexus is your gateway to a seamless digital identity experience. Say goodbye to repetitive KYC checks! With trustnexus, you create a secure e-ID, manage your personal data, and decide who gets access—all in one place. It's your data, your control.",
      },
      {
        question: "How does Trustnexus simplify KYC for me?",
        answer: "No more uploading your passport or utility bill for every new service. Perform your KYC once on Trustnexus, and use your verified e-ID to access multiple services with just a click. It's fast, secure, and hassle-free.",
      },
      {
        question: "Can I control who accesses my data?",
        answer: "Absolutely! Trustnexus puts you in charge. You decide which service providers can access your information and can revoke consent at any time. Transparency and control are at the core of what we do.",
      },
      {
        question: "Is my data safe with trustnexus?",
        answer: "Yes! Trustnexus uses advanced security measures and operates via a federated community network, ensuring your data is protected and only shared with your explicit consent.",
      },
      {
        question: "What is the Tier System for KYC?",
        answer: "The Tier System allows you to complete different levels of verification based on your needs. Start with basic verification and upgrade to higher tiers for access to more services—all within the same portal.\n\n\"Entry\" is the initial level when you register.\n\n\"Basic\" is the standard level of KYC required, including identification documents, utility bills and tax information. This allows a user access to most services like telecommunications, gaming and even financial services (with a limit in transaction value).\n\n\"Standard\" permits users who upload their source of funds (through an employment contract or payslip) in addition to their \"Basic\" tier information, to access all financial services as well as law firms and insurance companies.\n\n\"Pro\" gives the user ultimate access to all provided services including trading and crypto asset services.",
      },
    ],
  },
  {
    category: "For Businesses",
    items: [
      {
        question: "How does Trustnexus simplify KYB for my business?",
        answer:
          "Stop sending the same documents to auditors, banks, and law firms repeatedly. With trustnexus, you create a business e-ID, complete KYB once, and onboard to multiple services with just one click.",
      },
      {
        question: "What is the Tier System for KYB?",
        answer: "The Tier System allows businesses to complete varying levels of verification, from basic to advanced, depending on the services they need. It's flexible, efficient, and designed to meet your business requirements.\n\n\"Entry\" is the initial level when your business registers.\n\n\"Basic\" services require a business to create an e-ID (including company name, registration number, country of incorporation, registered address, information of the authorized representative, corporate certificates and tax information), to gain instant access to most services like telecommunications, gaming and even financial services (with a limit in transaction value).\n\n\"Standard\" permits businesses who upload their source of funds (e.g. financial statements) and UBO information, in addition to \"Basic\" tier information, to access all financial services as well as law firms and auditors.\n\n\"Pro\" services like trading require businesses to complete industry specific questionnaires depending on the service they wish to access – all provided on the Trustnexus portal.",
      },
    ],
  },
  {
    category: "Compliance-as-a-Service",
    items: [
      {
        question:
          "How does trustnexus ensure secure onboarding for my clients?",
        answer:
          "Trustnexus provides end-to-end KYC and KYB services, ensuring your clients' data is verified securely and efficiently. This reduces onboarding time while maintaining compliance with regulations.",
      },
      {
        question: "Can I integrate Trustnexus with my existing systems?",
        answer: "Yes! Trustnexus is designed for interoperability and can seamlessly integrate with your current systems, making onboarding and verification a breeze.",
      },
      {
        question: "How does Trustnexus help with compliance?",
        answer: "Built by legal experts with deep knowledge of Greek, Cypriot, and European KYC/AML regulations, Trustnexus ensures your business stays compliant while simplifying the verification process.",
      },
    ],
  },
  {
    category: "For Everyone",
    items: [
      {
        question: "What makes trustnexus different from other KYC platforms?",
        answer:
          "Trustnexus is not just a KYC platform. It goes beyond traditional KYC by creating a federated community network. This means verified identities are interoperable across services, reducing redundancy and empowering users with control over their data.",
      },
      {
        question: "What is federated assurance, and why does it matter?",
        answer: "Federated assurance means your verified identity is recognized and trusted across multiple platforms and services. This enables peer-verification across the trustnexus portal network ensuring a safe and secure environment with minimized effort and reduced risk.",
      },
      {
        question: "How do I get started with trustnexus?",
        answer: "Simply sign up on the Trustnexus portal, create your e-ID, and complete your KYC or KYB. Once verified, you're ready to access a marketplace of services with ease.",
      },
      {
        question:
          "Is trustnexus only for individuals, or can businesses use it too?",
        answer: "Trustnexus is for everyone! Whether you're an individual or company tired of repetitive KYC/KYB checks or a business looking to streamline client onboarding, Trustnexus has you covered.",
      },
      {
        question:
          "Is Trustnexus compliant with GDPR and other data protection laws?",
        answer: "Absolutely. Trustnexus is designed by legal experts with a deep understanding of GDPR and other data protection regulations, ensuring full compliance at every step.",
      },
      {
        question: "What's next for trustnexus?",
        answer: "We're constantly innovating to redefine the future of KYC and digital identity. Stay tuned for new features, expanded services, and even greater interoperability across platforms.",
      },
    ],
  },
];

// Reusable Accordion Item Component
const AccordionItem = ({
  item,
  isOpen,
  onClick,
}: {
  item: FAQItem;
  isOpen: boolean;
  onClick: () => void;
}) => {
  return (
    <div
      className={cn(
        "bg-white border-b border-black" // Adjusted borders based on Figma
        // Remove top border for the first item in a section potentially? Needs logic in map.
      )}
    >
      <button
        className="flex justify-between items-center w-full px-6 py-6 text-left"
        onClick={onClick}
      >
        <span className="text-primary text-xl font-semibold  leading-tight md:w-auto w-72">
          {item.question}
        </span>
        <div className="w-6 h-6 relative overflow-hidden flex-shrink-0">
          {/* Simple Plus/Minus based on Figma's general style */}
          {isOpen ? (
            <Minus className="w-6 h-6 text-black" /> // Using lucide-react icons
          ) : (
            <Plus className="w-6 h-6 text-black" /> // Using lucide-react icons
          )}
          {/* Alternative: Using the div lines from Figma (more complex) */}
          {/* {isOpen ? (
             <div className="w-3.5 h-0 left-[5px] top-[12px] absolute outline outline-2 outline-offset-[-1px] outline-black" />
           ) : (
             <>
               <div className="w-0 h-3.5 left-[12px] top-[5px] absolute outline outline-2 outline-offset-[-1px] outline-black" />
               <div className="w-3.5 h-0 left-[5px] top-[12px] absolute outline outline-2 outline-offset-[-1px] outline-black" />
             </>
           )} */}
        </div>
      </button>
      {isOpen && (
        <div className="px-6 pb-6">
          {/* Adjusted padding */}
          <p className="text-zinc-600 text-base font-normal  leading-relaxed">
            {item.answer}
          </p>
        </div>
      )}
    </div>
  );
};

const FAQPage = () => {
  const [openIndices, setOpenIndices] = useState<{
    [key: string]: number | null;
  }>({}); // Store open index per category

  const toggleFAQ = (category: string, index: number) => {
    setOpenIndices((prev) => ({
      ...prev,
      [category]: prev[category] === index ? null : index,
    }));
  };

  // Manually open the first item of the first category by default
  useState(() => {
    if (faqData.length > 0 && faqData[0].items.length > 0) {
      setOpenIndices({ [faqData[0].category]: 0 });
    }
  });

  return (
    <div className="relative  overflow-x-hidden min-h-screen flex flex-col">
      {/* Background Elements - Need careful positioning */}
      <div className="absolute top-0 left-0 w-full h-[600px] md:h-[993px] -z-10" />
      <div className="absolute top-[-150px] md:top-[-217px] right-[-200px] md:left-[calc(50%+170px)] lg:left-[1050px] w-[500px] h-[800px] md:w-[714px] md:h-[1191.44px] bg-Main-Light-Color -z-10" />

      {/* Use existing Navbar */}
      {/* <Navbar showNavItems={true} className="container relative z-20" /> */}

      <main className="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative z-10">
        {/* Header Section */}
        <div className="text-center mb-12 md:mb-16">
          <h1 className="text-primary text-4xl md:text-6xl font-semibold  leading-tight md:leading-[70px] mb-3 md:mb-4">
            FAQs
          </h1>
          <p className="text-primary text-lg font-normal  leading-7">
            Any questions? We are here to help!
          </p>
        </div>

        {/* FAQ Sections */}
        <div className="max-w-5xl mx-auto flex flex-col gap-12 md:gap-16">
          {faqData.map((section) => (
            <div key={section.category}>
              {/* Section Title */}
              <h2 className="text-neutral-400 text-3xl md:text-4xl font-medium  leading-snug md:leading-[60px] mb-8 md:mb-12">
                {section.category}
              </h2>
              {/* Accordion Items */}
              <div className="flex flex-col border-t border-black">
                {" "}
                {/* Add top border for the container */}
                {section.items.map((item, index) => (
                  <AccordionItem
                    key={index}
                    item={item}
                    isOpen={openIndices[section.category] === index}
                    onClick={() => toggleFAQ(section.category, index)}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Have more questions? Section */}
        <div className="text-center mt-24 md:mt-32">
          <h2 className="text-primary text-3xl md:text-6xl font-normal  leading-tight md:leading-[114px] mb-8 md:mb-10">
            Have more questions?
          </h2>
          <Button
            variant="outline"
            size="lg" // Adjust size as needed
            className="rounded-[50px] outline outline-1 outline-black text-black text-xl font-semibold  leading-7 px-8 md:px-12 py-3 md:py-4 inline-flex justify-center items-center hover:bg-gray-100 w-fit" // Adjusted styling
            asChild
          >
            <Link href="/contact">Contact us</Link>
          </Button>
        </div>
      </main>

      {/* Use existing Footer */}
      {/* <Footer className="container relative z-10 mt-auto" /> */}
    </div>
  );
};

export default FAQPage;
