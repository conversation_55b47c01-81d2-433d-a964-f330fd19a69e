'use client'
import { BackgroundLanding } from "@/components/layout/background-landing";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { usePathname } from "next/navigation";
import { Background } from "@/components/layout/background";

interface LayoutProps {
  children: React.ReactNode;
}
const Layout = ({ children }: LayoutProps) => {
  const pathname = usePathname();
  return (
    <div className="flex flex-col min-h-screen relative">
      <div className="relative flex-1 flex flex-col overflow-hidden">
        {pathname === "/" ? <BackgroundLanding /> : <Background />}
        <Navbar showNavItems={true} className="fixed top-0 left-0 right-0 z-50 w-full bg-white shadow-sm" />
        <main className="flex-1 flex flex-col relative z-10 pt-[104px] lg:pt-[135px]">{children}</main>
        <Footer className="container relative z-10" />
      </div>
    </div>
  );
};

export default Layout;
