import { TrustedByFooter } from "@/components/auth/trusted-by-footer";
import { Button } from "@/components/ui/button";
import { ArrowUpRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const ContactPage = () => {
  return (
    <div className="relative w-full min-h-screen flex flex-col">
      
      {/* Hero Section */}
      <section className="container mt-16 md:mt-24 text-center relative z-10">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-semibold text-primary leading-tight tracking-tight">
          Contact us
          <span className="block">Our team is here to help you</span>
        </h1>
        <p className="mt-6 md:mt-10 text-base md:text-lg text-primary max-w-3xl mx-auto font-normal">
          Trust Beyond Industry Borders: The Federated KYC Solution
        </p>
        
        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 md:gap-6 mt-8 md:mt-12 justify-center">
          <Link href="/register" className="sm:w-[187px]">
            <Button 
              variant="default" 
              className="rounded-[40px] h-14 w-full px-5 flex items-center justify-center gap-2 font-semibold text-xs uppercase"
            >
              REGISTER ACCOUNT
              <ArrowUpRight className="h-5 w-5 md:h-6 md:w-6 text-[--border]" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Contact Form */}
      <section className="container flex justify-center my-16 md:my-24 relative z-10">
        <div className="w-full max-w-[576px] p-6 md:p-7 bg-white/60 border border-charity-border rounded-[15px] backdrop-blur-md shadow-[0px_40px_68px_-20px_rgba(202,202,202,0.2)]">
          <div className="flex flex-col items-center gap-8">
            <h2 className="text-2xl font-semibold text-primary text-center">
              Contact our team
            </h2>
            
            <form className="w-full max-w-[450px] space-y-6">
              <div className="space-y-3">
                <label className="block text-primary text-base font-normal">
                  First name
                </label>
                <input 
                  type="text" 
                  placeholder="First name" 
                  className="w-full h-12 px-4 rounded-[40px] border border-input bg-white/60 text-sm focus:outline-none focus:ring-1 focus:ring-primary placeholder-charity-border"
                />
              </div>
              
              <div className="space-y-3">
                <label className="block text-primary text-base font-normal">
                  Last name
                </label>
                <input 
                  type="text" 
                  placeholder="Last name" 
                  className="w-full h-12 px-4 rounded-[40px] border border-input bg-white/60 text-sm focus:outline-none focus:ring-1 focus:ring-primary placeholder-charity-border"
                />
              </div>
              
              <div className="space-y-3">
                <label className="block text-primary text-base font-normal">
                  Email address
                </label>
                <input 
                  type="email" 
                  placeholder="Email address" 
                  className="w-full h-12 px-4 rounded-[40px] border border-input bg-white/60 text-sm focus:outline-none focus:ring-1 focus:ring-primary placeholder-charity-border"
                />
              </div>
              
              <div className="space-y-3">
                <label className="block text-primary text-base font-normal">
                  Company
                </label>
                <input 
                  type="text" 
                  placeholder="Company name" 
                  className="w-full h-12 px-4 rounded-[40px] border border-input bg-white/60 text-sm focus:outline-none focus:ring-1 focus:ring-primary placeholder-charity-border"
                />
              </div>
              
              <div className="space-y-3">
                <label className="block text-primary text-base font-normal">
                  Message
                </label>
                <textarea 
                  placeholder="Your message" 
                  rows={4}
                  className="w-full px-4 py-3 rounded-[20px] border border-input bg-white/60 text-sm focus:outline-none focus:ring-1 focus:ring-primary placeholder-charity-border resize-none"
                />
              </div>
              
              <div className="pt-6 flex justify-center">
                <Button 
                  variant="default" 
                  className="w-[248px] h-[56px] rounded-[40px] font-semibold text-base uppercase"
                >
                  SEND MESSAGE
                </Button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <div className="relative z-10">
        <TrustedByFooter />
      </div>
    </div>
  );
};

export default ContactPage;
