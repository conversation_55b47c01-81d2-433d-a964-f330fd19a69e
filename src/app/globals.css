@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #1C3F3C;
  --secondary: #d4e6d7;
  --border: #C8E2CE;
  --input: #888E92;
  --radius: 8px;
  --card-foreground: rgba(255, 255, 255, 0.6);
  --black: #231F20;
  --light-gray: #E3E3E3;
  --dark-gray: #60636B;
  --normal-gray: #898989;
}

html,
body {
  height: 100%;
}

body {
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
}

#__next {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
.no-scrollbar {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Custom scrollbar for alphabet navigation */
.alphabet-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.alphabet-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.alphabet-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.alphabet-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Firefox scrollbar styling */
.alphabet-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

/* Hide scrollbar initially, show on hover */
.alphabet-scrollbar-hidden::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.alphabet-scrollbar-hidden:hover::-webkit-scrollbar {
  width: 4px;
}

.alphabet-scrollbar-hidden {
  scrollbar-width: none;
}

.alphabet-scrollbar-hidden:hover {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}
