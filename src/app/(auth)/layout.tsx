"use client";
import React from "react";
import "./layout.css";
import { TrustedBy } from "@/components/auth/trusted-by";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { Background } from "@/components/layout/background";
import { usePathname } from "next/navigation";

const AuthLayout = ({ children }: Readonly<{ children: React.ReactNode }>) => {
  const pathname = usePathname();
  
  const getHeadingText = () => {
    if (pathname.includes('/login')) {
      return 'Welcome back';
    } else if (pathname.includes('/register')) {
      return 'Welcome to trustnexus';
    } else if (pathname.includes('/forgot-password')) {
      return 'Forgot Password';
    } else if (pathname.includes('/reset-password')) {
      return 'Reset Password';
    }
    return 'Welcome to trustnexus';
  };
  
  return (
    <div className="overflow-hidden">
      <Background />
      <section className=" min-h-screen w-full flex justify-center relative">
        <div className="w-full container flex flex-col">
          <Navbar showNavItems={false} className="pt-[40px] pb-[20px]" />

          <div className="flex flex-col-reverse items-center gap-8 sm:gap-[50px] flex-1 lg:flex-row py-10">
            <div className="block lg:hidden">
              <TrustedBy />
            </div>

            {children}

            <div className="flex flex-col gap-[82px]">
              <h1 className="text-[36px] leading-[42px] text-primary font-medium">
                {getHeadingText()}
              </h1>

              <div className="hidden lg:block">
                <TrustedBy />
              </div>
            </div>
          </div>

          <Footer />
        </div>
      </section>
    </div>
  );
};
export default AuthLayout;
