"use client";

import React from "react";
import { SelectRegistrationType } from "@/components/auth/select-registration-type";
import { FormCard } from "@/components/register/form-card";

import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { UserRegistration } from "@/components/auth/user-registration";
import { Form } from "@/components/common/form";
import { useRegister } from "@/react-query/auth-hooks";
import { RegistrationType } from "@/types/user";

const registrationSchema = z.object({
  // Step 1
  registrationType: z.object({
    type: z.nativeEnum(RegistrationType),
  }),

  personalInfo: z
    .object({
      firstName: z.string().optional(),
      lastName: z.string().optional(),
      email: z.string().email(),
      password: z.string().min(8),
      confirmPassword: z.string().min(8),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    }),

  step: z.number(),
})
.superRefine((data, ctx) => {
  if (data.registrationType.type === RegistrationType.KYC) {
    if (!data.personalInfo.firstName || data.personalInfo.firstName.trim() === "") {
      ctx.addIssue({
        code: "custom",
        message: "First name is required",
        path: ["personalInfo", "firstName"],
      });
    }
    if (!data.personalInfo.lastName || data.personalInfo.lastName.trim() === "") {
      ctx.addIssue({
        code: "custom",
        message: "Last name is required",
        path: ["personalInfo", "lastName"],
      });
    }
  }
});

type StepNamesType = keyof Omit<z.infer<typeof registrationSchema>, "step">;

// Define the steps, each step is a component
const steps: {
  [key in StepNamesType]: React.FC<{
    isPending: boolean;
    registrationType: RegistrationType;
  }>;
} = {
  registrationType: SelectRegistrationType,
  personalInfo: UserRegistration,
};

const renderStep = (
  step: StepNamesType,
  isPending: boolean,
  registrationType: RegistrationType,
) => {
  const Component = steps[step];
  return (
    <Component isPending={isPending} registrationType={registrationType} />
  );
};

// Define a mapping from step number to step name
export const STEPS_TO_STEP_NAMES: { [key: number]: StepNamesType } = {
  1: "registrationType",
  2: "personalInfo",
};

export type RegistrationInputs = z.infer<typeof registrationSchema>;

export const Register = () => {
  const { mutate: register, isPending } = useRegister();

  const form = useForm<RegistrationInputs>({
    resolver: zodResolver(registrationSchema),
    mode: "onChange",
    defaultValues: {
      registrationType: {
        type: undefined,
      },
      personalInfo: {
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        confirmPassword: "",
      },

      step: 1,
    },
  });

  const currentStep = form.watch("step");
  const currentStepName = STEPS_TO_STEP_NAMES[currentStep];

  const onSubmit = async (data: RegistrationInputs) => {
    register(data);
  };

  const registrationType = form.watch("registrationType.type");

  return (
    <FormCard className="px-5">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col gap-6 pt-6 w-full max-w-lg"
        >
          {renderStep(currentStepName, isPending, registrationType)}
        </form>
      </Form>
    </FormCard>
  );
};
