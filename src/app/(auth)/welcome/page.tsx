import { WelcomeStepCard } from "@/components/auth/welcome-step";
import { getSumSubToken } from "@/react-query/query/sumsub-token";
import { RegistrationType, SUMSUB_LEVEL } from "@/types/user";
import { getUserCookies } from "@/utils/get-token";
import { redirect } from "next/navigation";

export default async function WelcomePage() {
  const { registrationType, email } = await getUserCookies();

  const token = await getSumSubToken(
    registrationType === RegistrationType.KYC
      ? SUMSUB_LEVEL.TIER1
      : SUMSUB_LEVEL.KYB_TIER1,
  );
  if (!token || !email || !registrationType) {
    return redirect("/");
  }

  console.log("token", token);

  return (
    <WelcomeStepCard token={token} email={email} userType={registrationType} />
  );
}
