import * as React from "react";
import { SVGProps } from "react";
const KycIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width="94" height="111" viewBox="0 0 94 111" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1051_4787)">
<path d="M44.9606 60.0019C31.1971 60.0019 26.0648 45.3778 26.0648 36.1593C26.0648 26.9408 34.541 18.7793 44.9606 18.7793C55.3802 18.7793 63.8564 26.5788 63.8564 36.1593C63.8564 41.174 62.4606 46.6182 60.1199 50.7255C56.6644 56.7924 51.4207 60.0019 44.9606 60.0019ZM44.9606 22.6405C36.6783 22.6405 29.9419 28.7073 29.9419 36.1593C29.9419 43.6113 34.0176 56.1408 44.9606 56.1408C50.0153 56.1408 53.9796 53.6793 56.7468 48.8239C58.7726 45.2716 59.9793 40.5369 59.9793 36.1641C59.9793 28.7073 53.243 22.6453 44.9606 22.6453V22.6405Z" fill="#1C3F3C"/>
<path d="M74.1355 96.5466H15.592C13.1204 96.5466 11.1092 94.5436 11.1092 92.0821V83.4862C11.1092 72.6895 17.5402 62.7084 27.4897 58.0605L29.1375 61.5549C20.5401 65.5705 14.9862 74.176 14.9862 83.4862V92.0821C14.9862 92.4152 15.2576 92.6854 15.592 92.6854H74.1355C74.4698 92.6854 74.7412 92.4152 74.7412 92.0821V83.4862C74.7412 74.1809 69.1874 65.5705 60.59 61.5549L62.2378 58.0605C72.1921 62.7084 78.6231 72.6895 78.6231 83.4862V92.0821C78.6231 94.5436 76.6119 96.5466 74.1403 96.5466H74.1355Z" fill="#1C3F3C"/>
</g>
<defs>
<clipPath id="clip0_1051_4787">
<rect width="67.5091" height="77.7636" fill="white" transform="translate(11.1092 18.7803)"/>
</clipPath>
</defs>
</svg>

);
export default KycIcon;
