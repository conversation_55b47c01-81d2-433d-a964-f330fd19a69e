import * as React from "react";
import { SVGProps } from "react";
const UserIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={34}
    height={34}
    fill="none"
    {...props}
  >
    <g
      stroke="#1C3F3C"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2.5}
      clipPath="url(#a)"
    >
      <path d="M17 21.25a8.5 8.5 0 1 0 0-17 8.5 8.5 0 0 0 0 17ZM4.25 28.688C6.823 24.242 11.495 21.25 17 21.25s10.177 2.992 12.75 7.438" />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h34v34H0z" />
      </clipPath>
    </defs>
  </svg>
);
export default UserIcon;
