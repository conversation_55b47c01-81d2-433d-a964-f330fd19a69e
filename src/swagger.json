{"openapi": "3.0.1", "info": {"title": "Core.EjKyc.API", "version": "1.0"}, "paths": {"/api/v1/Accounts": {"get": {"tags": ["Accounts"], "summary": "Searches for accounts based on the provided email and password.", "description": "This method retrieves accounts from the database that match the given email and password, \r\nmaps the results to a list of Core.EjKyc.Service.Accounts.Query.AccountsResponse, and returns them in a paginated response.", "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "password", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}}}}}, "post": {"tags": ["Accounts"], "summary": "Inserts or updates an account based on the provided command.", "description": "This method processes the given command to either insert a new account or update an existing one.\r\nThe result is mapped to an Core.EjKyc.Service.Accounts.Query.AccountsResponse and returned.", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpsertAccountsCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpsertAccountsCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertAccountsCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertAccountsCommand"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UpsertAccountsCommand"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UpsertAccountsCommand"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/UpsertAccountsCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}}}}}}, "/api/v1/Accounts/get-all-users": {"get": {"tags": ["Accounts"], "summary": "Retrieves a list of all user accounts from the database.", "description": "This method fetches all user accounts, maps them to the Core.EjKyc.Service.Accounts.Query.AccountsResponse model, \r\nand returns them in a paginated response. The response includes metadata such as total count, \r\npage size, and page number.", "responses": {"200": {"description": "Returns the list of user accounts successfully.", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponseApiResponse"}}}}, "500": {"description": "If there is an internal server error."}}}}, "/api/v1/Accounts/get-all-users-by-company-ids": {"get": {"tags": ["Accounts"], "parameters": [{"name": "companyIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int32"}}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BOAccountDtoApiSearchResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BOAccountDtoApiSearchResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BOAccountDtoApiSearchResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/BOAccountDtoApiSearchResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/BOAccountDtoApiSearchResponseApiResponse"}}}}}}}, "/api/v1/Accounts/get-all-clients-pending-approval-by-company-id": {"get": {"tags": ["Accounts"], "parameters": [{"name": "companyId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusDtoApiSearchResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusDtoApiSearchResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusDtoApiSearchResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusDtoApiSearchResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusDtoApiSearchResponseApiResponse"}}}}}}}, "/api/v1/Accounts/get-all-clients-pending-approval": {"get": {"tags": ["Accounts"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusApiSearchResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusApiSearchResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusApiSearchResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusApiSearchResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountWithApprovalStatusApiSearchResponseApiResponse"}}}}}}}, "/api/v1/Accounts/get-approval-status-by-user-id": {"get": {"tags": ["Accounts"], "parameters": [{"name": "userid", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}}}}}}, "/api/v1/Accounts/get-approval-status-by-user-id-and-company-id": {"get": {"tags": ["Accounts"], "parameters": [{"name": "userid", "in": "query", "schema": {"type": "string"}}, {"name": "companyId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/ApprovalStatusApiResponse"}}}}}}}, "/api/v1/Accounts/{userid}": {"get": {"tags": ["Accounts"], "summary": "Retrieves account details for a specific user based on their unique identifier.", "description": "This method validates the provided user ID, retrieves the corresponding account details \r\nfrom the database using the Core.EjKyc.Service.Interface.IAccountsService, and maps the result to an \r\nCore.EjKyc.Service.Accounts.Query.AccountsResponse object.\r\nIf the user ID format is invalid, an error response is returned.", "parameters": [{"name": "userid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}}}}}}, "/api/v1/Accounts/get-user-by-userid-with-company-and-role/{userid}": {"get": {"tags": ["Accounts"], "parameters": [{"name": "userid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}}}}}}, "/api/v1/Accounts/update-password": {"post": {"tags": ["Accounts"], "summary": "Updates the password for an account.", "description": "This endpoint allows updating the password for an existing account. \r\nEnsure that the provided command contains valid and complete data.", "requestBody": {"description": "The command containing the necessary information to update the password.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordCommand"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UpdatePasswordCommand"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UpdatePasswordCommand"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/UpdatePasswordCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}}}}}}, "/api/v1/Accounts/create-consent-account-company": {"post": {"tags": ["Accounts"], "summary": "Creates a consent account for a company based on the provided command.", "description": "This method interacts with the Core.EjKyc.Service.Interface.IAccountsService to create a consent account \r\nfor a company and maps the result to a Core.EjKyc.Service.Accounts.Query.UserCompanyConsentDto.", "requestBody": {"description": "The Core.EjKyc.Service.Accounts.Command.CreateConsentAccountCompanyCommand containing the details \r\nrequired to create the consent account, such as the user ID and company ID.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoApiResponse"}}}}}}}, "/api/v1/Accounts/upsert-approval-client-company": {"post": {"tags": ["Accounts"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyCommand"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyCommand"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyCommand"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyDtoApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyDtoApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyDtoApiResponse"}}}}}}}, "/api/v1/Accounts/revoke-consent-account-company": {"post": {"tags": ["Accounts"], "summary": "Revokes the consent for a user to associate with a specific company.", "requestBody": {"description": "The command containing the details required to revoke the consent, \r\nincluding the user ID, company ID, and applicant ID.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/CreateConsentAccountCompanyCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/v1/Accounts/get-consent-company-for-userid/{userid}": {"get": {"tags": ["Accounts"], "summary": "Retrieves a list of companies that have received consent from a specific user.", "description": "This method fetches the consented companies for the specified user by interacting with the accounts service.", "parameters": [{"name": "userid", "in": "path", "description": "The unique identifier of the user whose consented companies are to be retrieved.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoListApiResponse"}}}}}}}, "/api/v1/Accounts/get-summary-report-for-applicantid/{aplicantId}/{registrationtype}": {"get": {"tags": ["Accounts"], "summary": "Retrieves a summary report for a specific user based on their user ID.", "description": "This method utilizes the M:Core.EjKyc.Service.Interface.IAccountsService.GetSummaryReportByApplicantId(System.String,System.String) service method to fetch the summary report\r\nand maps the result to a string before returning it.", "parameters": [{"name": "aplicantId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "registrationtype", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/v1/Accounts/get-consent-history-companies-for-userid/{userid}": {"get": {"tags": ["Accounts"], "summary": "Retrieves the consent history for users associated with a specific company based on the provided user ID.", "description": "This method interacts with the Core.EjKyc.Service.Interface.IAccountsService to fetch the consent history data \r\nand maps it to a list of Core.EjKyc.Service.Accounts.Query.UserCompanyConsentHistoryDto objects.", "parameters": [{"name": "userid", "in": "path", "description": "The unique identifier of the user whose consent history is to be retrieved.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}}}}}}, "/api/v1/Accounts/get-documents-for-applicantid/{aplicantId}": {"get": {"tags": ["Accounts"], "summary": "Retrieves a list of documents associated with a specific applicant ID.", "description": "This method fetches documents for the given applicant ID by interacting with the accounts service.", "parameters": [{"name": "aplicantId", "in": "path", "description": "The unique identifier of the applicant whose documents are to be retrieved.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}}}}}}, "/api/v1/Accounts/get-documents-for-applicantid/{applicantId}/{companyId}": {"get": {"tags": ["Accounts"], "parameters": [{"name": "applicantId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}}}}}}, "/api/v1/Accounts/get-documents-for-applicantid-all-tiers/{aplicantId}": {"get": {"tags": ["Accounts"], "parameters": [{"name": "aplicantId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TierSubSubDataWithApplicantIdApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TierSubSubDataWithApplicantIdApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TierSubSubDataWithApplicantIdApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/TierSubSubDataWithApplicantIdApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/TierSubSubDataWithApplicantIdApiResponse"}}}}}}}, "/api/v1/Accounts/old-get-documents-for-applicantid/{aplicantId}": {"get": {"tags": ["Accounts"], "parameters": [{"name": "aplicantId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}}}}}}, "/api/v1/Company": {"get": {"tags": ["Company"], "summary": "Gets a list of Companies.", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiSearchResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiSearchResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiSearchResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiSearchResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiSearchResponseApiResponse"}}}}}}, "post": {"tags": ["Company"], "summary": "Creates a Company.", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Entity": {"type": "string"}, "EntityDto.company_id": {"type": "integer", "format": "int32"}, "EntityDto.logo": {"type": "string"}, "EntityDto.image": {"type": "string"}, "EntityDto.description": {"type": "string"}, "EntityDto.name": {"type": "string"}, "EntityDto.category": {"type": "string"}, "EntityDto.required_individual_tier": {"type": "integer", "format": "int32"}, "EntityDto.required_corporate_tier": {"type": "integer", "format": "int32"}, "EntityDto.webhook_target_url": {"type": "string"}, "EntityDto.s3LogoUrl": {"type": "string"}, "EntityDto.s3ImageUrl": {"type": "string"}, "Logo": {"type": "string", "format": "binary"}, "Image": {"type": "string", "format": "binary"}}}, "encoding": {"Entity": {"style": "form"}, "EntityDto.company_id": {"style": "form"}, "EntityDto.logo": {"style": "form"}, "EntityDto.image": {"style": "form"}, "EntityDto.description": {"style": "form"}, "EntityDto.name": {"style": "form"}, "EntityDto.category": {"style": "form"}, "EntityDto.required_individual_tier": {"style": "form"}, "EntityDto.required_corporate_tier": {"style": "form"}, "EntityDto.webhook_target_url": {"style": "form"}, "EntityDto.s3LogoUrl": {"style": "form"}, "EntityDto.s3ImageUrl": {"style": "form"}, "Logo": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}}}}}}, "/api/v1/Company/{id}": {"get": {"tags": ["Company"], "summary": "Retrieves a Company with the specified Id.", "parameters": [{"name": "id", "in": "path", "description": "The Id of the Company to retrieve.", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}}}}}, "put": {"tags": ["Company"], "summary": "Updates the Company with the specified id.", "parameters": [{"name": "id", "in": "path", "description": "The id of the Company to update.", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Entity", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.company_id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "EntityDto.logo", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.image", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.description", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.name", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.category", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.required_individual_tier", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "EntityDto.required_corporate_tier", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "EntityDto.webhook_target_url", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.s3LogoUrl", "in": "query", "schema": {"type": "string"}}, {"name": "EntityDto.s3ImageUrl", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Logo": {"type": "string", "format": "binary"}, "Image": {"type": "string", "format": "binary"}}}, "encoding": {"Logo": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyDtoApiResponse"}}}}}}, "delete": {"tags": ["Company"], "summary": "Deletes a Company.", "parameters": [{"name": "id", "in": "path", "description": "The Company id.", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/v1/Company/get-consent-users-for-companyid/{companyid}": {"get": {"tags": ["Company"], "summary": "Retrieves a list of consent users associated with a specific company.", "description": "This method fetches the consent users for a given company by its identifier.", "parameters": [{"name": "companyid", "in": "path", "description": "The unique identifier of the company.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentDtoListApiResponse"}}}}}}}, "/api/v1/Company/get-consent-history-users-for-companyid/{companyid}": {"get": {"tags": ["Company"], "summary": "Retrieves the consent history users associated with a specific company.", "description": "This method fetches the historical consent data for users linked to the specified company.", "parameters": [{"name": "companyid", "in": "path", "description": "The unique identifier of the company for which to retrieve consent history users.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}}}}}}, "/api/v1/Company/get-approval-history-users-for-companyid/{companyid}": {"get": {"tags": ["Company"], "parameters": [{"name": "companyid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}}}}}}, "/api/v1/Company/get-all-approval-history-users": {"get": {"tags": ["Company"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDtoListApiResponse"}}}}}}}, "/api/v1/Company/get-consent-global-history": {"get": {"tags": ["Company"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDtoListApiResponse"}}}}}}}, "/api/v1/Company/get-all-documents-for-consent-clients-by-companyid/{companyid}": {"get": {"tags": ["Company"], "parameters": [{"name": "companyid", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/FileSumSubDataDtoListApiResponse"}}}}}}}, "/api/v1/Notifications/user/{userId}": {"get": {"tags": ["Notifications"], "summary": "Retrieves a list of notifications for a specific user.", "description": "The method validates the format of the provided userId and returns an error response\r\nif the format is invalid. Otherwise, it fetches the notifications associated with the user.", "parameters": [{"name": "userId", "in": "path", "description": "The unique identifier of the user for whom the notifications are to be retrieved.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/NotificationDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoListApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/NotificationDtoListApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/NotificationDtoListApiResponse"}}}}}}}, "/api/v1/Notifications/send": {"post": {"tags": ["Notifications"], "summary": "Sends a notification to a specific user.", "description": "This method validates the format of the user ID provided in the request. If the format is invalid,\r\nan error response is returned. Otherwise, the notification is sent to the specified user.", "requestBody": {"description": "An instance of Core.EjKyc.Service.Accounts.Query.SendNotificationRequest containing the details of the notification to be sent.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/v1/SumSub": {"post": {"tags": ["SumSub"], "summary": "Handles incoming webhook requests from SumSub.", "description": "This method processes the webhook payload by invoking the M:Core.EjKyc.Service.Interface.ISumSubService.CreateSumSub(Core.EjKyc.Service.SumSub.Command.CreateSumSubCommand) \r\nservice method, maps the result to a Core.EjKyc.Service.SumSub.Query.SumSubResponse, and returns it as a response.", "requestBody": {"description": "The payload containing applicant details and associated data required to process the webhook.", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateSumSubCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateSumSubCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateSumSubCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateSumSubCommand"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CreateSumSubCommand"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CreateSumSubCommand"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/CreateSumSubCommand"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SumSubResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SumSubResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SumSubResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/SumSubResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/SumSubResponseApiResponse"}}}}}}}, "/api/v1/SumSub/individual-applicant-data/{applicantId}": {"get": {"tags": ["SumSub"], "summary": "Retrieves the data of an individual applicant based on the provided applicant ID.", "description": "This method interacts with the Core.EjKyc.Service.Interface.IAccountsService to fetch the account details \r\nand the Core.EjKyc.Service.Interface.ISumSubService to retrieve the applicant's data.", "parameters": [{"name": "applicantId", "in": "path", "description": "The unique identifier of the applicant whose data is to be retrieved.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}}}}}}, "/api/v1/SumSub/initiate-interview/{applicantId}/{companyId}": {"get": {"tags": ["SumSub"], "parameters": [{"name": "applicantId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/IndividualApplicantApiResponse"}}}}}}}, "/api/v1/SumSub/company-applicant-data/{applicantId}": {"get": {"tags": ["SumSub"], "summary": "Retrieves the data of a company applicant based on the provided applicant ID.", "parameters": [{"name": "applicantId", "in": "path", "description": "The unique identifier of the applicant whose company data is to be retrieved.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CompanyApplicantApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CompanyApplicantApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CompanyApplicantApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/CompanyApplicantApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/CompanyApplicantApiResponse"}}}}}}}, "/api/v1/SumSub/get-tier-sdk-userid-level-token": {"get": {"tags": ["SumSub"], "parameters": [{"name": "userid", "in": "query", "schema": {"type": "string"}}, {"name": "levelName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/v1/SumSub/{applicantId}/companies": {"get": {"tags": ["SumSub"], "parameters": [{"name": "applicantId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/StringApiResponse"}}}}}}}, "/api/v1/Token": {"post": {"tags": ["Token"], "summary": "Generates a JWT token for the account associated with the provided email and password.", "description": "This method retrieves the account details from the database based on the provided email and password.\r\nIf the account is found, a JWT token is generated using the account information.", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+xml": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}}}}}}, "/api/v1/Token/refresh-token": {"post": {"tags": ["Token"], "summary": "Refreshes the API token for a user based on their user ID.", "description": "This method validates the provided userId and generates a new JWT token\r\nfor the associated user account. If the user ID is invalid or the account does not exist,\r\nan error response is returned.", "requestBody": {"description": "The unique identifier of the user whose API token needs to be refreshed.", "content": {"application/json-patch+json": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}, "application/xml": {"schema": {"type": "string"}}, "text/xml": {"schema": {"type": "string"}}, "application/*+xml": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}, "text/xml": {"schema": {"$ref": "#/components/schemas/AccountsResponseApiResponse"}}}}}}}}, "components": {"schemas": {"Account": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "nullable": true}, "password_hash": {"type": "string", "nullable": true}, "signup_date": {"type": "string", "format": "date-time"}, "state": {"type": "string", "nullable": true}, "registrationtype": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "applicant_id": {"type": "string", "nullable": true}, "application": {"type": "string", "nullable": true}, "salt": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}, "nullable": true}, "companies": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}, "nullable": true}, "sumSubUserIndividual": {"$ref": "#/components/schemas/SumSubUserIndividual"}, "sumSubUserCompany": {"$ref": "#/components/schemas/SumSubUserCompany"}, "sumSubUserInfo": {"$ref": "#/components/schemas/SumSubUserInfo"}, "sumSubApplicationUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AccountWithApprovalStatus": {"type": "object", "properties": {"account": {"$ref": "#/components/schemas/Account"}, "approvalStatus": {"$ref": "#/components/schemas/ApprovalStatus"}, "company": {"$ref": "#/components/schemas/Company"}, "accountSumSubInfo": {"$ref": "#/components/schemas/SumSubUserInfo"}, "approvalCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AccountWithApprovalStatusApiSearchResponse": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/AccountWithApprovalStatus"}, "nullable": true}}, "additionalProperties": false}, "AccountWithApprovalStatusApiSearchResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/AccountWithApprovalStatusApiSearchResponse"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "AccountWithApprovalStatusDto": {"type": "object", "properties": {"account": {"$ref": "#/components/schemas/AccountsResponse"}, "approvalStatus": {"$ref": "#/components/schemas/ApprovalStatus"}, "company": {"$ref": "#/components/schemas/CompanyDto"}, "accountSumSubInfo": {"$ref": "#/components/schemas/SumSubUserInfoResponse"}, "approvalCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "AccountWithApprovalStatusDtoApiSearchResponse": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/AccountWithApprovalStatusDto"}, "nullable": true}}, "additionalProperties": false}, "AccountWithApprovalStatusDtoApiSearchResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/AccountWithApprovalStatusDtoApiSearchResponse"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "AccountsResponse": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "nullable": true}, "signup_date": {"type": "string", "format": "date-time"}, "state": {"type": "string", "nullable": true}, "registrationtype": {"type": "string", "nullable": true}, "applicant_id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}, "nullable": true}, "companies": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDto"}, "nullable": true}, "token": {"type": "string", "nullable": true}, "sumSubUserIndividual": {"$ref": "#/components/schemas/SumSubUserIndividualResponse"}, "sumSubUserCompany": {"$ref": "#/components/schemas/SumSubUserCompanyResponse"}, "sumSubUserInfo": {"$ref": "#/components/schemas/SumSubUserInfoResponse"}, "sumSubApplicationUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AccountsResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/AccountsResponse"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "AccountsResponseApiSearchResponse": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/AccountsResponse"}, "nullable": true}}, "additionalProperties": false}, "AccountsResponseApiSearchResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/AccountsResponseApiSearchResponse"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "Address": {"type": "object", "properties": {"formattedAddress": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AddressIndividual": {"type": "object", "properties": {"street": {"type": "string", "nullable": true}, "streetEn": {"type": "string", "nullable": true}, "buildingNumber": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "formattedAddress": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Agreement": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "acceptedAt": {"type": "string", "format": "date-time"}, "source": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AgreementIndividual": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "source": {"type": "string", "nullable": true}, "targets": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ApiError": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "severity": {"$ref": "#/components/schemas/Severity"}, "customMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApprovalStatus": {"type": "object", "properties": {"status_id": {"type": "integer", "format": "int32"}, "status_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApprovalStatusApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/ApprovalStatus"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "BOAccountDto": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "nullable": true}, "signup_date": {"type": "string", "format": "date-time"}, "approval_date": {"type": "string", "format": "date-time", "nullable": true}, "state": {"type": "string", "nullable": true}, "registrationtype": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "applicant_id": {"type": "string", "nullable": true}, "application": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}, "nullable": true}, "companies": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}, "nullable": true}, "sumSubUserIndividual": {"$ref": "#/components/schemas/SumSubUserIndividual"}, "sumSubUserCompany": {"$ref": "#/components/schemas/SumSubUserCompany"}, "sumSubUserInfo": {"$ref": "#/components/schemas/SumSubUserInfo"}}, "additionalProperties": false}, "BOAccountDtoApiSearchResponse": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/BOAccountDto"}, "nullable": true}}, "additionalProperties": false}, "BOAccountDtoApiSearchResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/BOAccountDtoApiSearchResponse"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "Beneficiary": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "applicantId": {"type": "string", "nullable": true}, "shareSize": {"type": "number", "format": "double"}, "types": {"type": "array", "items": {"type": "string"}, "nullable": true}, "beneficiaryInfo": {"$ref": "#/components/schemas/BeneficiaryInfo"}}, "additionalProperties": false}, "BeneficiaryDal": {"required": ["applicantId", "companyApplicantId", "companyUserId", "submitted", "types", "userId"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "applicantId": {"maxLength": 255, "minLength": 1, "type": "string"}, "userId": {"type": "string", "format": "uuid"}, "companyUserId": {"type": "string", "format": "uuid"}, "companyApplicantId": {"maxLength": 255, "minLength": 1, "type": "string"}, "types": {"minLength": 1, "type": "string"}, "submitted": {"type": "boolean"}, "email": {"maxLength": 255, "type": "string", "nullable": true}, "phone": {"maxLength": 50, "type": "string", "nullable": true}, "firstName": {"maxLength": 100, "type": "string", "nullable": true}, "lastName": {"maxLength": 100, "type": "string", "nullable": true}, "middleName": {"maxLength": 100, "type": "string", "nullable": true}, "dob": {"type": "string", "format": "date-time", "nullable": true}, "user": {"$ref": "#/components/schemas/Account"}}, "additionalProperties": false}, "BeneficiaryInfo": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "taxResidenceCountry": {"type": "string", "nullable": true}, "shareSize": {"type": "number", "format": "double"}, "phone": {"type": "string", "nullable": true}, "middleName": {"type": "string", "nullable": true}, "dob": {"type": "string", "format": "date-time", "nullable": true}, "submitted": {"type": "boolean"}}, "additionalProperties": false}, "BeneficiaryResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "applicantId": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "companyUserId": {"type": "string", "format": "uuid"}, "companyApplicantId": {"type": "string", "nullable": true}, "types": {"type": "string", "nullable": true}, "submitted": {"type": "boolean"}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "middleName": {"type": "string", "nullable": true}, "dob": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BooleanApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "boolean"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "Company": {"type": "object", "properties": {"company_id": {"type": "integer", "format": "int32", "nullable": true}, "logo": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "required_individual_tier": {"type": "integer", "format": "int32"}, "required_corporate_tier": {"type": "integer", "format": "int32"}, "webhook_target_url": {"type": "string", "nullable": true}, "s3LogoUrl": {"type": "string", "nullable": true}, "s3ImageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanyApplicant": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "clientId": {"type": "string", "nullable": true}, "inspectionId": {"type": "string", "nullable": true}, "externalUserId": {"type": "string", "nullable": true}, "info": {"$ref": "#/components/schemas/Info"}, "fixedInfo": {"$ref": "#/components/schemas/FixedInfo"}, "applicantPlatform": {"type": "string", "nullable": true}, "ipCountry": {"type": "string", "nullable": true}, "authCode": {"type": "string", "nullable": true}, "agreement": {"$ref": "#/components/schemas/Agreement"}, "requiredIdDocs": {"$ref": "#/components/schemas/RequiredIdDocs"}, "review": {"$ref": "#/components/schemas/Review"}, "lang": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "riskLabels": {"$ref": "#/components/schemas/RiskLabels"}}, "additionalProperties": false}, "CompanyApplicantApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/CompanyApplicant"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "CompanyBeneficiaryDefinition": {"type": "object", "properties": {"category": {"type": "string", "nullable": true}, "canSkip": {"type": "boolean"}, "individual": {"$ref": "#/components/schemas/Individual"}, "company": {"$ref": "#/components/schemas/CompanySumSUb"}}, "additionalProperties": false}, "CompanyDocsGroupDefinition": {"type": "object", "properties": {"label": {"type": "string", "nullable": true}, "subTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "required": {"type": "boolean"}}, "additionalProperties": false}, "CompanyDto": {"type": "object", "properties": {"company_id": {"type": "integer", "format": "int32"}, "logo": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "required_individual_tier": {"type": "integer", "format": "int32"}, "required_corporate_tier": {"type": "integer", "format": "int32"}, "webhook_target_url": {"type": "string", "nullable": true}, "s3LogoUrl": {"type": "string", "nullable": true}, "s3ImageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanyDtoApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/CompanyDto"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "CompanyDtoApiSearchResponse": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "totalCount": {"type": "integer", "format": "int32"}, "result": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDto"}, "nullable": true}}, "additionalProperties": false}, "CompanyDtoApiSearchResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/CompanyDtoApiSearchResponse"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "CompanyDtoListApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "CompanyInfo": {"type": "object", "properties": {"companyName": {"type": "string", "nullable": true}, "registrationNumber": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "legalAddress": {"type": "string", "nullable": true}, "incorporatedOn": {"type": "string", "format": "date-time"}, "applicantPosition": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "taxId": {"type": "string", "nullable": true}, "registrationLocation": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "postalAddress": {"type": "string", "nullable": true}, "noShareholders": {"type": "boolean"}, "beneficiaries": {"type": "array", "items": {"$ref": "#/components/schemas/Beneficiary"}, "nullable": true}}, "additionalProperties": false}, "CompanySumSUb": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "levelName": {"type": "string", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}, "nullable": true}, "customFields": {"nullable": true}}, "additionalProperties": false}, "CompanyUserApprovalHistoryDto": {"type": "object", "properties": {"approval_history_id": {"type": "integer", "format": "int32"}, "company_id": {"type": "integer", "format": "int32"}, "user_id": {"type": "string", "format": "uuid"}, "applicant_id": {"type": "string", "nullable": true}, "approval_date": {"type": "string", "format": "date-time"}, "status_id": {"type": "integer", "format": "int32"}, "approvalStatus": {"$ref": "#/components/schemas/ApprovalStatus"}, "company": {"$ref": "#/components/schemas/CompanyDto"}, "user": {"$ref": "#/components/schemas/AccountsResponse"}, "approvalCount": {"type": "integer", "format": "int32"}, "sumSubApplicationUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompanyUserApprovalHistoryDtoListApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyUserApprovalHistoryDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "CreateConsentAccountCompanyCommand": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "company_id": {"type": "integer", "format": "int32"}, "applicant_id": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateSumSubCommand": {"type": "object", "properties": {"applicantId": {"type": "string", "nullable": true}, "inspectionId": {"type": "string", "nullable": true}, "applicantType": {"type": "string", "nullable": true}, "correlationId": {"type": "string", "nullable": true}, "levelName": {"type": "string", "nullable": true}, "sandboxMode": {"type": "boolean"}, "externalUserId": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "reviewStatus": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdAtMs": {"type": "string", "format": "date-time"}, "clientId": {"type": "string", "nullable": true}, "sumsub_id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DocSet": {"type": "object", "properties": {"idDocSetType": {"type": "string", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}, "nullable": true}, "companyBeneficiaryDefinitions": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyBeneficiaryDefinition"}, "nullable": true}, "companyDocsGroupDefinitions": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyDocsGroupDefinition"}, "nullable": true}}, "additionalProperties": false}, "DocSetIndividual": {"type": "object", "properties": {"idDocSetType": {"type": "string", "nullable": true}, "types": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Field": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "required": {"type": "boolean"}}, "additionalProperties": false}, "FileSumSubData": {"type": "object", "properties": {"fileS3Path": {"type": "string", "nullable": true}, "fileS3PathThumbnail": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "idDocDefCountry": {"type": "string", "nullable": true}, "idDocDefIdDocType": {"type": "string", "nullable": true}, "idDocDefIdDocSubType": {"type": "string", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "modificationDate": {"type": "string", "format": "date-time", "nullable": true}, "documentId": {"type": "string", "nullable": true}, "documentPreviewId": {"type": "string", "nullable": true}, "fileType": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileSumSubDataDto": {"type": "object", "properties": {"fileS3Path": {"type": "string", "nullable": true}, "fileS3PathThumbnail": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "idDocDefCountry": {"type": "string", "nullable": true}, "idDocDefIdDocType": {"type": "string", "nullable": true}, "idDocDefIdDocSubType": {"type": "string", "nullable": true}, "creationDate": {"type": "string", "format": "date-time", "nullable": true}, "modificationDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "FileSumSubDataDtoListApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/FileSumSubDataDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "FixedInfo": {"type": "object", "properties": {"companyInfo": {"$ref": "#/components/schemas/CompanyInfo"}}, "additionalProperties": false}, "FixedInfoIndividual": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "tin": {"type": "string", "nullable": true}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/AddressIndividual"}, "nullable": true}, "taxResidenceCountry": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IdDoc": {"type": "object", "properties": {"idDocType": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IdDocIndividual": {"type": "object", "properties": {"idDocType": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "firstNameEn": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "lastNameEn": {"type": "string", "nullable": true}, "validUntil": {"type": "string", "format": "date-time"}, "number": {"type": "string", "nullable": true}, "dob": {"type": "string", "format": "date-time"}, "mrzLine1": {"type": "string", "nullable": true}, "mrzLine2": {"type": "string", "nullable": true}, "mrzLine3": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Individual": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "levelName": {"type": "string", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}, "nullable": true}, "customFields": {"nullable": true}}, "additionalProperties": false}, "IndividualApplicant": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "clientId": {"type": "string", "nullable": true}, "inspectionId": {"type": "string", "nullable": true}, "externalUserId": {"type": "string", "nullable": true}, "fixedInfo": {"$ref": "#/components/schemas/FixedInfoIndividual"}, "info": {"$ref": "#/components/schemas/InfoIndividual"}, "agreement": {"$ref": "#/components/schemas/AgreementIndividual"}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "applicantPlatform": {"type": "string", "nullable": true}, "requiredIdDocs": {"$ref": "#/components/schemas/RequiredIdDocsIndividual"}, "review": {"$ref": "#/components/schemas/ReviewIndividual"}, "lang": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IndividualApplicantApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/IndividualApplicant"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "Info": {"type": "object", "properties": {"idDocs": {"type": "array", "items": {"$ref": "#/components/schemas/IdDoc"}, "nullable": true}, "companyInfo": {"$ref": "#/components/schemas/CompanyInfo"}}, "additionalProperties": false}, "InfoIndividual": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "firstNameEn": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "lastNameEn": {"type": "string", "nullable": true}, "dob": {"type": "string", "format": "date-time"}, "country": {"type": "string", "nullable": true}, "taxResidenceCountry": {"type": "string", "nullable": true}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/Address"}, "nullable": true}, "idDocs": {"type": "array", "items": {"$ref": "#/components/schemas/IdDocIndividual"}, "nullable": true}}, "additionalProperties": false}, "KybSettings": {"type": "object", "properties": {"shareholderThreshold": {"type": "number", "format": "double"}, "uboThreshold": {"type": "number", "format": "double"}}, "additionalProperties": false}, "LoginRequest": {"required": ["email", "password"], "type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "twoFactorCode": {"type": "string", "nullable": true}, "twoFactorRecoveryCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "NotificationDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "applicantId": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "notificationType": {"$ref": "#/components/schemas/NotificationTypeDto"}}, "additionalProperties": false}, "NotificationDtoListApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "NotificationTypeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RequiredIdDocs": {"type": "object", "properties": {"docSets": {"type": "array", "items": {"$ref": "#/components/schemas/DocSet"}, "nullable": true}, "kybSettings": {"$ref": "#/components/schemas/KybSettings"}}, "additionalProperties": false}, "RequiredIdDocsIndividual": {"type": "object", "properties": {"docSets": {"type": "array", "items": {"$ref": "#/components/schemas/DocSetIndividual"}, "nullable": true}}, "additionalProperties": false}, "Review": {"type": "object", "properties": {"reviewId": {"type": "string", "nullable": true}, "attemptId": {"type": "string", "nullable": true}, "attemptCnt": {"type": "integer", "format": "int32"}, "elapsedSincePendingMs": {"type": "integer", "format": "int32"}, "levelName": {"type": "string", "nullable": true}, "createDate": {"type": "string", "format": "date-time"}, "reviewDate": {"type": "string", "format": "date-time"}, "reviewResult": {"$ref": "#/components/schemas/ReviewResultCompany"}, "reviewStatus": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReviewIndividual": {"type": "object", "properties": {"elapsedSincePendingMs": {"type": "integer", "format": "int64"}, "elapsedSinceQueuedMs": {"type": "integer", "format": "int64"}, "reprocessing": {"type": "boolean"}, "levelName": {"type": "string", "nullable": true}, "createDate": {"type": "string", "format": "date-time"}, "reviewDate": {"type": "string", "format": "date-time"}, "reviewResult": {"$ref": "#/components/schemas/ReviewResultIndividual"}, "reviewStatus": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReviewResultCompany": {"type": "object", "properties": {"reviewAnswer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReviewResultIndividual": {"type": "object", "properties": {"reviewAnswer": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RiskLabels": {"type": "object", "properties": {"attemptId": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "device": {"type": "array", "items": {"type": "string"}, "nullable": true}, "crossCheck": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Role": {"type": "object", "properties": {"role_id": {"type": "integer", "format": "int32", "nullable": true}, "role_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RoleDto": {"type": "object", "properties": {"role_id": {"type": "integer", "format": "int32"}, "role_name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendNotificationRequest": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Severity": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "StringApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "SumSubResponse": {"type": "object", "properties": {"applicant_id": {"type": "string", "nullable": true}, "inspection_id": {"type": "string", "nullable": true}, "applicant_type": {"type": "string", "nullable": true}, "correlation_id": {"type": "string", "nullable": true}, "level_name": {"type": "string", "nullable": true}, "sandbox_mode": {"type": "boolean"}, "external_user_id": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "review_status": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "created_at_ms": {"type": "string", "format": "date-time"}, "client_id": {"type": "string", "nullable": true}, "sumsub_id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SumSubResponseApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/SumSubResponse"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "SumSubUserCompany": {"required": ["applicantId", "userId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "applicantId": {"minLength": 1, "type": "string"}, "userId": {"type": "string", "format": "uuid"}, "email": {"maxLength": 255, "type": "string", "nullable": true}, "phoneNumber": {"maxLength": 50, "type": "string", "nullable": true}, "companyName": {"maxLength": 255, "type": "string", "nullable": true}, "registrationNumber": {"maxLength": 100, "type": "string", "nullable": true}, "country": {"maxLength": 100, "type": "string", "nullable": true}, "legalAddress": {"type": "string", "nullable": true}, "incorporationDate": {"type": "string", "format": "date-time", "nullable": true}, "taxId": {"maxLength": 100, "type": "string", "nullable": true}, "registrationLocation": {"maxLength": 255, "type": "string", "nullable": true}, "website": {"maxLength": 255, "type": "string", "nullable": true}, "postalAddress": {"type": "string", "nullable": true}, "beneficiaries": {"type": "array", "items": {"$ref": "#/components/schemas/BeneficiaryDal"}, "nullable": true}, "beneficiaryApplicantId": {"type": "string", "nullable": true}, "beneficiaryUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SumSubUserCompanyResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "applicantId": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "registrationNumber": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "legalAddress": {"type": "string", "nullable": true}, "incorporationDate": {"type": "string", "format": "date-time", "nullable": true}, "taxId": {"type": "string", "nullable": true}, "registrationLocation": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "postalAddress": {"type": "string", "nullable": true}, "beneficiaries": {"type": "array", "items": {"$ref": "#/components/schemas/BeneficiaryResponse"}, "nullable": true}, "beneficiaryApplicantId": {"type": "string", "nullable": true}, "beneficiaryUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SumSubUserIndividual": {"required": ["applicantId", "userId"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "applicantId": {"maxLength": 255, "minLength": 1, "type": "string"}, "userId": {"type": "string", "format": "uuid"}, "email": {"maxLength": 255, "type": "string", "nullable": true}, "phoneNumber": {"maxLength": 50, "type": "string", "nullable": true}, "firstName": {"maxLength": 255, "type": "string", "nullable": true}, "lastName": {"maxLength": 255, "type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date-time", "nullable": true}, "country": {"maxLength": 100, "type": "string", "nullable": true}, "countryOfResidence": {"maxLength": 100, "type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "tin": {"type": "string", "nullable": true}, "taxResidenceCountry": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SumSubUserIndividualResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "applicantId": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date-time", "nullable": true}, "country": {"type": "string", "nullable": true}, "countryOfResidence": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "tin": {"type": "string", "nullable": true}, "taxResidenceCountry": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SumSubUserInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "state": {"type": "string", "nullable": true}, "registrationType": {"type": "string", "nullable": true}, "applicantId": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "sumSubApplicationUrl": {"type": "string", "nullable": true}, "interviewCompletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SumSubUserInfoResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "state": {"type": "string", "nullable": true}, "registrationType": {"type": "string", "nullable": true}, "applicantId": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "sumSubApplicationUrl": {"type": "string", "nullable": true}, "interviewCompletedDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TierSubSubData": {"type": "object", "properties": {"state": {"type": "string", "nullable": true}, "fileSumSubDataList": {"type": "array", "items": {"$ref": "#/components/schemas/FileSumSubData"}, "nullable": true}}, "additionalProperties": false}, "TierSubSubDataWithApplicantId": {"type": "object", "properties": {"applicantId": {"type": "string", "nullable": true}, "tierSubSubDataList": {"type": "array", "items": {"$ref": "#/components/schemas/TierSubSubData"}, "nullable": true}}, "additionalProperties": false}, "TierSubSubDataWithApplicantIdApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/TierSubSubDataWithApplicantId"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "UpdatePasswordCommand": {"type": "object", "properties": {"password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "user_id": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "UpsertAccountsCommand": {"type": "object", "properties": {"registrationType": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "user_id": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "UpsertApprovalClientCompanyCommand": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "company_id": {"type": "integer", "format": "int32"}, "applicant_id": {"type": "string", "nullable": true}, "status_id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpsertApprovalClientCompanyDto": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "company_id": {"type": "integer", "format": "int32"}, "applicant_id": {"type": "string", "nullable": true}, "status_id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpsertApprovalClientCompanyDtoApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/UpsertApprovalClientCompanyDto"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "UserCompanyConsentDto": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "company_id": {"type": "integer", "format": "int32"}, "applicant_id": {"type": "string", "nullable": true}, "action_date": {"type": "string", "format": "date-time"}, "state": {"type": "string", "nullable": true}, "registrationtype": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "signup_date": {"type": "string", "format": "date-time"}, "sumSubUserInfo": {"$ref": "#/components/schemas/SumSubUserInfoResponse"}}, "additionalProperties": false}, "UserCompanyConsentDtoApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"$ref": "#/components/schemas/UserCompanyConsentDto"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "UserCompanyConsentDtoListApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserCompanyConsentDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}, "UserCompanyConsentHistoryDto": {"type": "object", "properties": {"user_id": {"type": "string", "format": "uuid"}, "company_id": {"type": "integer", "format": "int32"}, "user_company_consent_id": {"type": "integer", "format": "int32"}, "applicant_id": {"type": "string", "nullable": true}, "consent_id": {"type": "integer", "format": "int32"}, "action_date": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/components/schemas/Account"}, "company": {"$ref": "#/components/schemas/Company"}, "registrationtype": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserCompanyConsentHistoryDtoListApiResponse": {"type": "object", "properties": {"isSuccess": {"type": "boolean", "readOnly": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserCompanyConsentHistoryDto"}, "nullable": true}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ApiError"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. \\r\\n\\r\\n \n                      Enter 'Bearer' [space] and then your token in the text input below.\n                      \\r\\n\\r\\nExample: 'Bearer 12345abcdef'", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}