"use client";

import React from 'react';
import NotificationsDisplay from '../components/NotificationsDisplay';
import { useUser } from '../hooks/useUser';

export default function NotificationsProvider() {
  const { user, isAuthenticated } = useUser();
  
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:5001';

  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <NotificationsDisplay 
      userId={user.user_id} 
      apiBaseUrl={apiBaseUrl} 
    />
  );
}
