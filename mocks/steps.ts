export interface StepName {
  name: string;
  finished: boolean;
}

export interface Step {
  id: number;
  title: string;
  fields: Field[];
}

export interface Field {
  id: number;
  title: string;
  type: string;
  required: boolean;
}

export const STEPS_MOCK_INDIVIDUAL: {
  stepNames: StepName[];
  steps: Step[];
} = {
  stepNames: [
    {
      name: "Step 1",
      finished: false,
    },
    {
      name: "Step 2",
      finished: false,
    },
  ],
  steps: [
    {
      id: 1,
      title: "Step 1",
      fields: [
        {
          id: 1,
          title: "Field 1",
          type: "text",
          required: true,
        },
        {
          id: 2,
          title: "Field 2",
          type: "text",
          required: true,
        },
      ],
    },
    {
      id: 2,
      title: "Step 2",
      fields: [
        {
          id: 3,
          title: "Field 3",
          type: "text",
          required: true,
        },
        {
          id: 4,
          title: "Field 4",
          type: "text",
          required: true,
        },
      ],
    },
  ],
};

export const STEPS_MOCK_COMPANY = {
  stepNames: [
    {
      name: "Step 1",
      finished: false,
    },
    {
      name: "Step 2",
      finished: false,
    },
  ],
  steps: [
    {
      id: 1,
      title: "Step 1",
      fields: [
        {
          id: 1,
          title: "Field 1",
          type: "text",
          required: true,
        },
        {
          id: 2,
          title: "Field 2",
          type: "text",
          required: true,
        },
      ],
    },
  ],
};
