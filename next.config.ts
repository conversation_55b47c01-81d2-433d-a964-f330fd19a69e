import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "edujourney-proto-1.s3.eu-central-1.amazonaws.com",
        pathname: "/**", // Allows all paths in the bucket
      },
    ],
  },
  webpack(config, { isServer }) {
    if (isServer) {
      config.devtool = "source-map";
    }
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    return config;
  },
};

export default nextConfig;
