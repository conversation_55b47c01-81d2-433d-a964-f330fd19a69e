import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import React, { useState } from "react";
import { StyleSheet, View, ScrollView, Alert } from "react-native";
import { AppButton } from "../ui/app-button";
import { AppText } from "../ui/app-text";
import Modal from "react-native-modal";
import { FormPassword } from "../form/form-password";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  changePasswordSchema,
  ChangePasswordFormValues,
} from "@/lib/validation/change-password-schema";
import { useChangePassword } from "@/services/auth/auth-hooks";
import { Ionicons } from "@expo/vector-icons";

interface ChangePasswordModalProps {
  isVisible: boolean;
  onClose: () => void;
}

export const ChangePasswordModal = ({
  isVisible,
  onClose,
}: ChangePasswordModalProps) => {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setError,
    clearErrors,
  } = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      password_current: "",
      password_new: "",
      password_confirm: "",
    },
  });

  const { mutate: changePassword, isPending } = useChangePassword({
    mutationOptions: {
      onSuccess: (data) => {
        console.log("✅ Password changed successfully:", data);
        setIsSubmitted(true);
        // Auto close after 2 seconds
        setTimeout(() => {
          handleClose();
        }, 2000);
      },
      onError: (error: any) => {
        console.error("❌ Change password error:", error);
        setError("root", {
          type: "manual",
          message:
            error?.message || "An error occurred while changing password.",
        });
      },
    },
  });

  const onSubmit = (data: ChangePasswordFormValues) => {
    clearErrors();
    console.log("🔐 Submitting password change");
    changePassword(data);
  };

  const handleClose = () => {
    if (!isPending) {
      onClose();
      setIsSubmitted(false);
      reset();
      clearErrors();
    }
  };

  // Success state - show confirmation message
  if (isSubmitted) {
    return (
      <Modal
        isVisible={isVisible}
        onBackdropPress={handleClose}
        onSwipeComplete={handleClose}
        swipeDirection="down"
        onBackButtonPress={handleClose}
        style={styles.modal}
      >
        <View style={styles.modalContent}>
          <View style={styles.successContainer}>
            {/* Success Icon */}
            <View style={styles.successIconContainer}>
              <Ionicons
                name="checkmark-circle"
                size={scale(48)}
                color={COLORS.success}
              />
            </View>

            {/* Success Message */}
            <AppText size="xl" weight="bold" style={styles.successTitle}>
              Password Changed Successfully
            </AppText>

            <AppText style={styles.successDescription}>
              Your password has been updated. You can now use your new password
              to sign in.
            </AppText>

            {/* Back Button */}
            <AppButton
              text="DONE"
              size="lg"
              onPress={handleClose}
              style={styles.successButton}
            />
          </View>
        </View>
      </Modal>
    );
  }

  // Form state - show the change password form
  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={handleClose}
      onSwipeComplete={handleClose}
      swipeDirection="down"
      onBackButtonPress={handleClose}
      style={styles.modal}
    >
      <View style={styles.modalContent}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <AppText size="xl" weight="bold" style={styles.title}>
            Change Password
          </AppText>
        </View>

        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {/* Error Message */}
          {errors.root && (
            <View style={styles.errorContainer}>
              <Ionicons
                name="alert-circle"
                size={scale(20)}
                color={COLORS.error}
                style={styles.errorIcon}
              />
              <AppText variant="destructive" size="sm" style={styles.errorText}>
                {errors.root.message}
              </AppText>
            </View>
          )}

          {/* Form Fields */}
          <View style={styles.formContainer}>
            <Controller
              control={control}
              name="password_current"
              render={({ field: { onChange, onBlur, value } }) => (
                <FormPassword
                  label="Current Password"
                  placeholder="Enter your current password"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.password_current?.message}
                  name="password_current"
                  returnKeyType="next"
                />
              )}
            />

            <Controller
              control={control}
              name="password_new"
              render={({ field: { onChange, onBlur, value } }) => (
                <FormPassword
                  label="New Password"
                  placeholder="Enter your new password"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.password_new?.message}
                  name="password_new"
                  returnKeyType="next"
                />
              )}
            />

            <Controller
              control={control}
              name="password_confirm"
              render={({ field: { onChange, onBlur, value } }) => (
                <FormPassword
                  label="Confirm New Password"
                  placeholder="Confirm your new password"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.password_confirm?.message}
                  name="password_confirm"
                  returnKeyType="done"
                />
              )}
            />
          </View>
        </ScrollView>

        {/* Fixed Bottom Buttons */}
        <View style={styles.buttonContainer}>
          <AppButton
            text="Cancel"
            variant="outline"
            onPress={handleClose}
            disabled={isPending}
            style={styles.cancelButton}
          />
          <AppButton
            text={isPending ? "CHANGING..." : "CHANGE PASSWORD"}
            onPress={handleSubmit(onSubmit)}
            disabled={isPending}
            style={styles.submitButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  modalContent: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: scale(24),
    borderTopRightRadius: scale(24),
    maxHeight: "90%",
    minHeight: "65%",
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: scale(20),
    paddingBottom: verticalScale(20),
  },
  headerContainer: {
    alignItems: "center",
    paddingHorizontal: scale(20),
    paddingTop: verticalScale(24),
    paddingBottom: verticalScale(16),
    borderBottomWidth: 1,
    borderBottomColor: COLORS.grayLight,
  },
  title: {
    marginBottom: verticalScale(8),
    textAlign: "center",
  },
  description: {
    textAlign: "center",
    color: COLORS.mediumGray,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FEF2F2",
    borderColor: COLORS.error,
    borderWidth: 1,
    borderRadius: scale(8),
    padding: scale(12),
    marginBottom: verticalScale(16),
  },
  errorIcon: {
    marginRight: scale(8),
  },
  errorText: {
    flex: 1,
  },
  formContainer: {
    gap: verticalScale(16),
    marginTop: verticalScale(16),
  },
  buttonContainer: {
    flexDirection: "row",
    paddingHorizontal: scale(20),
    paddingVertical: verticalScale(20),
    gap: scale(12),
    borderTopWidth: 1,
    borderTopColor: COLORS.grayLight,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 1,
  },
  // Success state styles
  successContainer: {
    alignItems: "center",
    paddingHorizontal: scale(20),
    paddingVertical: verticalScale(40),
  },
  successIconContainer: {
    marginBottom: verticalScale(24),
  },
  successTitle: {
    marginBottom: verticalScale(12),
    textAlign: "center",
  },
  successDescription: {
    textAlign: "center",
    color: COLORS.mediumGray,
    marginBottom: verticalScale(32),
  },
  successButton: {
    width: scale(200),
  },
});
