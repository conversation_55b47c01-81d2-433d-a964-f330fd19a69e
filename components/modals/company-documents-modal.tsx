import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { Document } from "@/types/document";
import { scale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import React from "react";
import {
  FlatList,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

interface CompanyDocumentsModalProps {
  isVisible: boolean;
  onRequestClose: () => void;
  companyName: string;
  companyTier: number;
  documents: Document[];
}

// Define which documents are shown for each tier
const getDocumentsForTier = (tier: number, allDocuments: Document[]) => {
  if (tier === 1) {
    // Basic tier: only ID and Selfie
    return allDocuments.filter(
      (doc) =>
        doc.idDocDefIdDocType === "ID_CARD" ||
        doc.idDocDefIdDocType === "SELFIE" ||
        doc.idDocDefIdDocType === "PASSPORT" ||
        doc.idDocDefIdDocType === "DRIVERS_LICENSE"
    );
  } else if (tier >= 2) {
    // Pro tier and above: all documents
    return allDocuments;
  }
  return [];
};

const getDocumentDisplayName = (document: Document) => {
  const typeMap: { [key: string]: string } = {
    ID_CARD: "ID Card",
    PASSPORT: "Passport",
    DRIVERS_LICENSE: "Driver's License",
    SELFIE: "Selfie",
    PROOF_OF_RESIDENCE: "Proof of Residence",
    BANK_STATEMENT: "Bank Statement",
    UTILITY_BILL: "Utility Bill",
    INCOME_STATEMENT: "Income Statement",
  };

  return typeMap[document.idDocDefIdDocType] || document.fileName || "Document";
};

const DocumentItem = ({ document }: { document: Document }) => (
  <View style={styles.documentItem}>
    <View style={styles.documentIcon}>
      {document.fileS3PathThumbnail ? (
        <Image
          source={{ uri: document.fileS3PathThumbnail }}
          style={styles.documentThumbnail}
          contentFit="cover"
        />
      ) : (
        <Ionicons name="document-outline" size={24} color={COLORS.primary} />
      )}
    </View>

    <View style={styles.documentInfo}>
      <AppText variant="primary" size="base" weight="medium" numberOfLines={1}>
        {getDocumentDisplayName(document)}
      </AppText>
      <AppText variant="secondary" size="sm" numberOfLines={1}>
        {document.idDocDefCountry || "Document"}
      </AppText>
    </View>

    <View style={styles.documentStatus}>
      <AppText variant="success" size="sm" weight="medium">
        VALID
      </AppText>
    </View>
  </View>
);

export const CompanyDocumentsModal = ({
  isVisible,
  onRequestClose,
  companyName,
  companyTier,
  documents,
}: CompanyDocumentsModalProps) => {
  const filteredDocuments = getDocumentsForTier(companyTier, documents);

  const getTierName = (tier: number) => {
    const tierNames = { 1: "Basic", 2: "Pro", 3: "Premium" };
    return tierNames[tier as keyof typeof tierNames] || `Tier ${tier}`;
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="slide"
      onRequestClose={onRequestClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <AppText
                variant="primary"
                size="lg"
                weight="semibold"
                numberOfLines={1}
                style={styles.title}
              >
                {companyName}
              </AppText>
              <AppText variant="secondary" size="sm" style={styles.subtitle}>
                Documents shared • {getTierName(companyTier)} tier
              </AppText>
            </View>

            <TouchableOpacity
              style={styles.closeButton}
              onPress={onRequestClose}
            >
              <Ionicons name="close" size={24} color={COLORS.gray} />
            </TouchableOpacity>
          </View>

          {/* Documents List */}
          <View style={styles.content}>
            {filteredDocuments.length > 0 ? (
              <FlatList
                data={filteredDocuments}
                keyExtractor={(item, index) => `${item.fileName}-${index}`}
                renderItem={({ item }) => <DocumentItem document={item} />}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.listContainer}
              />
            ) : (
              <View style={styles.emptyState}>
                <Ionicons
                  name="document-outline"
                  size={48}
                  color={COLORS.mediumGray}
                />
                <AppText
                  variant="secondary"
                  size="base"
                  style={styles.emptyText}
                >
                  No documents shared with this company
                </AppText>
              </View>
            )}
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <AppButton
              text="Close"
              variant="outline"
              size="sm"
              onPress={onRequestClose}
              buttonStyle={styles.closeButtonStyle}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  container: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: scale(20),
    borderTopRightRadius: scale(20),
    maxHeight: "80%",
    minHeight: "50%",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: scale(20),
    borderBottomWidth: 1,
    borderBottomColor: COLORS.grayLight,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    marginBottom: scale(4),
  },
  subtitle: {
    opacity: 0.7,
  },
  closeButton: {
    padding: scale(4),
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(20),
  },
  listContainer: {
    paddingVertical: scale(16),
  },
  documentItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(12),
    borderBottomWidth: 1,
    borderBottomColor: COLORS.grayLight,
  },
  documentIcon: {
    width: scale(40),
    height: scale(40),
    borderRadius: scale(8),
    backgroundColor: COLORS.grayLight,
    alignItems: "center",
    justifyContent: "center",
    marginRight: scale(12),
    overflow: "hidden",
  },
  documentThumbnail: {
    width: "100%",
    height: "100%",
  },
  documentInfo: {
    flex: 1,
  },
  documentStatus: {
    alignItems: "flex-end",
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: scale(40),
  },
  emptyText: {
    marginTop: scale(16),
    textAlign: "center",
  },
  footer: {
    padding: scale(20),
    borderTopWidth: 1,
    borderTopColor: COLORS.grayLight,
  },
  closeButtonStyle: {
    width: "100%",
  },
});
