import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import Modal from "react-native-modal";
import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { InterviewNotification } from "@/types/interview-notification";
import { SUMSUB_LEVEL } from "@/types/auth";
import { runSumsubProcess } from "@/services/sumsub/launchSNSMobileSDK";
import { useCurrentUser } from "@/services/user/user-hooks";

interface InterviewNotificationModalProps {
  isOpen: boolean;
  notification: InterviewNotification | null;
  onClose: () => void;
  onAccept: () => void;
  onReject: () => void;
}

export const InterviewNotificationModal = ({
  isOpen,
  notification,
  onClose,
  onAccept,
  onReject,
}: InterviewNotificationModalProps) => {
  const [isLaunchingSumsub, setIsLaunchingSumsub] = useState(false);
  const { data: user } = useCurrentUser();

  const handleAccept = async () => {
    if (!user?.email) {
      console.error("❌ User email not available for interview");
      onAccept();
      return;
    }

    try {
      setIsLaunchingSumsub(true);
      console.log("🎯 Starting interview verification process...");

      // Call the accept handler
      onAccept();

      // Then launch Sumsub SDK with interview level
      await runSumsubProcess({
        tier: SUMSUB_LEVEL.INTERVIEW,
        userEmail: user.email,
        changeHandler: (event) => {
          console.log("📋 Interview status change:", event);

          // Handle completion
          if (
            event.newStatus === "ActionCompleted" ||
            event.newStatus === "Approved"
          ) {
            console.log("✅ Interview completed successfully");
            setIsLaunchingSumsub(false);
            onClose();
          }

          // Handle failure
          if (
            event.newStatus === "Failed" ||
            event.newStatus === "FinallyRejected"
          ) {
            console.log("❌ Interview failed or rejected");
            setIsLaunchingSumsub(false);
            // Don't close modal on failure, let user try again or decline
          }
        },
      });
    } catch (error) {
      console.error("❌ Failed to launch interview verification:", error);
      setIsLaunchingSumsub(false);
      // Don't auto-close on error, let user decide what to do
    }
  };

  const handleReject = () => {
    console.log("❌ Interview request rejected");
    onReject();
  };

  const handleClose = () => {
    if (!isLaunchingSumsub) {
      onClose();
    }
  };

  if (!isOpen || !notification) return null;

  return (
    <Modal
      isVisible={isOpen}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      onBackdropPress={handleClose}
      onSwipeComplete={handleClose}
      swipeDirection="down"
      onBackButtonPress={handleClose}
      style={styles.modal}
    >
      <View style={styles.container}>
        <View style={styles.content}>
          <AppText
            variant="primary"
            size="xl"
            weight="bold"
            style={styles.title}
          >
            {notification.title}
          </AppText>

          <AppText variant="secondary" size="base" style={styles.message}>
            {notification.message}
          </AppText>

          <AppText variant="secondary" size="sm" style={styles.timestamp}>
            {notification.timestamp.toLocaleString()}
          </AppText>

          <View style={styles.actions}>
            <AppButton
              text="Decline"
              variant="ghost"
              onPress={handleReject}
              disabled={isLaunchingSumsub}
              style={styles.rejectButton}
            />
            <AppButton
              text="Accept Interview"
              variant="default"
              onPress={handleAccept}
              isLoading={isLaunchingSumsub}
              disabled={isLaunchingSumsub}
              style={styles.acceptButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  container: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: scale(24),
    borderTopRightRadius: scale(24),
    paddingHorizontal: scale(24),
    paddingTop: verticalScale(24),
    paddingBottom: verticalScale(40),
    minHeight: verticalScale(250),
  },
  content: {
    alignItems: "center",
  },
  title: {
    textAlign: "center",
    marginBottom: verticalScale(16),
  },
  message: {
    textAlign: "center",
    marginBottom: verticalScale(12),
    lineHeight: scale(20),
  },
  timestamp: {
    textAlign: "center",
    marginBottom: verticalScale(32),
    opacity: 0.7,
  },
  actions: {
    flexDirection: "row",
    gap: scale(12),
    width: "100%",
  },
  rejectButton: {
    flex: 1,
  },
  acceptButton: {
    flex: 1,
  },
});
