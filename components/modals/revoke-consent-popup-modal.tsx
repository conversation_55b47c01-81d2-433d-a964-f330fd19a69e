import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import React from "react";
import { StyleSheet, View } from "react-native";
import Modal from "react-native-modal";
import { useRevokeUserCompanyConsent } from "@/services/company/company-hooks";
import { useCurrentUser } from "@/services/user/user-hooks";

interface RevokeConsentPopupModalProps {
  isVisible: boolean;
  onRequestClose: () => void;
  companyName: string;
  companyId: number;
  refreshUserData: () => void;
}

export const RevokeConsentPopupModal = ({
  isVisible,
  onRequestClose,
  companyName,
  companyId,
  refreshUserData,
}: RevokeConsentPopupModalProps) => {
  const { data: user } = useCurrentUser();

  // Consent revocation mutation
  const { mutate: revokeUserCompanyConsent, isPending: isLoading } =
    useRevokeUserCompanyConsent({
      onSuccess: () => {
        console.log("✅ Consent revoked successfully");
        onRequestClose();
        refreshUserData();
      },
      onError: () => {
        console.error("❌ Failed to revoke consent");
        // Even if there's an error, the consent might have been revoked
        // Let's refresh the data to check the current state
        console.log("🔄 Refreshing data to check consent status...");
        refreshUserData();
        onRequestClose();
      },
    });

  const onConfirm = async () => {
    if (!user?.email || !user?.user_id || !user?.applicant_id) {
      console.error("❌ Missing user information for consent revocation");
      return;
    }

    try {
      console.log("🚀 Starting consent revocation for:", companyName);
      
      revokeUserCompanyConsent({
        userId: user.user_id,
        companyId: companyId,
        applicant_id: user.applicant_id,
      });
    } catch (error) {
      console.error("❌ Error during consent revocation:", error);
    }
  };

  return (
    <Modal
      isVisible={isVisible}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      onBackdropPress={onRequestClose}
      onSwipeComplete={onRequestClose}
      swipeDirection="down"
      style={styles.modal}
    >
      <View style={styles.container}>
        <View style={styles.content}>
          <AppText
            variant="primary"
            size="xl"
            weight="medium"
            style={styles.title}
          >
            Revoke Consent
          </AppText>

          <AppText variant="secondary" size="base" style={styles.message}>
            Are you sure you want to revoke consent for{" "}
            <AppText weight="semibold">{companyName}</AppText>? This will remove
            their access to your documents.
          </AppText>

          <View style={styles.buttonContainer}>
            <AppButton
              text="Cancel"
              variant="outline"
              size="lg"
              onPress={onRequestClose}
              style={styles.cancelButton}
              disabled={isLoading}
            />

            <AppButton
              text={isLoading ? "Revoking..." : "Revoke Consent"}
              variant="destructive"
              size="lg"
              onPress={onConfirm}
              style={styles.confirmButton}
              disabled={isLoading}
              isLoading={isLoading}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  container: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: scale(24),
    borderTopRightRadius: scale(24),
    paddingBottom: verticalScale(20),
    maxHeight: "80%",
  },
  content: {
    paddingHorizontal: scale(20),
    paddingTop: verticalScale(20),
    alignItems: "center",
  },
  title: {
    marginBottom: verticalScale(16),
    textAlign: "center",
  },
  message: {
    textAlign: "center",
    marginBottom: verticalScale(24),
    lineHeight: verticalScale(20),
  },
  buttonContainer: {
    flexDirection: "row",
    gap: scale(12),
    width: "100%",
  },
  cancelButton: {
    flex: 1,
  },
  confirmButton: {
    flex: 1,
  },
});
