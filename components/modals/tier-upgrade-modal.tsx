import { ICONS } from "@/constants/icons";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { getTierDisplayName } from "@/utils/tier-number";
import { Image } from "expo-image";
import React from "react";
import { Modal, StyleSheet, TouchableOpacity, View } from "react-native";
import { AppButton } from "../ui/app-button";
import { AppText } from "../ui/app-text";

interface TierUpgradeModalProps {
  isVisible: boolean;
  onClose: () => void;
  companyName: string;
  tierNumber: number;
  onUpgrade: () => void;
}

export const TierUpgradeModal = ({
  isVisible,
  onClose,
  companyName,
  tierNumber,
  onUpgrade,
}: TierUpgradeModalProps) => {
  const tierDisplayName = getTierDisplayName(tierNumber);

  const requiredDocuments = [
    "Proof of Income/Source of Funds",
    "Employment Contract; and/or",
    "Payslip; and/or",
    "Tax Return",
  ];

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContent}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <AppText size="lg">×</AppText>
          </TouchableOpacity>

          <AppText size="xl" weight="bold" style={styles.title}>
            {companyName}
          </AppText>

          <AppText size="lg" weight="bold" style={styles.subtitle}>
            {tierDisplayName} is locked
          </AppText>

          <View style={styles.iconContainer}>
            <Image
              source={ICONS.ARROW_LEFT_ICON}
              contentFit="contain"
              transition={200}
            />
          </View>

          <View style={styles.infoContainer}>
            <AppText style={styles.infoText}>
              In order to onboard with{" "}
              <AppText weight="bold">{companyName}</AppText>, the required level
              of verification is{" "}
              <AppText weight="bold">{tierDisplayName}</AppText>. To verify your
              account to <AppText weight="bold">{tierDisplayName}</AppText>, you
              will need to complete the following steps:
            </AppText>
          </View>

          <View style={styles.documentsList}>
            {requiredDocuments.map((doc, index) => (
              <View key={index} style={styles.documentItem}>
                <AppText>• {doc}</AppText>
              </View>
            ))}
          </View>

          <View style={styles.actions}>
            <AppButton
              text={`UPGRADE TO ${tierDisplayName.toUpperCase()}`}
              variant="default"
              size="lg"
              onPress={onUpgrade}
            />
            <AppButton
              text="BACK"
              variant="ghost"
              size="sm"
              onPress={onClose}
              style={styles.backButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: COLORS.background,
    borderRadius: scale(24),
    padding: scale(15),
    width: "90%",
    maxWidth: scale(400),
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    right: scale(16),
    top: scale(16),
    padding: scale(8),
  },
  title: {
    marginBottom: verticalScale(8),
    textAlign: "center",
  },
  subtitle: {
    marginBottom: verticalScale(24),
    textAlign: "center",
  },
  iconContainer: {
    marginBottom: verticalScale(24),
  },
  infoContainer: {
    backgroundColor: "#F5F9F8",
    padding: scale(16),
    borderRadius: scale(12),
    marginBottom: verticalScale(24),
  },
  infoText: {
    lineHeight: verticalScale(24),
  },
  documentsList: {
    width: "100%",
    marginBottom: verticalScale(32),
  },
  documentItem: {
    marginBottom: verticalScale(12),
  },
  actions: {
    width: "100%",
    gap: verticalScale(12),
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    alignItems: "center",
  },
  backButton: {
    marginTop: verticalScale(8),
  },
});
