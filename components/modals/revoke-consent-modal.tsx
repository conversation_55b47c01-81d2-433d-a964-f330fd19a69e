import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import React from "react";
import { StyleSheet, View } from "react-native";
import Modal from "react-native-modal";
import { useRevokeUserCompanyConsent } from "@/services/company/company-hooks";
import { useCurrentUser } from "@/services/user/user-hooks";
import { create } from "zustand";
import { useRefetchUserData } from "@/hooks/useRefetchUserData";

interface RevokeConsentModalProps {
  companyName: string;
  companyId: number;
  showButton?: boolean;
  isVisible?: boolean;
}

interface RevokeConsentStore {
  isVisible: boolean;
  setIsVisible: (isVisible: boolean) => void;
}

export const useRevokeConsentStore = create<RevokeConsentStore>((set) => ({
  isVisible: false,
  setIsVisible: (isVisible) => set({ isVisible }),
}));

export const RevokeConsentModal = ({
  companyName,
  showButton = true,
  companyId,
}: RevokeConsentModalProps) => {
  const { refreshUserData } = useRefetchUserData();
  const { isVisible, setIsVisible } = useRevokeConsentStore();
  const { data: user } = useCurrentUser();

  // Consent revocation mutation
  const { mutate: revokeUserCompanyConsent, isPending: isLoading } =
    useRevokeUserCompanyConsent({
      onSuccess: () => {
        console.log("✅ Consent revoked successfully");
        setIsVisible(false);
        refreshUserData();
      },
      onError: () => {
        console.error("❌ Failed to revoke consent");
        // Even if there's an error, the consent might have been revoked
        // Let's refresh the data to check the current state
        console.log("🔄 Refreshing data to check consent status...");
        refreshUserData();
        setIsVisible(false);
      },
    });

  const openModal = () => {
    setIsVisible(true);
  };

  const closeModal = () => {
    setIsVisible(false);
  };

  const onConfirm = async () => {
    if (!user?.email || !user?.user_id || !user?.applicant_id) {
      console.error("❌ Missing user information for consent revocation");
      return;
    }

    try {
      console.log("🚀 Starting consent revocation for:", companyName);

      revokeUserCompanyConsent({
        userId: user.user_id,
        companyId: companyId,
        applicant_id: user.applicant_id,
      });
    } catch (error) {
      console.error("❌ Error during consent revocation:", error);
    }
  };

  return (
    <View>
      {showButton && (
        <AppButton
          text="REVOKE"
          variant="destructive"
          size="lg"
          onPress={openModal}
        />
      )}

      <Modal
        isVisible={isVisible}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        onBackdropPress={closeModal}
        onSwipeComplete={closeModal}
        swipeDirection="down"
        style={styles.modal}
      >
        <View style={styles.container}>
          <View style={styles.content}>
            <AppText
              variant="primary"
              size="xl"
              weight="medium"
              style={styles.title}
            >
              Revoke Consent
            </AppText>

            <AppText variant="secondary" size="base" style={styles.message}>
              Are you sure you want to revoke consent for{" "}
              <AppText weight="semibold">{companyName}</AppText>? This will
              remove their access to your documents.
            </AppText>

            <View style={styles.buttonContainer}>
              <AppButton
                text="Cancel"
                variant="outline"
                onPress={closeModal}
                style={styles.cancelButton}
                disabled={isLoading}
              />

              <AppButton
                text={isLoading ? "Revoking..." : "Revoke Consent"}
                variant="destructive"
                onPress={onConfirm}
                style={styles.confirmButton}
                disabled={isLoading}
                isLoading={isLoading}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  container: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: scale(24),
    borderTopRightRadius: scale(24),
    paddingBottom: verticalScale(20),
    maxHeight: "80%",
  },
  content: {
    padding: scale(24),
  },
  title: {
    textAlign: "center",
    marginBottom: scale(16),
  },
  message: {
    textAlign: "center",
    marginBottom: scale(24),
    lineHeight: scale(20),
  },
  buttonContainer: {
    flexDirection: "row",
    gap: scale(12),
  },
  cancelButton: {
    flex: 1,
  },
  confirmButton: {
    flex: 1,
  },
});
