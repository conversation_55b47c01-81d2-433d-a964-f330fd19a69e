import { ICONS } from "@/constants/icons";
import { COLORS } from "@/constants/theme";
import { openKYBInBrowser, isKYBTier } from "@/services/sumsub/kyb-redirect-service";
import { startSequentialTierUpgrade } from "@/services/sumsub/sequential-tier-upgrade";
import { useCurrentUser } from "@/services/user/user-hooks";
import { RegistrationTypeOption, SUMSUB_LEVEL } from "@/types/auth";
import { scale, verticalScale } from "@/utils/styling-scale";
import {
  getTierNumber,
  extractTierNumber,
  TIER_NAMES,
  validateTierUpgrade,
  canUpgradeToCompanyTier,
} from "@/utils/tier-number";
import { Image } from "expo-image";
import React, { useState, useRef } from "react";
import { StyleSheet, View, ScrollView } from "react-native";
import Modal from "react-native-modal";
import { AppButton } from "../ui/app-button";
import { AppText } from "../ui/app-text";
import { useRefetchUserData } from "@/hooks/useRefetchUserData";

interface UpgradeSumSubModalProps {
  companyName: string;
  companyLogo?: string;
  companyTierNumber: number;
  companyTierSumsubLevel: SUMSUB_LEVEL;
}

type RegistrationType = "individual" | "company";

interface TierDataStructure {
  individual: Record<string, string[]>;
  company: Record<string, string[]>;
}

// Define the tier data mappings
const tierData: TierDataStructure = {
  individual: {
    tier1: [
      "Proof of Identity (POI)",
      "Proof of Residence (POR)",
      "Liveness Check",
      "AML Screening",
    ],
    tier2: [
      "Proof of Income/Source of Funds",
      "Employment Contract; and/or",
      "Payslip; and/or",
      "Tax Return",
    ],
    tier3: [
      "Suitability & Appropriateness Test – Questionnaire",
      "Economic Profile Questionnaire",
    ],
  },
  company: {
    tier1: [
      "Company Data Collection",
      "Company Representative Individual KYC",
      "Corporate Certificates",
      "Tax Residence and Tax Identification/VAT",
    ],
    tier2: [
      "Source of Funds",
      "Financial Statements; and/or Engagement Letter",
      "Ownership Structure",
      "Individual KYC on UBOs/Representatives",
      "AML Screening on Company and UBOs",
      "Bank Reference Letter (if applicable)",
    ],
    tier3: ["Questionnaire for Corporate KYB"],
  },
};

export const UpgradeSumSubModal = ({
  companyName,
  companyLogo,
  companyTierSumsubLevel,
}: UpgradeSumSubModalProps) => {
  const { refreshUserData } = useRefetchUserData();
  const { data: user } = useCurrentUser();
  const [isVisible, setIsVisible] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // State and ref for handling scroll inside the modal
  const [scrollOffset, setScrollOffset] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);

  // Get tier information
  const companyTierNumber = getTierNumber(companyTierSumsubLevel);
  const companyTierName =
    TIER_NAMES[companyTierNumber] || `Tier ${companyTierNumber}`;
  const currentUserTier = user?.state || "";

  // Determine user type early
  const isKYB = user?.registrationtype === RegistrationTypeOption.KYB;

  // Convert SUMSUB_LEVEL to tier string for analysis
  const companyTierString = isKYB
    ? `kybtier${companyTierNumber}`
    : `tier${companyTierNumber}`;
  console.log(
    `🔍 Company tier: ${companyTierSumsubLevel} → ${companyTierString} (KYB: ${isKYB})`
  );

  // Determine the actual tier to upgrade to (sequential progression)
  const upgradeAnalysis = canUpgradeToCompanyTier(
    currentUserTier,
    companyTierString
  );
  const actualUpgradeTier = upgradeAnalysis.nextTier || currentUserTier;
  const actualUpgradeTierNumber = extractTierNumber(actualUpgradeTier);
  const actualUpgradeTierName =
    TIER_NAMES[actualUpgradeTierNumber] || `Tier ${actualUpgradeTierNumber}`;

  // Validate the tier upgrade
  const tierValidation = validateTierUpgrade(
    currentUserTier,
    actualUpgradeTier
  );

  const registrationType: RegistrationType =
    (user?.registrationtype as RegistrationType) || "individual";
  const tierKey = `tier${actualUpgradeTierNumber}`;
  const requirements = tierData[registrationType][tierKey] || [];
  const isTier0 =
    user?.state === SUMSUB_LEVEL.KYB_TIER0 ||
    user?.state === SUMSUB_LEVEL.TIER0;
  const isKYBAndTier0 = isKYB && isTier0;

  // Check if user can upgrade
  const canUpgrade = upgradeAnalysis.canUpgrade && tierValidation.isValid;

  const handleUpgrade = async () => {
    if (!user?.email) {
      console.error("User email is not available.");
      return;
    }

    // Validate tier upgrade before proceeding
    if (!canUpgrade) {
      console.error("❌ Tier upgrade validation failed:", tierValidation.error);
      setValidationError(tierValidation.error || "Invalid tier upgrade");
      return;
    }

    // Clear any previous validation errors
    setValidationError(null);

    setIsVisible(false);

    try {
      console.log(
        `🎯 Starting tier upgrade: ${currentUserTier} → ${companyTierSumsubLevel}`
      );

      // For KYB tiers that should use web SDK (kybtier1 and kybtier2)
      if (isKYB && isKYBTier(companyTierSumsubLevel)) {
        console.log(
          `🌐 Using web SDK for KYB tier upgrade: ${companyTierSumsubLevel}`
        );
        await openKYBInBrowser(companyTierSumsubLevel);
      } else {
        // Use native SDK for other tiers
        console.log(
          `📱 Using native SDK for tier upgrade: ${companyTierSumsubLevel}`
        );
        await startSequentialTierUpgrade({
          currentTier: currentUserTier,
          targetTier: companyTierSumsubLevel,
          userEmail: user.email,
          isKYB,
          onProgress: refreshUserData,
          onComplete: refreshUserData,
        });
      }
    } catch (error) {
      console.error("Error during upgrade process:", error);
    }
  };

  const openModal = async () => {
    // Clear any previous validation errors
    setValidationError(null);

    if (isKYBAndTier0) {
      await openKYBInBrowser();
    } else {
      setIsVisible(true);
    }
  };

  const closeModal = () => {
    setIsVisible(false);
  };

  // Handlers to connect ScrollView to the Modal
  const handleOnScroll = (event: any) => {
    setScrollOffset(event.nativeEvent.contentOffset.y);
  };

  const handleScrollTo = (p: any) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo(p);
    }
  };

  return (
    <View>
      <AppButton
        text={isKYB ? "UPGRADE KYB" : "UPGRADE KYC"}
        size="lg"
        onPress={openModal}
      />
      <Modal
        isVisible={isVisible}
        onBackdropPress={closeModal}
        onSwipeComplete={closeModal}
        swipeDirection="down"
        onBackButtonPress={closeModal}
        style={styles.modal}
        // Props to enable scrolling inside the modal
        scrollTo={handleScrollTo}
        scrollOffset={scrollOffset}
        propagateSwipe={true}
      >
        <View style={styles.modalContent}>
          <ScrollView
            ref={scrollViewRef}
            onScroll={handleOnScroll}
            scrollEventThrottle={16} // Essential for smooth scroll tracking
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            {/* Header Content */}
            <View style={styles.headerContainer}>
              <View style={styles.logoContainer}>
                <Image
                  source={{ uri: companyLogo }}
                  style={styles.companyLogo}
                  cachePolicy="memory-disk"
                />
              </View>
              <AppText size="xl" weight="medium" style={styles.statusMessage}>
                &quot;{actualUpgradeTierName}&quot; is locked
              </AppText>
              <Image
                source={ICONS.DOCUMENT_LOCKED_ICON}
                style={styles.shieldIcon}
                transition={200}
                cachePolicy="memory-disk"
              />
            </View>

            {/* Multi-step upgrade warning */}
            {upgradeAnalysis.requiresMultipleSteps &&
              currentUserTier &&
              actualUpgradeTier && (
                <View style={styles.multiStepWarning}>
                  <AppText
                    size="sm"
                    weight="medium"
                    style={styles.warningTitle}
                  >
                    Step-by-step upgrade process
                  </AppText>
                  <AppText size="sm" style={styles.warningText}>
                    You are currently at{" "}
                    <AppText weight="semibold">
                      {TIER_NAMES[extractTierNumber(currentUserTier)]}
                    </AppText>{" "}
                    tier. This upgrade will first take you to{" "}
                    <AppText weight="semibold">{actualUpgradeTierName}</AppText>{" "}
                    tier. To access{" "}
                    <AppText weight="semibold">{companyName}</AppText> requiring{" "}
                    <AppText weight="semibold">{companyTierName}</AppText> tier,
                    you will need to complete additional upgrade steps after
                    this one.
                  </AppText>
                </View>
              )}

            {/* Validation Error Display */}
            {validationError && (
              <View style={styles.errorContainer}>
                <AppText size="sm" style={styles.errorText}>
                  ⚠️ {validationError}
                </AppText>
              </View>
            )}

            {/* Description Section */}
            <View style={styles.descriptionContainer}>
              <AppText size="base" style={styles.descriptionText}>
                In order to onboard with{" "}
                <AppText weight="semibold">{companyName}</AppText>, the required
                level of verification is{" "}
                <AppText weight="semibold">
                  &quot;{companyTierName}&quot;
                </AppText>
                . To verify your account to{" "}
                <AppText weight="semibold">
                  &quot;{actualUpgradeTierName}&quot;
                </AppText>
                , you will need to complete the following steps:
              </AppText>
            </View>

            {/* Requirements Section */}
            <View style={styles.requirementsContainer}>
              {requirements.map((requirement, index) => (
                <View key={index} style={styles.requirementRow}>
                  <AppText size="base" style={styles.bulletPoint}>
                    •
                  </AppText>
                  <AppText size="base" style={styles.requirementText}>
                    {requirement}
                  </AppText>
                </View>
              ))}
            </View>
          </ScrollView>

          {/* Fixed Buttons at Bottom */}
          <View style={styles.buttonContainer}>
            <AppButton
              text={`UPGRADE TO ${actualUpgradeTierName.toUpperCase()}`}
              variant="default"
              onPress={handleUpgrade}
              disabled={!canUpgrade}
            />
            <AppButton text="BACK" variant="outline" onPress={closeModal} />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  modalContent: {
    backgroundColor: COLORS.background,

    height: "100%",
    shadowColor: "rgba(78, 78, 78, 0.15)",
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 1,
    shadowRadius: scale(35),
    elevation: 10,
  },
  headerContainer: {
    alignItems: "center",
    paddingTop: verticalScale(20),
    paddingBottom: verticalScale(12),
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: scale(20),
    paddingBottom: verticalScale(180), // Increased padding to ensure all content is visible above buttons
  },
  logoContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: scale(12),
    marginBottom: verticalScale(8),
  },
  companyLogo: {
    width: scale(80),
    height: scale(40),
    borderRadius: scale(10),
  },
  companyName: {
    color: COLORS.secondary,
    textAlign: "center",
  },
  statusMessage: {
    color: COLORS.primary,
    marginBottom: verticalScale(12),
    textAlign: "center",
    letterSpacing: -0.5,
  },
  shieldIcon: {
    width: scale(60),
    height: scale(60),
  },
  requirementsContainer: {
    marginBottom: verticalScale(12),
  },
  requirementRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: verticalScale(4),
  },
  bulletPoint: {
    color: COLORS.mediumGray,
    marginRight: scale(8),
    fontSize: scale(16),
    lineHeight: verticalScale(20),
  },
  requirementText: {
    flex: 1,
    color: COLORS.mediumGray,
    textAlign: "left",
    lineHeight: verticalScale(20),
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    gap: verticalScale(12),
    paddingHorizontal: scale(20),
    paddingTop: verticalScale(16),
    paddingBottom: verticalScale(30),
    backgroundColor: COLORS.background,
    borderTopWidth: 1,
    borderTopColor: "#F0F0F0",
  },

  multiStepWarning: {
    marginTop: verticalScale(8),
    marginBottom: verticalScale(8),
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(8),
    backgroundColor: "#EBF8FF",
    borderColor: "#BEE3F8",
    borderWidth: 1,
    borderRadius: scale(8),
  },
  warningTitle: {
    color: "#2B6CB0",
    marginBottom: verticalScale(2),
  },
  warningText: {
    color: "#2C5282",
    lineHeight: verticalScale(18),
  },
  descriptionContainer: {
    backgroundColor: "#EFF7ED",
    borderRadius: scale(8),
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(8),
    marginTop: verticalScale(8),
    marginBottom: verticalScale(8),
  },
  descriptionText: {
    color: COLORS.primary,
    lineHeight: verticalScale(20),
    textAlign: "center",
  },
  consentContainer: {
    backgroundColor: "#F9F9F9",
    borderRadius: scale(8),
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(8),
    marginTop: verticalScale(8),
    marginBottom: verticalScale(8),
  },
  consentText: {
    color: COLORS.mediumGray,
    lineHeight: verticalScale(18),
    textAlign: "left",
  },
  errorContainer: {
    backgroundColor: "#FEF2F2",
    borderColor: "#FECACA",
    borderWidth: 1,
    borderRadius: scale(8),
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(8),
    marginTop: verticalScale(8),
    marginBottom: verticalScale(8),
  },
  errorText: {
    color: "#DC2626",
    lineHeight: verticalScale(18),
    textAlign: "center",
  },
});
