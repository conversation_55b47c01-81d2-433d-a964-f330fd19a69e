import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import React from "react";
import { Modal, StyleSheet, View } from "react-native";
import { AppButton } from "../ui/app-button";
import { AppText } from "../ui/app-text";

interface SignOutConfirmationModalProps {
  isVisible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export const SignOutConfirmationModal = ({
  isVisible,
  onConfirm,
  onCancel,
  isLoading = false,
}: SignOutConfirmationModalProps) => {
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContent}>
          <AppText size="xl" weight="bold" style={styles.title}>
            Sign Out
          </AppText>

          <AppText style={styles.description}>
            Are you sure you want to leave?
          </AppText>

          <View style={styles.actions}>
            <AppButton
              text="YES"
              variant="destructive"
              size="sm"
              onPress={onConfirm}
              isLoading={isLoading}
              disabled={isLoading}
            />
            <AppButton
              text="NO"
              variant="ghost"
              size="sm"
              onPress={onCancel}
              disabled={isLoading}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: scale(16),
  },
  modalContent: {
    backgroundColor: COLORS.background,
    borderRadius: scale(24),
    padding: scale(24),
    width: "100%",
    maxWidth: scale(400),
    borderWidth: 1,
    borderColor: COLORS.error + "20", // 20% opacity for subtle error indication
  },
  title: {
    textAlign: "center",
    marginBottom: verticalScale(16),
  },
  description: {
    textAlign: "center",
    marginBottom: verticalScale(24),
    lineHeight: verticalScale(24),
  },
  actions: {
    gap: verticalScale(12),
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    alignItems: "center",
  },
});
