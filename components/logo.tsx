import { IMAGES } from "@/constants/images";
import { Image } from "expo-image";
import React from "react";

interface LogoProps {
  withTitle?: boolean;
  className?: string;
}
const Logo = ({ withTitle = false, className }: LogoProps) => {
  return (
    <>
      {withTitle ? (
        <Image
          source={IMAGES.logoTitle}
          contentFit="cover"
          transition={200}
          cachePolicy="memory-disk"
          style={{ width: 131, height: 80 }}
        />
      ) : (
        <Image
          source={IMAGES.logo}
          contentFit="cover"
          transition={200}
          cachePolicy="memory-disk"
          style={{ width: 56, height: 63 }}
        />
      )}
    </>
  );
};

export default Logo;
