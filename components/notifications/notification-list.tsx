import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { Notification } from "@/types/notification";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  ActivityIndicator,
  FlatList,
  Pressable,
  RefreshControl,
  StyleSheet,
  View,
} from "react-native";
import { NotificationItem } from "./notification-item";

interface NotificationListProps {
  notifications: Notification[];
  isLoading?: boolean;
  unreadCount: number;
  onRefresh?: () => void;
  onNotificationPress?: (notification: Notification) => void;
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
  onDeleteNotification?: (id: string) => void;
  onClearAll?: () => void;
}

export const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  isLoading = false,
  unreadCount,
  onRefresh,
  onNotificationPress,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onClearAll,
}) => {
  const renderNotificationItem = ({ item }: { item: Notification }) => (
    <NotificationItem
      notification={item}
      onPress={onNotificationPress}
      onMarkAsRead={onMarkAsRead}
      onDelete={onDeleteNotification}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {unreadCount > 0 && (
        <View style={styles.headerActions}>
          <Pressable style={styles.actionButton} onPress={onMarkAllAsRead}>
            <AppText
              variant="primary"
              size="sm"
              weight="medium"
              style={styles.actionText}
            >
              Mark all read
            </AppText>
          </Pressable>

          <Pressable style={styles.actionButton} onPress={onClearAll}>
            <AppText
              variant="secondary"
              size="sm"
              weight="medium"
              style={styles.actionText}
            >
              Clear all
            </AppText>
          </Pressable>
        </View>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="notifications-outline"
        size={scale(40)}
        color={COLORS.grayLight}
        style={styles.emptyIcon}
      />

      <AppText
        variant="primary"
        size="base"
        weight="medium"
        style={styles.emptyTitle}
      >
        No notifications
      </AppText>

      <AppText variant="secondary" size="sm" style={styles.emptyDescription}>
        When you receive notifications, they&apos;ll appear here
      </AppText>
    </View>
  );

  if (isLoading && notifications.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <View style={styles.loadingContent}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <AppText variant="secondary" size="sm" style={styles.loadingText}>
            Loading notifications...
          </AppText>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={notifications.length > 0 ? renderHeader : null}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={[
          styles.listContent,
          notifications.length === 0 && styles.listContentEmpty,
        ]}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={onRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  listContent: {
    padding: scale(18),
    paddingTop: scale(12),
  },
  listContentEmpty: {
    flexGrow: 1,
  },
  header: {
    marginBottom: verticalScale(12),
  },
  headerActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: scale(16),
  },
  actionButton: {
    paddingVertical: verticalScale(6),
    paddingHorizontal: scale(8),
  },
  actionText: {
    fontSize: scale(13),
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: scale(32),
  },
  emptyIcon: {
    marginBottom: verticalScale(16),
  },
  emptyTitle: {
    marginBottom: verticalScale(8),
    textAlign: "center",
  },
  emptyDescription: {
    textAlign: "center",
    lineHeight: scale(18),
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContent: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    gap: verticalScale(12),
  },
  loadingText: {
    marginTop: verticalScale(8),
  },
});
