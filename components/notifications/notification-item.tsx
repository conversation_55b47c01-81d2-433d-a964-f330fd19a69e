import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { Notification } from "@/types/notification";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { Alert, Pressable, StyleSheet, View } from "react-native";

interface NotificationItemProps {
  notification: Notification;
  onPress?: (notification: Notification) => void;
  onMarkAsRead?: (id: string) => void;
  onDelete?: (id: string) => void;
}

/**
 * Format time ago string
 */
const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();

  const minutes = Math.floor(diffInMs / (1000 * 60));
  const hours = Math.floor(diffInMs / (1000 * 60 * 60));
  const days = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (minutes < 1) return "Just now";
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;

  return date.toLocaleDateString();
};

export const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onPress,
  onMarkAsRead,
  onDelete,
}) => {
  const timeAgo = formatTimeAgo(notification.createdAt);

  const handlePress = () => {
    // Mark as read when pressed
    if (!notification.read && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }

    // Handle notification press
    if (onPress) {
      onPress(notification);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      "Delete Notification",
      "Are you sure you want to delete this notification?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => onDelete && onDelete(notification.id),
        },
      ]
    );
  };

  return (
    <Pressable
      style={styles.container}
      onPress={handlePress}
      android_ripple={{ color: COLORS.primaryLight }}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.titleRow}>
              <AppText
                variant="primary"
                size="sm"
                weight={notification.read ? "medium" : "semibold"}
                style={styles.title}
                numberOfLines={1}
              >
                {notification.title}
              </AppText>

              {!notification.read && <View style={styles.unreadBadge} />}

              <AppText variant="secondary" size="xs" style={styles.timeAgo}>
                {timeAgo}
              </AppText>
            </View>

            <AppText
              variant="secondary"
              size="xs"
              style={styles.message}
              numberOfLines={1}
            >
              {notification.message}
            </AppText>
          </View>

          <Pressable
            style={styles.deleteButton}
            onPress={handleDelete}
            hitSlop={scale(8)}
          >
            <Ionicons name="close" size={scale(14)} color={COLORS.mediumGray} />
          </Pressable>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.background,
    borderRadius: scale(10),
    marginBottom: verticalScale(12),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 3,
  },
  content: {
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(10),
  },
  header: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  headerContent: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: verticalScale(2),
  },
  title: {
    flex: 1,
    marginRight: scale(6),
  },
  unreadBadge: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(4),
    backgroundColor: COLORS.primary,
    marginRight: scale(6),
  },
  timeAgo: {
    color: COLORS.mediumGray,
    minWidth: scale(45),
    textAlign: "right",
  },
  message: {
    lineHeight: scale(14),
  },
  deleteButton: {
    padding: scale(4),
    marginLeft: scale(8),
  },
});
