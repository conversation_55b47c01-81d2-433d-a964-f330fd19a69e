import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React, { forwardRef, useCallback } from "react";
import {
  Platform,
  StyleProp,
  StyleSheet,
  TextInput,
  TextInputProps,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";

interface FormSearchProps extends TextInputProps {
  containerStyle?: StyleProp<ViewStyle>;
  onClear?: () => void;
  searchIconStyle?: StyleProp<TextStyle>;
  label?: string;
}

export const FormSearch = forwardRef<TextInput, FormSearchProps>(
  (
    {
      containerStyle,
      style,
      onFocus,
      onBlur,
      value,
      onClear,
      searchIconStyle,
      ...props
    },
    ref
  ) => {
    const handleFocus = useCallback(
      (e: any) => {
        if (Platform.OS === "ios") {
          // Ensure proper focus handling on iOS
          e.target?.focus();
        }
        onFocus?.(e);
      },
      [onFocus]
    );

    const handleBlur = useCallback(
      (e: any) => {
        if (Platform.OS === "ios") {
          // Ensure proper blur handling on iOS
          e.target?.blur();
        }
        onBlur?.(e);
      },
      [onBlur]
    );

    return (
      <View style={[styles.container, containerStyle]}>
        <View style={styles.inputContainer}>
          <TextInput
            ref={ref}
            style={[styles.input, style]}
            placeholderTextColor="#999"
            autoCorrect={false}
            spellCheck={false}
            autoCapitalize="none"
            keyboardAppearance="light"
            enablesReturnKeyAutomatically
            onFocus={handleFocus}
            onBlur={handleBlur}
            rejectResponderTermination={false}
            returnKeyType="search"
            value={value}
            {...props}
          />

          <TouchableOpacity
            style={styles.searchIconButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            onPress={() => {
              // Search action
            }}
          >
            <Ionicons
              name="search-outline"
              size={scale(20)}
              color="#333"
              style={searchIconStyle}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  }
);

FormSearch.displayName = "FormSearch";

const styles = StyleSheet.create({
  container: {
    marginBottom: verticalScale(16),
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderWidth: 1,
    borderColor: "#C8E2CE",
    borderRadius: scale(40),
    height: verticalScale(40),
    paddingHorizontal: scale(20),
  },
  input: {
    flex: 1,
    height: "100%",
    fontSize: scale(14),
    color: "#000",
    padding: 0,
  },
  searchIconButton: {
    justifyContent: "center",
    alignItems: "center",
  },
});
