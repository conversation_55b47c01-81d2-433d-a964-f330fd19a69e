import { ICONS } from "@/constants/icons";
import { COLORS } from "@/constants/theme";
import { scale } from "@/utils/styling-scale";
import { Image } from "expo-image";
import React from "react";
import {
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { AppText } from "../ui/app-text";

type ImageSelectionCardProps = {
  title: string;
  imageSource: any;
  isSelected: boolean;
  onPress: () => void;
  imageStyle?: {
    width?: number;
    height?: number;
  };
  style?: StyleProp<ViewStyle>;
};

export function ImageSelectionCard({
  title,
  imageSource,
  isSelected,
  onPress,
  imageStyle = { width: scale(50), height: scale(50) },
  style,
}: ImageSelectionCardProps) {
  return (
    <TouchableOpacity
      style={[
        styles.container,
        isSelected ? styles.containerSelected : styles.containerUnselected,
        style,
      ]}
      onPress={onPress}
    >
      <View style={styles.checkIconContainer}>
        <Image
          source={ICONS.CHECK_ICON}
          style={[
            styles.checkIcon,
            {
              tintColor: isSelected ? COLORS.primary : COLORS.mediumGray,
            },
          ]}
          contentFit="contain"
          transition={200}
          cachePolicy="memory-disk"
        />
      </View>

      <Image
        source={imageSource}
        style={imageStyle}
        contentFit="contain"
        transition={200}
        cachePolicy="memory-disk"
      />
      <AppText
        variant="primary"
        weight="semibold"
        size="lg"
        style={styles.title}
      >
        {title}
      </AppText>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: scale(24),
    alignItems: "center",
    justifyContent: "center",
    width: scale(150),
    height: scale(150),
    position: "relative",
  },
  containerSelected: {
    borderColor: COLORS.primary,
    borderWidth: 2,
  },
  containerUnselected: {
    borderColor: COLORS.mediumGray,
  },
  checkIconContainer: {
    position: "absolute",
    top: scale(12),
    right: scale(12),
    zIndex: 10,
  },
  checkIcon: {
    width: scale(14),
    height: scale(14),
  },
  title: {
    marginTop: scale(4),
  },
});
