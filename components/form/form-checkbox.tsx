import { COLORS } from "@/constants/theme";
import { scale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  StyleProp,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";

interface FormCheckboxProps {
  label: string;
  checked: boolean;
  onToggle: () => void;
  style?: StyleProp<ViewStyle>;
}

export const FormCheckbox = ({
  label,
  checked,
  onToggle,
  style,
}: FormCheckboxProps) => {
  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onToggle}>
      <View
        style={[
          styles.checkbox,
          checked ? styles.checkboxChecked : styles.checkboxUnchecked,
        ]}
      >
        {checked && (
          <Ionicons name="checkmark" size={18} color={COLORS.background} />
        )}
      </View>
      <Text style={styles.label}>{label}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  checkbox: {
    width: scale(24),
    height: scale(24),
    borderRadius: scale(6),
    marginRight: scale(12),
    justifyContent: "center",
    alignItems: "center",
  },
  checkboxChecked: {
    backgroundColor: COLORS.primaryLight,
  },
  checkboxUnchecked: {
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.mediumGray,
  },
  label: {
    color: COLORS.primary,
    fontSize: scale(16),
  },
});
