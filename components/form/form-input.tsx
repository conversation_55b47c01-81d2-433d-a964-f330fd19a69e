import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import React, { forwardRef, useCallback } from "react";
import {
  Platform,
  StyleProp,
  StyleSheet,
  TextInput,
  TextInputProps,
  View,
  ViewStyle,
} from "react-native";
import { AppText } from "../ui/app-text";

interface FormInputProps extends TextInputProps {
  label?: string;
  error?: string;
  name: string;
  containerStyle?: StyleProp<ViewStyle>;
}

export const FormInput = forwardRef<TextInput, FormInputProps>(
  ({ label, error, containerStyle, style, onFocus, onBlur, ...props }, ref) => {
    const handleFocus = useCallback(
      (e: any) => {
        if (Platform.OS === "ios") {
          // Ensure proper focus handling on iOS
          e.target?.focus();
        }
        onFocus?.(e);
      },
      [onFocus]
    );

    const handleBlur = useCallback(
      (e: any) => {
        if (Platform.OS === "ios") {
          // Ensure proper blur handling on iOS
          e.target?.blur();
        }
        onBlur?.(e);
      },
      [onBlur]
    );

    return (
      <View style={[styles.container, containerStyle]}>
        {label && (
          <AppText
            variant="primary"
            weight="medium"
            size="sm"
            style={styles.label}
          >
            {label}
          </AppText>
        )}
        <TextInput
          ref={ref}
          style={[
            styles.input,
            error ? styles.inputError : styles.inputDefault,
            style,
          ]}
          placeholderTextColor={COLORS.mediumGray}
          autoCorrect={false}
          spellCheck={false}
          autoCapitalize="none"
          keyboardAppearance="light"
          enablesReturnKeyAutomatically
          onFocus={handleFocus}
          onBlur={handleBlur}
          rejectResponderTermination={false}
          {...props}
        />
        {error && (
          <AppText variant="destructive" size="xs" style={styles.errorText}>
            {error}
          </AppText>
        )}
      </View>
    );
  }
);

FormInput.displayName = "FormInput";

const styles = StyleSheet.create({
  container: {
    width: "100%",
    gap: verticalScale(8),
    marginBottom: verticalScale(2),
  },
  label: {
    marginBottom: verticalScale(2),
  },
  input: {
    height: verticalScale(56),
    paddingHorizontal: scale(24),
    borderWidth: 1,
    borderRadius: scale(28),
    fontSize: scale(16),
    color: COLORS.primary,
  },
  inputDefault: {
    borderColor: COLORS.mediumGray,
    backgroundColor: COLORS.background,
  },
  inputError: {
    borderColor: COLORS.error,
    backgroundColor: COLORS.background,
  },
  errorText: {
    marginTop: verticalScale(4),
  },
});
