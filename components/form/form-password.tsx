import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React, { forwardRef, useCallback, useState } from "react";
import {
  Platform,
  StyleProp,
  StyleSheet,
  TextInput,
  TextInputProps,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { AppText } from "../ui/app-text";

interface FormPasswordProps extends TextInputProps {
  label?: string;
  error?: string;
  name: string;
  containerStyle?: StyleProp<ViewStyle>;
}

export const FormPassword = forwardRef<TextInput, FormPasswordProps>(
  ({ label, error, containerStyle, style, onFocus, onBlur, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);

    const handleFocus = useCallback(
      (e: any) => {
        if (Platform.OS === "ios") {
          // Ensure proper focus handling on iOS
          e.target?.focus();
        }
        onFocus?.(e);
      },
      [onFocus]
    );

    const handleBlur = useCallback(
      (e: any) => {
        if (Platform.OS === "ios") {
          // Ensure proper blur handling on iOS
          e.target?.blur();
        }
        onBlur?.(e);
      },
      [onBlur]
    );

    return (
      <View style={[styles.container, containerStyle]}>
        {label && (
          <AppText
            variant="primary"
            weight="medium"
            size="sm"
            style={styles.label}
          >
            {label}
          </AppText>
        )}
        <View style={styles.inputContainer}>
          <TextInput
            ref={ref}
            {...props}
            secureTextEntry={!showPassword}
            textContentType="none"
            autoComplete="off"
            style={[
              styles.input,
              error ? styles.inputError : styles.inputDefault,
              style,
            ]}
            placeholderTextColor={COLORS.mediumGray}
            autoCorrect={false}
            spellCheck={false}
            autoCapitalize="none"
            keyboardAppearance="light"
            onFocus={handleFocus}
            onBlur={handleBlur}
            returnKeyType="done"
            enablesReturnKeyAutomatically
            rejectResponderTermination={false}
          />
          <TouchableOpacity
            onPress={() => setShowPassword(!showPassword)}
            style={styles.eyeButton}
          >
            <Ionicons
              name={showPassword ? "eye-off" : "eye"}
              size={24}
              color={showPassword ? COLORS.primary : COLORS.mediumGray}
            />
          </TouchableOpacity>
        </View>
        {error && (
          <AppText variant="destructive" size="xs" style={styles.errorText}>
            {error}
          </AppText>
        )}
      </View>
    );
  }
);

FormPassword.displayName = "FormPassword";

const styles = StyleSheet.create({
  container: {
    width: "100%",
    gap: verticalScale(8),
    marginBottom: verticalScale(4),
  },
  label: {
    marginBottom: verticalScale(2),
  },
  inputContainer: {
    position: "relative",
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    height: verticalScale(56),
    paddingHorizontal: scale(24),
    paddingRight: scale(56),
    borderWidth: 1,
    borderRadius: scale(28),
    fontSize: scale(16),
    color: COLORS.primary,
    width: "100%",
  },
  inputDefault: {
    borderColor: COLORS.mediumGray,
    backgroundColor: COLORS.background,
  },
  inputError: {
    borderColor: COLORS.error,
    backgroundColor: COLORS.background,
  },
  eyeButton: {
    position: "absolute",
    right: scale(16),
    height: "100%",
    width: scale(40),
    alignItems: "center",
    justifyContent: "center",
  },
  errorText: {
    marginTop: verticalScale(4),
  },
});
