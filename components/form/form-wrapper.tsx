import { scale } from "@/utils/styling-scale";
import React from "react";
import { StyleProp, StyleSheet, View, ViewStyle } from "react-native";

interface FormWrapperProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  enableFlexWrap?: boolean;
}

const FormWrapper = ({
  children,
  style,
  enableFlexWrap = false,
}: FormWrapperProps) => {
  return (
    <View style={[styles.container, style, enableFlexWrap && styles.flexWrap]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    gap: scale(10),
    paddingHorizontal: scale(30),
    width: "100%",
  },
  flexWrap: {
    flexWrap: "wrap",
  },
});

export { FormWrapper };
