import React, { useEffect } from 'react';
import { View } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { scale, verticalScale } from '@/utils/styling-scale';

interface AnimatedTabIndicatorProps {
  isActive: boolean;
  color?: string;
  size?: number;
}

export const AnimatedTabIndicator: React.FC<AnimatedTabIndicatorProps> = ({
  isActive,
  color = "#1B3142",
  size = scale(7)
}) => {
  const scaleValue = useSharedValue(0);
  const opacityValue = useSharedValue(0);

  useEffect(() => {
    if (isActive) {
      scaleValue.value = withSpring(1, {
        damping: 15,
        stiffness: 150,
      });
      opacityValue.value = withTiming(1, { duration: 200 });
    } else {
      scaleValue.value = withSpring(0, {
        damping: 15,
        stiffness: 150,
      });
      opacityValue.value = withTiming(0, { duration: 200 });
    }
  }, [isActive]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scaleValue.value }],
      opacity: opacityValue.value,
    };
  });

  return (
    <View style={{ 
      height: size, 
      width: size, 
      marginTop: verticalScale(8),
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Animated.View
        style={[
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            backgroundColor: color,
          },
          animatedStyle,
        ]}
      />
    </View>
  );
};
