import { COLORS } from "@/constants/theme";
import { analyzeError, logError } from "@/utils/error-handler";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React, { Component, ReactNode } from "react";
import { StyleSheet, View } from "react-native";
import { AppButton } from "./app-button";
import { AppText } from "./app-text";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

/**
 * Error Boundary component that catches JavaScript errors anywhere in the child component tree
 * and displays a fallback UI instead of crashing the entire app
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // Log the error
    const appError = analyzeError(error);
    logError(appError, "ErrorBoundary");

    // Store error info in state
    this.setState({ errorInfo });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <View style={styles.container}>
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons
                name="warning-outline"
                size={scale(64)}
                color={COLORS.error}
              />
            </View>

            <AppText
              variant="primary"
              size="xl"
              weight="semibold"
              style={styles.title}
            >
              Oops! Something went wrong
            </AppText>

            <AppText variant="secondary" size="base" style={styles.description}>
              We encountered an unexpected error. Don&apos;t worry, your data is
              safe.
            </AppText>

            {__DEV__ && this.state.error && (
              <View style={styles.errorDetails}>
                <AppText
                  variant="destructive"
                  size="sm"
                  style={styles.errorText}
                >
                  {this.state.error.message}
                </AppText>
              </View>
            )}

            <View style={styles.actions}>
              <AppButton
                text="Try Again"
                onPress={this.handleRetry}
                variant="default"
                size="md"
                buttonStyle={styles.retryButton}
              />
            </View>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based error boundary for functional components
 * Use this when you need error boundary functionality in a functional component
 */
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: any) => void
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName || Component.name || "Component"
  })`;

  return WrappedComponent;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: "center",
    alignItems: "center",
    padding: scale(24),
  },
  content: {
    alignItems: "center",
    maxWidth: scale(400),
    width: "100%",
  },
  iconContainer: {
    marginBottom: verticalScale(24),
  },
  title: {
    textAlign: "center",
    marginBottom: verticalScale(16),
  },
  description: {
    textAlign: "center",
    lineHeight: verticalScale(24),
    marginBottom: verticalScale(32),
  },
  errorDetails: {
    backgroundColor: COLORS.error + "10",
    padding: scale(16),
    borderRadius: scale(8),
    marginBottom: verticalScale(24),
    width: "100%",
  },
  errorText: {
    textAlign: "center",
    fontFamily: "monospace",
  },
  actions: {
    width: "100%",
    gap: verticalScale(12),
  },
  retryButton: {
    width: "100%",
  },
});
