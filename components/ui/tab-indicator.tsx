import React from "react";
import Svg, { Circle } from "react-native-svg";
import { scale } from "@/utils/styling-scale";

interface TabIndicatorProps {
  isActive: boolean;
  color?: string;
  size?: number;
}

export const TabIndicator: React.FC<TabIndicatorProps> = ({
  isActive,
  size = scale(7),
}) => {
  let color = isActive ? "#1B3142" : "transparent";

  const radius = size / 2;

  return (
    <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
      <Circle cx={radius} cy={radius} r={radius} fill={color} />
    </Svg>
  );
};
