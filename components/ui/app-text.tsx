import { COLORS } from "@/constants/theme";
import { isVerySmallDevice } from "@/utils/platform-utils";
import { scale } from "@/utils/styling-scale";
import { Link, LinkProps } from "expo-router";
import React from "react";
import {
  StyleSheet,
  Text,
  TextProps,
  TouchableOpacity,
  View,
} from "react-native";

type TextVariant =
  | "default"
  | "primary"
  | "secondary"
  | "destructive"
  | "success"
  | "link"
  | "muted";
type TextSize = "xs" | "sm" | "base" | "lg" | "xl" | "2xl" | "3xl" | "default";
type TextWeight = "normal" | "medium" | "semibold" | "bold";

interface AppTextProps extends TextProps {
  children: React.ReactNode;
  style?: TextProps["style"];
  variant?: TextVariant;
  size?: TextSize;
  weight?: TextWeight;
}

const getVariantStyle = (variant: TextVariant) => {
  switch (variant) {
    case "default":
      return [styles.text, styles.default];
    case "primary":
      return [styles.text, styles.primary];
    case "secondary":
      return [styles.text, styles.secondary];
    case "destructive":
      return [styles.text, styles.destructive];
    case "success":
      return [styles.text, styles.success];
    case "link":
      return [styles.text, styles.link];
    case "muted":
      return [styles.text, styles.muted];
    default:
      return [styles.text, styles.default];
  }
};

const getSizeStyle = (size: TextSize) => {
  switch (size) {
    case "xs":
      return styles.xs;
    case "sm":
      return styles.sm;
    case "base":
      return styles.base;
    case "lg":
      return styles.lg;
    case "xl":
      return styles.xl;
    case "2xl":
      return styles.xxl;
    case "3xl":
      return styles.xxxl;
    default:
      return styles.base;
  }
};

const getWeightStyle = (weight: TextWeight) => {
  switch (weight) {
    case "normal":
      return styles.weightNormal;
    case "medium":
      return styles.weightMedium;
    case "semibold":
      return styles.weightSemibold;
    case "bold":
      return styles.weightBold;
    default:
      return styles.weightNormal;
  }
};

export const AppText = ({
  children,
  variant = "primary",
  size = "base",
  weight = "normal",
  style,
  ...props
}: AppTextProps) => {
  return (
    <Text
      style={[
        getVariantStyle(variant),
        getSizeStyle(size),
        getWeightStyle(weight),
        style,
      ]}
      {...props}
    >
      {children}
    </Text>
  );
};

interface AppTextWithLinkProps extends Omit<AppTextProps, "children"> {
  textBefore?: string;
  textAfter?: string;
  linkText: string;
  linkVariant?: TextVariant;
  linkSize?: TextSize;
  linkWeight?: TextWeight;
  linkStyle?: TextProps["style"];
  href?: LinkProps["href"];
  onLinkPress?: () => void;
}

export const AppTextWithLink = ({
  textBefore,
  textAfter,
  linkText,
  variant = "primary",
  size = "base",
  weight = "normal",
  linkVariant = "link",
  linkSize,
  linkWeight = "bold",
  style,
  linkStyle,
  href,
  onLinkPress,
  ...props
}: AppTextWithLinkProps) => {
  const linkTextComponent = (
    <Text
      style={[
        getVariantStyle(linkVariant),
        getSizeStyle(linkSize || size),
        getWeightStyle(linkWeight),
        linkStyle,
      ]}
    >
      {linkText}
    </Text>
  );

  return (
    <View style={isVerySmallDevice ? styles.containerSmall : styles.container}>
      {textBefore && (
        <Text
          style={[
            getVariantStyle(variant),
            getSizeStyle(size),
            getWeightStyle(weight),
            style,
          ]}
          {...props}
        >
          {textBefore}{" "}
        </Text>
      )}

      {href ? (
        <Link href={href}>{linkTextComponent}</Link>
      ) : (
        <TouchableOpacity onPress={onLinkPress}>
          {linkTextComponent}
        </TouchableOpacity>
      )}

      {textAfter && (
        <Text
          style={[
            getVariantStyle(variant),
            getSizeStyle(size),
            getWeightStyle(weight),
            style,
          ]}
          {...props}
        >
          {" "}
          {textAfter}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "center",
    flexWrap: "wrap",
    alignItems: "center",
  },
  containerSmall: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
  },
  text: {
    fontSize: scale(16), // base size
  },
  // Variants
  default: {
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  primary: {
    color: COLORS.primary,
  },
  secondary: {
    color: COLORS.gray,
  },
  destructive: {
    color: COLORS.error,
  },
  success: {
    color: COLORS.success,
  },
  link: {
    color: COLORS.primary,
    textDecorationLine: "underline",
  },
  muted: {
    color: COLORS.mediumGray,
  },
  // Sizes
  xs: {
    fontSize: scale(12),
  },
  sm: {
    fontSize: scale(14),
  },
  base: {
    fontSize: scale(16),
  },
  lg: {
    fontSize: scale(18),
  },
  xl: {
    fontSize: scale(20),
  },
  xxl: {
    fontSize: scale(24),
  },
  xxxl: {
    fontSize: scale(34),
  },
  // Weights
  weightNormal: {
    fontWeight: "400",
  },
  weightMedium: {
    fontWeight: "500",
  },
  weightSemibold: {
    fontWeight: "600",
  },
  weightBold: {
    fontWeight: "700",
  },
});
