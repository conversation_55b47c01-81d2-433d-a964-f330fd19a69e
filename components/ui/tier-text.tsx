import { getTierDisplayName } from "@/utils/tier-number";
import React from "react";
import { AppText } from "./app-text";

interface TierTextProps {
  tier?: number;
  prefix?: string;
  suffix?: string;
  uppercase?: boolean;
  style?: any;
  size?: "xs" | "sm" | "base" | "lg" | "xl" | "2xl";
  weight?: "normal" | "medium" | "semibold" | "bold";
  variant?: "primary" | "secondary";
}

export const TierText = ({
  tier,
  prefix = "",
  suffix = "",
  uppercase = false,
  style,
  size = "base",
  weight = "normal",
  variant = "primary",
}: TierTextProps) => {
  const tierName = getTierDisplayName(tier);
  const displayText = `${prefix}${
    uppercase ? tierName.toUpperCase() : tierName
  }${suffix}`;

  return (
    <AppText variant={variant} size={size} weight={weight} style={style}>
      {displayText}
    </AppText>
  );
};
