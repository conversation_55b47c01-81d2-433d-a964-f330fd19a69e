import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Link, LinkProps } from "expo-router";
import * as React from "react";
import {
  ActivityIndicator,
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from "react-native";

type ButtonVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "ghost"
  | "link"
  | "icon";

type ButtonSize = "default" | "sm" | "md" | "lg" | "icon" | "text";

const getButtonVariantStyle = (variant: ButtonVariant): ViewStyle => {
  switch (variant) {
    case "default":
      return styles.buttonDefault;
    case "destructive":
      return styles.buttonDestructive;
    case "outline":
      return styles.buttonOutline;
    case "secondary":
      return styles.buttonSecondary;
    case "ghost":
      return styles.buttonGhost;
    case "link":
      return styles.buttonLink;
    case "icon":
      return styles.buttonIcon;
    default:
      return styles.buttonDefault;
  }
};

const getButtonSizeStyle = (size: ButtonSize): ViewStyle => {
  switch (size) {
    case "sm":
      return styles.buttonSm;
    case "md":
      return styles.buttonMd;
    case "lg":
      return styles.buttonLg;
    case "icon":
      return styles.buttonIconSize;
    case "text":
      return styles.buttonText;
    default:
      return styles.buttonDefaultSize;
  }
};

const getTextVariantStyle = (variant: ButtonVariant): TextStyle => {
  switch (variant) {
    case "default":
      return styles.textDefault;
    case "destructive":
      return styles.textDestructive;
    case "outline":
      return styles.textOutline;
    case "secondary":
      return styles.textSecondary;
    case "ghost":
      return styles.textGhost;
    case "link":
      return styles.textLink;
    case "icon":
      return styles.textIcon;
    default:
      return styles.textDefault;
  }
};

interface BtnProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  text: string;
  icon?: React.ReactNode;
  buttonStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  isLoading?: boolean;
  asLink?: boolean;
}

interface AppButtonProps extends TouchableOpacityProps, BtnProps {}

const AppButton = (props: AppButtonProps) => {
  const {
    variant = "default",
    size = "default",
    buttonStyle,
    textStyle,
    text,
    icon,
    asLink,
    isLoading,
    ...rest
  } = props;

  return (
    <TouchableOpacity
      {...rest}
      style={[
        styles.button,
        getButtonVariantStyle(variant),
        getButtonSizeStyle(size),
        buttonStyle,
      ]}
    >
      <Text
        style={[
          styles.text,
          styles.buttonTextBase,
          getTextVariantStyle(variant),
          textStyle,
        ]}
      >
        {isLoading ? <ActivityIndicator color="#FFFFFF" /> : text}
      </Text>
      {icon && <View>{icon}</View>}
    </TouchableOpacity>
  );
};

interface AppLinkProps extends Omit<LinkProps, "style">, BtnProps {
  style?: StyleProp<ViewStyle>;
}

const AppLink = (props: AppLinkProps) => {
  const {
    variant = "default",
    size = "default",
    buttonStyle,
    textStyle,
    text,
    icon,
    style,
    ...rest
  } = props;

  return (
    <View
      style={[
        styles.button,
        getButtonVariantStyle(variant),
        getButtonSizeStyle(size),
        buttonStyle,
      ]}
    >
      <Link {...rest}>
        <Text
          style={[
            styles.text,
            styles.buttonTextBase,
            getTextVariantStyle(variant),
            textStyle,
          ]}
        >
          {text}
        </Text>
        {icon && <View>{icon}</View>}
      </Link>
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: scale(28),
    minWidth: scale(159), // rounded-full
  },
  // Button Variants
  buttonDefault: {
    backgroundColor: COLORS.primary,
  },
  buttonDestructive: {
    backgroundColor: COLORS.error,
  },
  buttonOutline: {
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#D1D5DB",
  },
  buttonSecondary: {
    backgroundColor: COLORS.secondary,
  },
  buttonGhost: {
    backgroundColor: "transparent",
  },
  buttonLink: {
    backgroundColor: "transparent",
    minWidth: "auto",
    minHeight: "auto",
    padding: 0,
  },
  buttonIcon: {
    backgroundColor: "transparent",
    minWidth: "auto",
    minHeight: "auto",
    padding: scale(8),
  },
  // Button Sizes
  buttonDefaultSize: {
    paddingVertical: verticalScale(10),
    paddingHorizontal: scale(16),
  },
  buttonSm: {
    minWidth: "auto",
    minHeight: "auto",
    paddingVertical: verticalScale(10),
    paddingHorizontal: scale(16),
    borderRadius: scale(28),
  },
  buttonMd: {
    paddingVertical: verticalScale(12),
    paddingHorizontal: scale(20),
  },
  buttonLg: {
    paddingVertical: verticalScale(20),
    paddingHorizontal: scale(28),
  },
  buttonIconSize: {
    padding: scale(8),
  },
  buttonText: {
    padding: 0,
  },
  // Text Styles
  text: {
    textAlign: "center",
  },
  buttonTextBase: {
    fontSize: scale(16),
    fontWeight: "500",
  },
  textDefault: {
    color: "#FFFFFF",
  },
  textDestructive: {
    color: "#FFFFFF",
  },
  textOutline: {
    color: "#374151",
  },
  textSecondary: {
    color: "#FFFFFF",
  },
  textGhost: {
    color: COLORS.primary,
  },
  textLink: {
    color: COLORS.primary,
    textDecorationLine: "underline",
  },
  textIcon: {
    color: COLORS.primary,
  },
});

export { AppButton, AppLink };
