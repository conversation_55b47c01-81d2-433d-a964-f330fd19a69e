import React from 'react';
import { View, ViewStyle } from 'react-native';
import { scale, verticalScale } from '@/utils/styling-scale';

interface SimpleTabIndicatorProps {
  isActive: boolean;
  color?: string;
  size?: number;
}

export const SimpleTabIndicator: React.FC<SimpleTabIndicatorProps> = ({
  isActive,
  color = "#1B3142",
  size = scale(7)
}) => {
  if (!isActive) return <View style={{ height: size, marginTop: verticalScale(8) }} />;

  const indicatorStyle: ViewStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
    backgroundColor: color,
    marginTop: verticalScale(8),
    // Dodajemo shadow za bolji izgled
    shadowColor: color,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 4, // Za Android
  };

  return <View style={indicatorStyle} />;
};
