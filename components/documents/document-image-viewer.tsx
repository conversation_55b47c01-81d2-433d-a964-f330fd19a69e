import { Document } from "@/types/document";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import ImageViewing from "react-native-image-viewing";

interface DocumentImageViewerProps {
  visible: boolean;
  onRequestClose: () => void;
  documents: Document[];
  initialIndex?: number;
}

export const DocumentImageViewer: React.FC<DocumentImageViewerProps> = ({
  visible,
  onRequestClose,
  documents,
  initialIndex = 0,
}) => {
  // Transform documents to image viewing format
  const images = documents
    .filter((doc) => doc.fileS3Path) // Only include documents with valid image URLs
    .map((doc) => ({
      uri: doc.fileS3Path,
      // Add document name as a title for better UX
      title: doc.fileName,
    }));

  // If no valid images, don't render the viewer
  if (images.length === 0) {
    return null;
  }

  return (
    <ImageViewing
      images={images}
      imageIndex={initialIndex}
      visible={visible}
      onRequestClose={onRequestClose}
      // Configuration for better UX
      swipeToCloseEnabled={true}
      doubleTapToZoomEnabled={true}
      presentationStyle="overFullScreen"
      // Header configuration with close button
      HeaderComponent={({ imageIndex }) => {
        const currentDoc = documents[imageIndex];
        return (
          <View style={styles.headerContainer}>
            <View style={styles.headerContent}>
              <View style={styles.headerInfo}>
                <Text style={styles.headerTitle}>
                  {currentDoc?.fileName || "Document"}
                </Text>
                <Text style={styles.headerSubtitle}>
                  {currentDoc?.idDocDefIdDocType || "Unknown Type"}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onRequestClose}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="close" size={scale(24)} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        );
      }}
      // Footer with document info
      FooterComponent={({ imageIndex }) => {
        const currentDoc = documents[imageIndex];
        return (
          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>
              {imageIndex + 1} of {images.length}
            </Text>
            {currentDoc?.modificationDate && (
              <Text style={styles.footerSubtext}>
                Modified:{" "}
                {new Date(currentDoc.modificationDate).toLocaleDateString()}
              </Text>
            )}
          </View>
        );
      }}
    />
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    position: "absolute",
    top: verticalScale(60),
    left: scale(20),
    right: scale(20),
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: scale(8),
    padding: scale(12),
    zIndex: 1,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  headerInfo: {
    flex: 1,
    marginRight: scale(12),
  },
  headerTitle: {
    color: "white",
    fontSize: scale(16),
    fontWeight: "600",
    marginBottom: verticalScale(4),
  },
  headerSubtitle: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: scale(12),
  },
  closeButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: scale(16),
    padding: scale(4),
  },
  footerContainer: {
    position: "absolute",
    bottom: verticalScale(60),
    left: scale(20),
    right: scale(20),
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: scale(8),
    padding: scale(12),
  },
  footerText: {
    color: "white",
    fontSize: scale(14),
    fontWeight: "500",
    marginBottom: verticalScale(4),
  },
  footerSubtext: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: scale(12),
  },
});
