import { ICONS } from "@/constants/icons";
import { COLORS } from "@/constants/theme";
import {
  canPreviewDocument,
  DocumentType,
  getDocumentType,
} from "@/utils/document-utils";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";

export type DocumentStatus = "Updated" | "Expired" | "Expires Soon";

export interface DocumentItemProps {
  id: string;
  name: string;
  fileType: string;
  status?: DocumentStatus;
  thumbnailUrl?: string;
  document?: any; // Document object for type detection
  onPress?: () => void;
  onImagePress?: () => void;
  onPDFPress?: () => void;
}

const images = [
  "png",
  "jpg",
  "jpeg",
  "gif",
  "bmp",
  "webp",
  "svg",
  "tiff",
  "tif",
];

const DocumentItem = ({
  name,
  fileType,
  status,
  thumbnailUrl,
  document,
  onPress,
  onImagePress,
  onPDFPress,
}: DocumentItemProps) => {
  const getStatusColor = () => {
    switch (status) {
      case "Updated":
        return "#4CAF50"; // Green
      case "Expired":
        return "#F44336"; // Red
      case "Expires Soon":
        return "#FF9800"; // Orange
      default:
        return COLORS.mediumGray;
    }
  };

  const renderDocumentPreview = () => {
    // Check if it's an image document (should show thumbnail)
    const isImage =
      fileType.toLowerCase().includes("selfie") ||
      fileType.toLowerCase().includes("photo") ||
      fileType.toLowerCase().includes("image") ||
      images.some((ext) => name.toLowerCase().includes(ext)) ||
      name.toLowerCase().includes("photo");

    // If it's an image and has thumbnail, show thumbnail
    if (isImage && thumbnailUrl) {
      return (
        <View style={styles.thumbnailContainer}>
          <Image
            source={{ uri: thumbnailUrl }}
            style={styles.thumbnail}
            contentFit="cover"
            placeholder={ICONS.DOCUMENT_ICON}
            transition={200}
          />
        </View>
      );
    }

    // For all other documents (PDF, attachments, etc.), show icon
    const isPDF = name.toLowerCase().endsWith(".pdf");

    let iconName = "document-outline"; // default
    if (isPDF) {
      iconName = "document-text-outline";
    } else if (isImage) {
      iconName = "image-outline";
    }

    // Debug log
    console.log(
      "📄 Document:",
      name,
      "isPDF:",
      isPDF,
      "isImage:",
      isImage,
      "hasThumbnail:",
      !!thumbnailUrl,
      "Icon:",
      iconName
    );

    return (
      <View style={styles.iconContainer}>
        <View style={styles.iconBackground}>
          <Ionicons
            name={iconName as any}
            size={scale(28)}
            color={COLORS.primary}
          />
        </View>
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {renderDocumentPreview()}

        <View style={styles.details}>
          <Text style={styles.title} numberOfLines={1}>
            {name}
          </Text>
          <Text style={styles.fileType}>{fileType}</Text>
        </View>

        <View style={styles.statusContainer}>
          <Text style={[styles.status, { color: getStatusColor() }]}>
            {status}
          </Text>
        </View>
      </View>

      <View style={styles.actions}>
        {document && canPreviewDocument(document) && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              const documentType = getDocumentType(document);
              if (documentType === DocumentType.PDF && onPDFPress) {
                onPDFPress();
              } else if (documentType === DocumentType.IMAGE && onImagePress) {
                onImagePress();
              }
            }}
            hitSlop={{ top: 10, bottom: 10, left: 5, right: 10 }}
          >
            <Ionicons name="eye" size={scale(20)} color={COLORS.mediumGray} />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: verticalScale(12),
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  content: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  iconContainer: {
    width: scale(48),
    height: scale(48),
    justifyContent: "center",
    alignItems: "center",
    marginRight: scale(12),
  },
  iconBackground: {
    width: scale(40),
    height: scale(40),
    backgroundColor: COLORS.grayLight,
    borderRadius: scale(8),
    justifyContent: "center",
    alignItems: "center",
  },
  docIcon: {
    width: scale(21),
    height: scale(21),
  },
  thumbnailContainer: {
    width: scale(48),
    height: scale(48),
    borderRadius: scale(8),
    marginRight: scale(12),
    position: "relative",
    overflow: "hidden",
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    borderRadius: scale(8),
  },
  pdfIndicator: {
    backgroundColor: COLORS.grayLight,
    borderRadius: scale(8),
    padding: scale(2),
    marginTop: scale(2),
    alignSelf: "flex-end",
    width: scale(24),
    height: scale(24),
    justifyContent: "center",
    alignItems: "center",
  },
  details: {
    flex: 1,
    justifyContent: "center",
  },
  title: {
    fontSize: scale(14),
    fontWeight: "600",
    color: "#1B3142",
    marginBottom: verticalScale(2),
  },
  fileType: {
    fontSize: scale(12),
    color: COLORS.mediumGray,
  },
  statusContainer: {
    alignItems: "flex-end",
    marginRight: scale(8),
    minWidth: scale(80),
  },
  status: {
    fontSize: scale(12),
    fontWeight: "500",
    marginBottom: verticalScale(2),
  },
  date: {
    fontSize: scale(10),
    color: COLORS.mediumGray,
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: scale(2),
    marginLeft: scale(2),
  },
});

export default DocumentItem;
