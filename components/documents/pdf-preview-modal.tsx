import { COLORS } from "@/constants/theme";
import { Document } from "@/types/document";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Linking,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Modal from "react-native-modal";
import { SafeAreaView } from "react-native-safe-area-context";
import { WebView } from "react-native-webview";

interface PDFPreviewModalProps {
  isVisible: boolean;
  onClose: () => void;
  document: Document | null;
}

export const PDFPreviewModal: React.FC<PDFPreviewModalProps> = ({
  isVisible,
  onClose,
  document,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Effect to handle modal visibility and cleanup
  useEffect(() => {
    if (isVisible && document) {
      // Reset state when modal opens
      setIsLoading(true);
      setError(null);
    }
  }, [isVisible, document]);

  // WebView event handlers
  const handleWebViewLoadStart = () => {
    setIsLoading(true);
    setError(null);
  };

  const handleWebViewLoadEnd = () => {
    setIsLoading(false);
  };

  const handleWebViewError = (syntheticEvent: any) => {
    const { nativeEvent } = syntheticEvent;
    console.error("WebView error:", nativeEvent);
    setIsLoading(false);
    setError("Unable to load PDF. Please try downloading it instead.");
  };

  const handleDownload = async () => {
    if (document?.fileS3Path) {
      Alert.alert(
        "Download PDF",
        "This will open the PDF in your browser for download.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Open",
            onPress: async () => {
              try {
                const supported = await Linking.canOpenURL(document.fileS3Path);
                if (supported) {
                  await Linking.openURL(document.fileS3Path);
                } else {
                  Alert.alert("Error", "Cannot open this PDF file");
                }
              } catch (error) {
                console.error("Error opening PDF:", error);
                Alert.alert("Error", "Failed to open PDF file");
              }
            },
          },
        ]
      );
    }
  };

  if (!document) return null;

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      onBackButtonPress={onClose}
      style={styles.modal}
      propagateSwipe={true}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.headerInfo}>
              <Text style={styles.headerTitle} numberOfLines={1}>
                {document.fileName}
              </Text>
              <Text style={styles.headerSubtitle}>
                {document.idDocDefIdDocType || "PDF Document"}
              </Text>
            </View>
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleDownload}
                activeOpacity={0.7}
              >
                <Ionicons
                  name="download-outline"
                  size={scale(20)}
                  color={COLORS.primary}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
                activeOpacity={0.7}
              >
                <Ionicons
                  name="close"
                  size={scale(20)}
                  color={COLORS.primary}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {error ? (
            <View style={styles.errorContainer}>
              <Ionicons
                name="alert-circle-outline"
                size={scale(48)}
                color={COLORS.error}
                style={styles.errorIcon}
              />
              <Text style={styles.errorTitle}>Unable to load PDF</Text>
              <Text style={styles.errorMessage}>{error}</Text>
            </View>
          ) : (
            <>
              {isLoading && (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={COLORS.primary} />
                  <Text style={styles.loadingText}>Loading PDF...</Text>
                </View>
              )}
              <SafeAreaView style={{ flex: 1 }}>
                <View style={styles.pdfContainer}>
                  <WebView
                    source={{
                      uri: `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(
                        document.fileS3Path
                      )}`,
                    }}
                    cacheEnabled={true}
                    onLoadStart={handleWebViewLoadStart}
                    onLoadEnd={handleWebViewLoadEnd}
                    onError={handleWebViewError}
                    // javaScriptEnabled={true}
                    // domStorageEnabled={true}
                    // startInLoadingState={true}
                    // mixedContentMode="compatibility"
                    // allowsInlineMediaPlayback={true}
                    // mediaPlaybackRequiresUserAction={false}
                  />
                </View>
              </SafeAreaView>
            </>
          )}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.footerContent}>
            <Text style={styles.footerText}>
              Modified:{" "}
              {new Date(document.modificationDate).toLocaleDateString()}
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
    justifyContent: "flex-end",
  },
  container: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: scale(24),
    borderTopRightRadius: scale(24),
    height: "90%",
    maxHeight: "90%",
  },
  pdfContainer: {
    flex: 1,
    padding: scale(16),
    backgroundColor: COLORS.grayLight,
  },

  header: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.grayLight,
    paddingHorizontal: scale(20),
    paddingVertical: verticalScale(16),
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  headerInfo: {
    flex: 1,
    marginRight: scale(12),
  },
  headerTitle: {
    fontSize: scale(18),
    fontWeight: "600",
    color: COLORS.primary,
    marginBottom: verticalScale(4),
  },
  headerSubtitle: {
    fontSize: scale(14),
    color: COLORS.mediumGray,
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: scale(12),
  },
  actionButton: {
    backgroundColor: COLORS.grayLight,
    borderRadius: scale(22),
    padding: scale(12),
    minWidth: scale(44), // Minimum 44pt touch target
    minHeight: scale(44),
    alignItems: "center",
    justifyContent: "center",
    // Enhanced shadow for better visual feedback
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },

  closeButton: {
    backgroundColor: COLORS.grayLight,
    borderRadius: scale(22),
    padding: scale(12),
    minWidth: scale(44), // Minimum 44pt touch target
    minHeight: scale(44),
    alignItems: "center",
    justifyContent: "center",
    // Enhanced shadow
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  content: {
    flex: 1,
    position: "relative",
  },
  webview: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderRadius: scale(12),
  },
  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.background,
    zIndex: 1,
  },
  loadingText: {
    marginTop: verticalScale(16),
    fontSize: scale(16),
    color: COLORS.mediumGray,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: scale(32),
  },
  errorIcon: {
    marginBottom: verticalScale(16),
  },
  errorTitle: {
    fontSize: scale(18),
    fontWeight: "600",
    color: COLORS.error,
    marginBottom: verticalScale(8),
    textAlign: "center",
  },
  errorMessage: {
    fontSize: scale(14),
    color: COLORS.mediumGray,
    textAlign: "center",
    lineHeight: verticalScale(20),
    marginBottom: verticalScale(24),
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: scale(24),
    paddingVertical: verticalScale(12),
    borderRadius: scale(24),
  },
  retryButtonText: {
    color: COLORS.background,
    fontSize: scale(14),
    fontWeight: "600",
  },
  retryActionsContainer: {
    marginTop: verticalScale(16),
    paddingHorizontal: scale(16),
  },
  retryActionText: {
    fontSize: scale(12),
    color: COLORS.mediumGray,
    marginBottom: verticalScale(4),
    textAlign: "center",
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: COLORS.grayLight,
    paddingHorizontal: scale(20),
    paddingVertical: verticalScale(12),
  },
  footerContent: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
  },
  footerText: {
    fontSize: scale(12),
    color: COLORS.mediumGray,
  },
});
