import { ICONS } from "@/constants/icons";
import { scale } from "@/utils/styling-scale";
import { Image } from "expo-image";
import { router } from "expo-router";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { AppText } from "../ui/app-text";

interface AppHeaderProps {
  title: string;
  grayColor?: boolean;
  hideBackButton?: boolean;
  onBackPress?: () => void;
}

export default function AppHeader({
  title,
  grayColor,
  onBackPress,
  hideBackButton,
}: AppHeaderProps) {
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };
  return (
    <View style={styles.header}>
      <View style={styles.container}>
        {!hideBackButton && (
          <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
            <Image
              source={ICONS.ARROW_LEFT_ICON}
              style={styles.backButtonIcon}
              contentFit="contain"
              transition={200}
              cachePolicy="memory-disk"
            />
          </TouchableOpacity>
        )}
        <AppText
          variant={grayColor ? "secondary" : "primary"}
          weight="semibold"
          style={styles.headerTitle}
          size={grayColor ? "xl" : "2xl"}
        >
          {title}
        </AppText>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    width: "100%",
    paddingVertical: scale(5),
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
    width: "100%",
  },
  headerTitle: {
    textAlign: "center",
  },
  backButton: {
    position: "absolute",
    left: scale(0),
    zIndex: 1,
  },
  backButtonIcon: {
    width: scale(18),
    height: scale(18),
  },
});
