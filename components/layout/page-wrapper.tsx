import AppHeader from "@/components/layout/app-header";
import { COLORS } from "@/constants/theme";
import { verticalScale } from "@/utils/styling-scale";
import { StyleSheet, View } from "react-native";

interface PageWrapperProps {
  children: React.ReactNode;
  title: string;
  onBackPress?: () => void;
  grayColor?: boolean;
  hideBackButton?: boolean;
}

const PageWrapper = ({
  children,
  title,
  onBackPress,
  grayColor,
  hideBackButton,
}: PageWrapperProps) => {
  return (
    <View style={styles.container}>
      <AppHeader
        title={title}
        onBackPress={onBackPress}
        grayColor={grayColor}
        hideBackButton={hideBackButton}
      />
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: verticalScale(60),
    backgroundColor: COLORS.background,
  },
});

export default PageWrapper;
