import { ICONS } from "@/constants/icons";
import { COLORS } from "@/constants/theme";
import { useCurrentUser } from "@/services/user/user-hooks";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { useRouter } from "expo-router";
import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import TierBadge from "../companies/tier-badge";
interface UserHeaderProps {
  onPressAvatar?: () => void;
  onPressSearch?: () => void;
  onPressNotification?: () => void;
}

const UserHeader = ({
  onPressAvatar,
  onPressNotification,
}: UserHeaderProps) => {
  const { data: user, isLoading } = useCurrentUser();
  const router = useRouter();

  if (isLoading) {
    return <ActivityIndicator />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.userInfo}>
        <TouchableOpacity
          style={styles.avatarContainer}
          onPress={() => {
            if (onPressAvatar) {
              onPressAvatar();
            } else {
              router.push("/(app)/(tabs)/profile");
            }
          }}
          activeOpacity={0.8}
        >
          <View style={styles.avatar}>
            <Image
              source={ICONS.PROFILE_ICON}
              style={styles.avatarImage}
              contentFit="contain"
              cachePolicy="memory-disk"
            />
          </View>
        </TouchableOpacity>
        <View style={styles.textContainer}>
          <Text style={styles.userName} numberOfLines={1} ellipsizeMode="tail">
            {user?.name}
          </Text>
          <Text style={styles.userEmail} numberOfLines={1} ellipsizeMode="tail">
            {user?.email}
          </Text>
        </View>
      </View>

      <View style={styles.actions}>
        <TierBadge tier={user?.tierAsNumber ?? 0} />

        <TouchableOpacity
          style={styles.iconButton}
          onPress={() => {
            if (onPressNotification) {
              onPressNotification();
            } else {
              router.push("/(app)/(tabs)/(home)/notifications");
            }
          }}
          activeOpacity={0.8}
        >
          <Ionicons
            name="notifications-outline"
            size={scale(24)}
            color={COLORS.mediumGray}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  avatarContainer: {
    marginRight: scale(12),
  },
  avatar: {
    width: scale(35),
    height: scale(35),
    borderRadius: scale(25),
    backgroundColor: COLORS.background,
    borderWidth: 1,

    borderColor: COLORS.primaryLight,
    justifyContent: "center",
    alignItems: "center",
  },
  textContainer: {
    justifyContent: "center",
    flex: 1,
  },
  userName: {
    fontSize: scale(18),
    fontWeight: "600",
    color: "#1B3142",
    marginBottom: verticalScale(4),
  },
  userEmail: {
    fontSize: scale(14),
    color: "#89929B",
  },
  actions: {
    flexDirection: "row",
  },
  iconButton: {
    marginLeft: scale(5),
  },
  avatarImage: {
    width: scale(24),
    height: scale(24),
  },
});

export default UserHeader;
