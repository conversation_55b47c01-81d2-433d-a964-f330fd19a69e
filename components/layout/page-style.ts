import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { StyleSheet } from "react-native";

export const pageStyle = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    paddingBottom: verticalScale(60),
    paddingHorizontal: scale(10),
  },
  listContainer: {
    paddingHorizontal: scale(10),
    backgroundColor: COLORS.background,
  },
  headerContainer: {
    gap: verticalScale(6),
  },
  searchContainer: {
    marginTop: verticalScale(15),
    marginBottom: verticalScale(20),
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: scale(20),
  },
  emptyText: {
    fontSize: scale(16),
    color: COLORS.mediumGray,
  },
});
