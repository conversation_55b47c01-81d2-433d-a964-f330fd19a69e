import { AppButton } from "@/components/ui/app-button";
import { COLORS } from "@/constants/theme";
import { Company } from "@/types/company";
import { Document } from "@/types/document";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import React from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";
import TierBadge from "./tier-badge";

interface ConsentCompanyItemProps {
  company: Company;
  documentCount?: number;
  isExpanded?: boolean;
  documentsForTier?: Document[];
  onPress?: () => void;
  onRevokePress?: () => void;
  onDetailsPress?: () => void;
}

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

const getDocumentDisplayName = (document: Document) => {
  const typeMap: { [key: string]: string } = {
    ID_CARD: "ID Card",
    PASSPORT: "Passport",
    DRIVERS_LICENSE: "Driver's License",
    SELFIE: "Selfie",
    PROOF_OF_RESIDENCE: "Proof of Residence",
    BANK_STATEMENT: "Bank Statement",
    UTILITY_BILL: "Utility Bill",
    INCOME_STATEMENT: "Income Statement",
    FILE_ATTACHMENT: "File Attachment",
  };

  return typeMap[document.idDocDefIdDocType] || document.fileName || "Document";
};

const getDocumentIcon = (document: Document) => {
  const iconMap: { [key: string]: keyof typeof Ionicons.glyphMap } = {
    ID_CARD: "card-outline",
    PASSPORT: "airplane-outline",
    DRIVERS_LICENSE: "car-outline",
    SELFIE: "camera-outline",
    PROOF_OF_RESIDENCE: "home-outline",
    BANK_STATEMENT: "card-outline",
    UTILITY_BILL: "receipt-outline",
    INCOME_STATEMENT: "document-text-outline",
    FILE_ATTACHMENT: "attach-outline",
  };

  return iconMap[document.idDocDefIdDocType] || "document-outline";
};

const getDocumentIconColor = (document: Document) => {
  const colorMap: { [key: string]: string } = {
    ID_CARD: COLORS.primary,
    PASSPORT: "#4A90E2",
    DRIVERS_LICENSE: "#F5A623",
    SELFIE: "#7ED321",
    PROOF_OF_RESIDENCE: "#9013FE",
    BANK_STATEMENT: "#FF6B6B",
    UTILITY_BILL: "#50E3C2",
    INCOME_STATEMENT: "#BD10E0",
    FILE_ATTACHMENT: "#FF9500",
  };

  return colorMap[document.idDocDefIdDocType] || COLORS.primary;
};

const DocumentItem = ({ document }: { document: Document }) => (
  console.log(document),
  (
    <View style={styles.documentItem}>
      <View style={styles.documentIcon}>
        {document.idDocDefIdDocType === "SELFIE" ? (
          <Image
            source={{ uri: document.fileS3PathThumbnail }}
            style={styles.documentThumbnail}
            contentFit="cover"
          />
        ) : (
          <View style={styles.documentIconContainer}>
            <Ionicons
              name={getDocumentIcon(document)}
              size={18}
              color={getDocumentIconColor(document)}
            />
          </View>
        )}
      </View>

      <View style={styles.documentInfo}>
        <Text style={styles.documentName} numberOfLines={1}>
          {getDocumentDisplayName(document)}
        </Text>
        <Text style={styles.documentCountry} numberOfLines={1}>
          {document.idDocDefCountry || "Document"}
        </Text>
      </View>

      <View style={styles.documentStatus}>
        <Text style={styles.statusText}>VALID</Text>
      </View>
    </View>
  )
);

export const ConsentCompanyItem = ({
  company,
  documentCount = 0,
  isExpanded = false,
  documentsForTier = [],
  onPress,
  onRevokePress,
  onDetailsPress,
}: ConsentCompanyItemProps) => {
  const tierLevel =
    company.required_individual_tier || company.required_corporate_tier || 1;

  return (
    <Pressable style={styles.container} onPress={onPress}>
      <View style={styles.content}>
        <View style={styles.leftSection}>
          <View style={styles.logoContainer}>
            {company.s3LogoUrl || company.s3ImageUrl ? (
              <Image
                source={{ uri: company.s3LogoUrl || company.s3ImageUrl }}
                style={styles.logo}
                contentFit="contain"
              />
            ) : (
              <View style={styles.logoPlaceholder}>
                <Text style={styles.logoPlaceholderText}>
                  {getInitials(company.name)}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.infoContainer}>
            <Text style={styles.companyName} numberOfLines={1}>
              {company.name}
            </Text>
            <View style={styles.metaInfo}>
              <View style={styles.documentsInfo}>
                <Ionicons
                  name="document-text-outline"
                  size={scale(14)}
                  color={COLORS.mediumGray}
                />
                <Text style={styles.documentsText}>
                  {documentCount} Documents
                </Text>
              </View>
              <View style={styles.statusBadge}>
                <View style={styles.statusDot} />
                <Text style={styles.activeStatusText}>Active</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.rightSection}>
          <TierBadge tier={tierLevel} />
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actions}>
        <AppButton
          text={isExpanded ? "Hide Documents" : "View Documents"}
          variant="ghost"
          size="sm"
          onPress={onDetailsPress}
          buttonStyle={styles.detailsButton}
          textStyle={styles.detailsButtonText}
        />

        <AppButton
          text="Revoke Access"
          variant="ghost"
          size="sm"
          onPress={onRevokePress}
          buttonStyle={styles.revokeButton}
          textStyle={styles.revokeButtonText}
        />
      </View>

      {/* Expanded Documents List */}
      {isExpanded && (
        <View style={styles.documentsContainer}>
          <View style={styles.documentsHeader}>
            <Text style={styles.documentsHeaderText}>
              Documents shared (
              {tierLevel === 1 ? "Basic" : tierLevel === 2 ? "Pro" : "Premium"}{" "}
              tier)
            </Text>
          </View>

          {documentsForTier.length > 0 ? (
            <View style={styles.documentsList}>
              {documentsForTier.map((document, index) => (
                <DocumentItem
                  key={`${document.fileName}-${index}`}
                  document={document}
                />
              ))}
            </View>
          ) : (
            <View style={styles.emptyDocuments}>
              <Text style={styles.emptyText}>
                No documents available for this tier
              </Text>
            </View>
          )}
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: scale(16),
    marginHorizontal: scale(16),
    marginBottom: verticalScale(12),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    overflow: "hidden",
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: scale(16),
  },
  leftSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  logoContainer: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(12),
    backgroundColor: "transparent",
    overflow: "hidden",
    marginRight: scale(12),
  },
  logo: {
    width: "100%",
    height: "100%",
  },
  logoPlaceholder: {
    width: "100%",
    height: "100%",
    backgroundColor: COLORS.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  logoPlaceholderText: {
    fontSize: scale(18),
    fontWeight: "600",
    color: "white",
  },
  infoContainer: {
    flex: 1,
    marginRight: scale(8),
  },
  companyName: {
    fontSize: scale(16),
    fontWeight: "600",
    color: COLORS.secondary,
    marginBottom: verticalScale(4),
  },
  metaInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  documentsInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: scale(12),
  },
  documentsText: {
    fontSize: scale(13),
    color: COLORS.mediumGray,
    marginLeft: scale(4),
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: scale(6),
    height: scale(6),
    borderRadius: scale(3),
    backgroundColor: "#10B981",
    marginRight: scale(4),
  },
  activeStatusText: {
    fontSize: scale(13),
    color: "#10B981",
    fontWeight: "500",
  },
  rightSection: {
    position: "absolute",
    top: scale(10),
    right: scale(10),
  },
  chevron: {
    marginTop: verticalScale(8),
  },
  actions: {
    flexDirection: "row",
    borderTopWidth: 1,
    borderTopColor: COLORS.grayLight,
    paddingVertical: verticalScale(8),
  },
  detailsButton: {
    flex: 1,
    minWidth: undefined,
    minHeight: undefined,
    paddingVertical: verticalScale(8),
    marginRight: scale(4),
  },
  revokeButton: {
    flex: 1,
    minWidth: undefined,
    minHeight: undefined,
    paddingVertical: verticalScale(8),
    marginLeft: scale(4),
  },
  detailsButtonText: {
    fontSize: scale(14),
    color: COLORS.primary,
    fontWeight: "500",
  },
  revokeButtonText: {
    fontSize: scale(14),
    color: COLORS.error,
    fontWeight: "500",
  },
  documentsContainer: {
    borderTopWidth: 1,
    borderTopColor: COLORS.grayLight,
    paddingHorizontal: scale(16),
    paddingBottom: scale(16),
  },
  documentsHeader: {
    paddingVertical: scale(12),
  },
  documentsHeaderText: {
    fontSize: scale(14),
    fontWeight: "600",
    color: COLORS.secondary,
  },
  documentsList: {
    gap: scale(8),
  },
  documentItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: scale(8),
    paddingHorizontal: scale(12),
    backgroundColor: COLORS.grayLight + "30",
    borderRadius: scale(8),
  },
  documentIcon: {
    width: scale(32),
    height: scale(32),
    borderRadius: scale(6),
    backgroundColor: COLORS.background,
    alignItems: "center",
    justifyContent: "center",
    marginRight: scale(10),
    overflow: "hidden",
  },
  documentThumbnail: {
    width: "100%",
    height: "100%",
  },
  documentIconContainer: {
    width: "100%",
    height: "100%",
    backgroundColor: COLORS.background,
    alignItems: "center",
    justifyContent: "center",
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: scale(14),
    fontWeight: "500",
    color: COLORS.secondary,
  },
  documentCountry: {
    fontSize: scale(12),
    color: COLORS.mediumGray,
    marginTop: scale(2),
  },
  documentStatus: {
    alignItems: "flex-end",
  },
  statusText: {
    fontSize: scale(12),
    color: "#10B981",
    fontWeight: "600",
  },
  emptyDocuments: {
    paddingVertical: scale(20),
    alignItems: "center",
  },
  emptyText: {
    fontSize: scale(14),
    color: COLORS.mediumGray,
    textAlign: "center",
    fontStyle: "italic",
  },
});
