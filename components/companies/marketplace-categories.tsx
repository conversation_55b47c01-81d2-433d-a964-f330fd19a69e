import {
  MARKETPLACE_CATEGORIES,
  MarketplaceCategory,
} from "@/constants/marketplace-icons";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Image } from "expo-image";
import React from "react";
import { FlatList, Pressable, StyleSheet, Text, View } from "react-native";

interface MarketplaceCategoriesProps {
  onCategoryPress?: (category: MarketplaceCategory) => void;
}

export default function MarketplaceCategories({
  onCategoryPress,
}: MarketplaceCategoriesProps) {
  const renderItem = ({ item }: { item: MarketplaceCategory }) => (
    <Pressable
      style={styles.categoryCard}
      onPress={() => onCategoryPress?.(item)}
    >
      <View style={styles.categoryContent}>
        <Image
          source={item.icon}
          style={styles.categoryIcon}
          contentFit="contain"
        />
        <Text style={styles.categoryTitle} numberOfLines={2}>
          {item.title}
        </Text>
      </View>
    </Pressable>
  );

  return (
    <FlatList
      data={MARKETPLACE_CATEGORIES}
      renderItem={renderItem}
      numColumns={3}
      keyExtractor={(item) => item.id}
      scrollEnabled={false} // Disable scroll since it's part of parent list
      columnWrapperStyle={styles.row} // Style for each row
      showsVerticalScrollIndicator={false}
    />
  );
}

const styles = StyleSheet.create({
  row: {
    justifyContent: "space-between",
    marginBottom: verticalScale(12),
  },
  categoryCard: {
    backgroundColor: "rgba(227, 227, 227, 0.1)",
    borderWidth: 1,
    borderColor: COLORS.grayLight,
    borderRadius: scale(12),
    width: "31%", // (100% - 2 gaps) / 3 columns
    aspectRatio: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: scale(8),
  },
  categoryContent: {
    alignItems: "center",
    justifyContent: "center",
  },
  categoryIcon: {
    width: scale(30),
    height: scale(30),
  },
  categoryTitle: {
    fontWeight: "500",
    color: COLORS.secondary,
    textAlign: "center",
    marginTop: verticalScale(4),
    fontSize: scale(12),
  },
});
