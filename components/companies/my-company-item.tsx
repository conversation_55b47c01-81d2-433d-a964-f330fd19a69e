import { COLORS } from "@/constants/theme";
import { Company } from "@/types/company";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { Pressable, StyleSheet, Text, View } from "react-native";
import { AppButton } from "../ui/app-button";
import TierBadge from "./tier-badge";

interface MyCompanyItemProps extends Company {
  documentsShared: number;
  onPressDetails: () => void;
  from: "my-companies" | "marketplace";
  onPressRevoke: () => void;
}

const getInitials = (name: string) => {
  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

export default function MyCompanyItem({
  name,
  s3LogoUrl,
  required_individual_tier,
  documentsShared,
  onPressDetails,
  onPressRevoke,
}: MyCompanyItemProps) {
  return (
    <Pressable style={styles.container} onPress={onPressDetails}>
      <View style={styles.content}>
        <View style={styles.leftSection}>
          <View style={styles.logoContainer}>
            {s3LogoUrl ? (
              <Image
                source={{ uri: s3LogoUrl }}
                style={styles.logo}
                contentFit="contain"
              />
            ) : (
              <View style={styles.logoPlaceholder}>
                <Text style={styles.logoPlaceholderText}>
                  {getInitials(name)}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.infoContainer}>
            <Text style={styles.companyName} numberOfLines={1}>
              {name}
            </Text>
            <View style={styles.metaInfo}>
              <View style={styles.documentsInfo}>
                <Ionicons
                  name="document-text-outline"
                  size={scale(14)}
                  color={COLORS.mediumGray}
                />
                <Text style={styles.documentsText}>
                  {documentsShared} Documents
                </Text>
              </View>
              <View style={styles.statusBadge}>
                <View style={styles.statusDot} />
                <Text style={styles.statusText}>Active</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.rightSection}>
          <TierBadge tier={required_individual_tier} />
          <Ionicons
            name="chevron-forward"
            size={scale(20)}
            color={COLORS.mediumGray}
            style={styles.chevron}
          />
        </View>
      </View>

      <View style={styles.actions}>
        <AppButton
          text="View Details"
          variant="ghost"
          size="sm"
          onPress={onPressDetails}
          buttonStyle={styles.detailsButton}
          textStyle={styles.detailsButtonText}
        />

        <AppButton
          text="Revoke Access"
          variant="ghost"
          size="sm"
          onPress={onPressRevoke}
          buttonStyle={styles.revokeButton}
          textStyle={styles.revokeButtonText}
        />
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: scale(16),
    marginHorizontal: scale(16),
    marginBottom: verticalScale(12),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    overflow: "hidden",
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: scale(16),
  },
  leftSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  logoContainer: {
    width: scale(56),
    height: scale(56),
    borderRadius: scale(12),
    backgroundColor: "transparent",
    overflow: "hidden",
    marginRight: scale(12),
  },
  logo: {
    width: "100%",
    height: "100%",
  },
  logoPlaceholder: {
    width: "100%",
    height: "100%",
    backgroundColor: COLORS.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  logoPlaceholderText: {
    fontSize: scale(18),
    fontWeight: "600",
    color: "white",
  },
  infoContainer: {
    flex: 1,
    marginRight: scale(8),
  },
  companyName: {
    fontSize: scale(16),
    fontWeight: "600",
    color: COLORS.secondary,
    marginBottom: verticalScale(4),
  },
  metaInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  documentsInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: scale(12),
  },
  documentsText: {
    fontSize: scale(13),
    color: COLORS.mediumGray,
    marginLeft: scale(4),
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: scale(6),
    height: scale(6),
    borderRadius: scale(3),
    backgroundColor: "#10B981",
    marginRight: scale(4),
  },
  statusText: {
    fontSize: scale(13),
    color: "#10B981",
    fontWeight: "500",
  },
  rightSection: {
    alignItems: "flex-end",
  },
  chevron: {
    marginTop: verticalScale(8),
  },
  actions: {
    flexDirection: "row",
    borderTopWidth: 1,
    borderTopColor: COLORS.grayLight,
    paddingVertical: verticalScale(8),
    paddingHorizontal: scale(16),
  },
  detailsButton: {
    flex: 1,
    minWidth: undefined,
    minHeight: undefined,
    paddingVertical: verticalScale(8),
    marginRight: scale(4),
  },
  revokeButton: {
    flex: 1,
    minWidth: undefined,
    minHeight: undefined,
    paddingVertical: verticalScale(8),
    marginLeft: scale(4),
  },
  detailsButtonText: {
    fontSize: scale(14),
    color: COLORS.primary,
    fontWeight: "500",
  },
  revokeButtonText: {
    fontSize: scale(14),
    color: COLORS.error,
    fontWeight: "500",
  },
});
