import { scale, verticalScale } from "@/utils/styling-scale";
import { getTierBadgeProps } from "@/utils/tier-number";
import { StyleSheet, Text, View } from "react-native";

export default function TierBadge({ tier }: { tier?: number }) {
  const { displayName } = getTierBadgeProps(tier);

  return (
    <View style={styles.tierBadge}>
      <Text style={styles.tierText}>{displayName}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  tierBadge: {
    paddingVertical: verticalScale(4),
    paddingHorizontal: scale(12),
    borderRadius: scale(16),
    borderWidth: 1,
    backgroundColor: "#F3F4F6",
    borderColor: "#E5E7EB",
    alignItems: "center",
    justifyContent: "center",
  },
  tierText: {
    fontSize: scale(12),
    fontWeight: "500",
    color: "#6B7280",
  },
});
