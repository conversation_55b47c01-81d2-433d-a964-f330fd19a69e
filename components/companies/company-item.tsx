import { COLORS } from "@/constants/theme";
import { RegistrationTypeOption } from "@/types/auth";
import { Company } from "@/types/company";
import { scale, verticalScale } from "@/utils/styling-scale";
import Ionicons from "@expo/vector-icons/Ionicons";
import { Image } from "expo-image";
import { useRouter } from "expo-router";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import TierBadge from "./tier-badge";

export interface CompanyItemProps extends Company {
  userType: RegistrationTypeOption;
  userTier: number;
}

const CompanyItem = ({
  name,
  s3LogoUrl,
  userType,
  required_corporate_tier,
  required_individual_tier,
  userTier,
  company_id,
}: CompanyItemProps) => {
  const router = useRouter();
  const isUserCorporate = userType === RegistrationTypeOption.KYB;
  const companyTier = isUserCorporate
    ? required_corporate_tier
    : required_individual_tier;
  const isLocked = userTier < companyTier;

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handlePress = () => {
    router.push(`/(app)/company/${company_id}?from=marketplace`);
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: isLocked ? COLORS.grayLight : COLORS.background,
        },
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.topContent}>
        <View style={styles.companyInfo}>
          <View style={styles.logoContainer}>
            <Image
              source={{ uri: s3LogoUrl }}
              style={styles.logo}
              contentFit="contain"
              cachePolicy="memory-disk"
              transition={200}
              contentPosition="center"
              onError={() => (
                <View style={styles.placeholderLogo}>
                  <Text style={styles.placeholderText}>
                    {getInitials(name)}
                  </Text>
                </View>
              )}
            />
          </View>
          <Text style={styles.companyName} numberOfLines={1}>
            {name}
          </Text>
        </View>
        {isLocked && (
          <View style={styles.lockContainer}>
            <Ionicons
              name="lock-closed-outline"
              size={18}
              color={COLORS.gray}
            />
          </View>
        )}
        <TierBadge tier={companyTier} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: scale(16),
    paddingVertical: verticalScale(10),
    paddingHorizontal: scale(10),
    marginBottom: verticalScale(12),
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: COLORS.primaryLight,
  },
  topContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  companyInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    marginRight: scale(12),
  },
  logoContainer: {
    width: scale(50),
    height: scale(40),
    marginRight: scale(12),
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
  },
  logo: {
    width: "100%",
    height: "100%",
  },
  placeholderLogo: {
    width: "100%",
    height: "100%",
    backgroundColor: COLORS.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  placeholderText: {
    color: "white",
    fontSize: scale(14),
    fontWeight: "bold",
  },
  companyName: {
    fontSize: scale(16),
    fontWeight: "600",
    color: "#1B3142",
    flex: 1,
  },

  infoRow: {
    marginBottom: verticalScale(12),
  },
  infoText: {
    fontSize: scale(14),
    color: "#6B7280",
  },

  button: {
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: scale(16),
    paddingVertical: verticalScale(6),
    paddingHorizontal: scale(16),
    marginRight: scale(8),
    backgroundColor: "#FFFFFF",
  },
  revokeButton: {
    borderColor: "#ffebee",
    backgroundColor: "#ffebee",
  },
  buttonText: {
    fontSize: scale(14),
    fontWeight: "500",
    color: "#1B3142",
  },
  revokeButtonText: {
    fontSize: scale(14),
    fontWeight: "500",
    color: "#F44336",
  },
  statusText: {
    fontSize: scale(14),
    fontWeight: "500",
    marginLeft: "auto",
  },
  lockContainer: {
    backgroundColor: COLORS.grayLight,
    padding: scale(4),
  },
});

export default CompanyItem;
