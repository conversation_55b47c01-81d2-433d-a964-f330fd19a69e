import React, { useState } from "react";
import { View, StyleSheet, Alert } from "react-native";
import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { useSendNotification } from "@/services/notification/interview-notification-hooks";
import { useCurrentUser } from "@/services/user/user-hooks";
import { useSignalRNotifications } from "@/hooks/useSignalRNotifications";
import { useSession } from "@/providers/auth/auth-provider";

/**
 * Debug component for testing interview notifications
 * This component allows developers to trigger test notifications
 * to verify the notification system is working correctly
 */
export const InterviewNotificationTest = () => {
  const { data: user } = useCurrentUser();
  const { token } = useSession();
  const sendNotificationMutation = useSendNotification();
  const [testMessage] = useState(
    "You have been invited for an interview verification. Please complete the interview process to continue."
  );

  // Get SignalR connection status for debugging
  const isAuthenticated = !!token;
  const userId = user?.user_id || null;
  const { isConnected: isSignalRConnected } = useSignalRNotifications(
    isAuthenticated ? userId : null
  );

  const handleSendTestNotification = async () => {
    if (!user?.user_id) {
      Alert.alert(
        "Error",
        "User ID not available. Please make sure you are logged in."
      );
      return;
    }

    try {
      await sendNotificationMutation.mutateAsync({
        userId: user.user_id,
        message: testMessage,
        title: "Interview Request",
        type: "interview",
      });

      Alert.alert("Success", "Test notification sent successfully!");
    } catch (error) {
      console.error("Failed to send test notification:", error);
      Alert.alert(
        "Error",
        "Failed to send test notification. Check console for details."
      );
    }
  };

  const handleSendSocketNotification = async () => {
    if (!user?.user_id) {
      Alert.alert(
        "Error",
        "User ID not available. Please make sure you are logged in."
      );
      return;
    }

    Alert.alert(
      "Socket Notification Test",
      "Use Swagger API at /api/v1/Notifications/send:\n\n" +
        `{\n  "userId": "${user.user_id}",\n  "message": "${testMessage}",\n  "title": "Interview Request",\n  "type": "interview"\n}\n\n` +
        "⚠️ Backend supports both 'type' and 'notificationType.name' formats.",
      [{ text: "OK" }]
    );
  };

  const handleTestSignalRDirectly = async () => {
    if (!user?.user_id) {
      Alert.alert("Error", "User ID not available.");
      return;
    }

    Alert.alert(
      "SignalR Connection Status",
      `Current Status:\n` +
        `• Authenticated: ${isAuthenticated ? "✅" : "❌"}\n` +
        `• SignalR Connected: ${isSignalRConnected ? "✅" : "❌"}\n` +
        `• User ID: ${user.user_id}\n` +
        `• Interview Completed: ${
          user?.userInfo?.interviewCompletedDate ? "✅" : "❌"
        }\n\n` +
        `${
          !isSignalRConnected
            ? "❌ SignalR is not connected! Check console for connection errors."
            : "✅ SignalR is connected and ready to receive notifications."
        }`,
      [{ text: "OK" }]
    );
  };

  const handleTestNonLoggedInFlow = async () => {
    if (!user?.user_id) {
      Alert.alert(
        "Error",
        "User ID not available. Please make sure you are logged in."
      );
      return;
    }

    Alert.alert(
      "Non-Logged-In User Test",
      "To test the non-logged-in user flow:\n\n" +
        "1. Send a notification using Swagger while logged out\n" +
        "2. Log back in to see the notification modal\n\n" +
        `Use this payload in Swagger:\n{\n  "userId": "${user.user_id}",\n  "message": "${testMessage}",\n  "title": "Interview Request",\n  "type": "interview"\n}\n\n` +
        "Make sure your interviewCompletedDate is null in the user profile.",
      [{ text: "OK" }]
    );
  };

  if (!__DEV__) {
    return null; // Only show in development
  }

  return (
    <View style={styles.container}>
      <AppText size="lg" weight="bold" style={styles.title}>
        🧪 Interview Notification Test
      </AppText>

      <AppText size="sm" style={styles.description}>
        Use these buttons to test the interview notification system:
      </AppText>

      <View style={styles.buttonContainer}>
        <AppButton
          text="Send API Notification"
          variant="default"
          size="sm"
          onPress={handleSendTestNotification}
          isLoading={sendNotificationMutation.isPending}
          disabled={sendNotificationMutation.isPending}
        />

        <AppButton
          text="Socket Info (Swagger)"
          variant="ghost"
          size="sm"
          onPress={handleSendSocketNotification}
        />

        <AppButton
          text="Test Non-Logged-In Flow"
          variant="outline"
          size="sm"
          onPress={handleTestNonLoggedInFlow}
        />

        <AppButton
          text="Check SignalR Status"
          variant="ghost"
          size="sm"
          onPress={handleTestSignalRDirectly}
        />
      </View>

      <View style={styles.infoContainer}>
        <AppText size="xs" style={styles.infoText}>
          User ID: {user?.user_id || "Not available"}
        </AppText>
        <AppText size="xs" style={styles.infoText}>
          Email: {user?.email || "Not available"}
        </AppText>
        <AppText size="xs" style={styles.infoText}>
          SignalR: {isSignalRConnected ? "🟢 Connected" : "🔴 Disconnected"}
        </AppText>
        <AppText size="xs" style={styles.infoText}>
          Interview Completed:{" "}
          {user?.userInfo?.interviewCompletedDate ? "✅ Yes" : "❌ No"}
        </AppText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.grayLight,
    borderRadius: scale(12),
    padding: scale(16),
    margin: scale(16),
  },
  title: {
    textAlign: "center",
    marginBottom: verticalScale(8),
    color: COLORS.primary,
  },
  description: {
    textAlign: "center",
    marginBottom: verticalScale(16),
    color: COLORS.gray,
  },
  buttonContainer: {
    gap: scale(8),
    marginBottom: verticalScale(16),
  },
  infoContainer: {
    borderTopWidth: 1,
    borderTopColor: COLORS.grayLight,
    paddingTop: verticalScale(12),
    gap: scale(4),
  },
  infoText: {
    color: COLORS.gray,
    fontSize: scale(10),
  },
});
