import React, { useState } from "react";
import { View, StyleSheet, Alert } from "react-native";
import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";
import { useSendNotification } from "@/services/notification/interview-notification-hooks";
import { useCurrentUser } from "@/services/user/user-hooks";
import { useSession } from "@/providers/auth/auth-provider";

/**
 * Debug component for testing persistent interview notifications
 * This component allows developers to test the persistent notification functionality
 * by simulating offline scenarios and verifying modal display behavior
 */
export const PersistentNotificationTest = () => {
  const { data: user } = useCurrentUser();
  const { token } = useSession();
  const sendNotificationMutation = useSendNotification();
  const [testMessage] = useState(
    "You have been invited for an interview verification. This is a test of persistent notifications for offline users."
  );

  const isAuthenticated = !!token;
  const userId = user?.user_id || null;

  const handleSendTestNotification = async () => {
    if (!userId) {
      Alert.alert("Error", "User ID not available");
      return;
    }

    try {
      await sendNotificationMutation.mutateAsync({
        userId: userId,
        message: testMessage,
        title: "Interview Request (Test)",
        type: "interview",
      });

      Alert.alert(
        "Test Notification Sent",
        `Notification sent to user ${userId}. ${
          isAuthenticated 
            ? "Since you're authenticated, check if polling still works for persistent notifications."
            : "Since you're not authenticated, the modal should appear via polling."
        }`
      );
    } catch (error) {
      console.error("Failed to send test notification:", error);
      Alert.alert("Error", "Failed to send test notification");
    }
  };

  const handleTestInterviewCompletion = () => {
    Alert.alert(
      "Interview Completion Test",
      `Current interview status: ${
        user?.userInfo?.interviewCompletedDate 
          ? `Completed on ${user.userInfo.interviewCompletedDate}`
          : "Not completed"
      }\n\nTo test persistent notifications:\n1. Send notification\n2. Close app\n3. Reopen app\n4. Modal should appear if interview not completed`
    );
  };

  return (
    <View style={styles.container}>
      <AppText variant="primary" size="lg" weight="bold" style={styles.title}>
        🧪 Persistent Notification Test
      </AppText>

      <View style={styles.infoContainer}>
        <AppText variant="secondary" size="sm" style={styles.infoText}>
          User ID: {userId || "Not available"}
        </AppText>
        <AppText variant="secondary" size="sm" style={styles.infoText}>
          Auth Status: {isAuthenticated ? "✅ Authenticated" : "❌ Not Authenticated"}
        </AppText>
        <AppText variant="secondary" size="sm" style={styles.infoText}>
          Interview Status: {
            user?.userInfo?.interviewCompletedDate 
              ? "✅ Completed" 
              : "❌ Not Completed"
          }
        </AppText>
      </View>

      <View style={styles.buttonContainer}>
        <AppButton
          title="Send Test Interview Notification"
          onPress={handleSendTestNotification}
          disabled={!userId || sendNotificationMutation.isPending}
          style={styles.button}
        />

        <AppButton
          title="Check Interview Status"
          onPress={handleTestInterviewCompletion}
          variant="outline"
          style={styles.button}
        />
      </View>

      <View style={styles.instructionsContainer}>
        <AppText variant="secondary" size="xs" style={styles.instructionsTitle}>
          Test Instructions:
        </AppText>
        <AppText variant="secondary" size="xs" style={styles.instructionsText}>
          1. Send test notification using button above
        </AppText>
        <AppText variant="secondary" size="xs" style={styles.instructionsText}>
          2. Close the app completely
        </AppText>
        <AppText variant="secondary" size="xs" style={styles.instructionsText}>
          3. Reopen the app after a few seconds
        </AppText>
        <AppText variant="secondary" size="xs" style={styles.instructionsText}>
          4. Interview modal should appear automatically
        </AppText>
        <AppText variant="secondary" size="xs" style={styles.instructionsText}>
          5. Test both authenticated and non-authenticated states
        </AppText>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.background,
    padding: scale(16),
    borderRadius: scale(12),
    borderWidth: 1,
    borderColor: COLORS.grayLight,
    margin: scale(16),
  },
  title: {
    textAlign: "center",
    marginBottom: verticalScale(16),
  },
  infoContainer: {
    backgroundColor: COLORS.grayLight,
    padding: scale(12),
    borderRadius: scale(8),
    marginBottom: verticalScale(16),
  },
  infoText: {
    marginBottom: verticalScale(4),
  },
  buttonContainer: {
    gap: verticalScale(12),
    marginBottom: verticalScale(16),
  },
  button: {
    width: "100%",
  },
  instructionsContainer: {
    backgroundColor: COLORS.background,
    padding: scale(12),
    borderRadius: scale(8),
    borderWidth: 1,
    borderColor: COLORS.gray,
  },
  instructionsTitle: {
    fontWeight: "bold",
    marginBottom: verticalScale(8),
  },
  instructionsText: {
    marginBottom: verticalScale(4),
    paddingLeft: scale(8),
  },
});
