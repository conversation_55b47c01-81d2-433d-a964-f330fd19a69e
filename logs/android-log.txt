 LOG  SUMSUB TOKEN -> _act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC00ZDBjODA0NC02ZmEzLTQyMzgtODY0Ny1hZTQ0MzI4ODdhZWMtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2
 LOG  ✅ Token received: {"token": "_act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC00ZDBjODA0NC02ZmEzLTQyMzgtODY0Ny1hZTQ0MzI4ODdhZWMtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2", "userEmail": "<EMAIL>"}
 LOG  🚀 Launching Sumsub SDK...
 WARN  `new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method.
 WARN  `new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method.
 LOG  ✅ Sumsub SDK launched successfully
 LOG  onLog EVENT -> {"message": "initFeatures: [Advanced 3D Face Scanning]"}
 LOG  onLog EVENT -> {"message": "starting: debug=true locale=en analytics=true"}
 LOG  onLog EVENT -> {"message": "SNSAppActivity.onCreate"}
 LOG  onLog EVENT -> {"message": "onProgress: progress=false, internalProgress=0, internalState=SNSOldViewModelInternalState(hideLogo=false, areStringsReady=false, isViewModelPrepared=false, poweredByText=null, progressText=null)"}
 LOG  onLog EVENT -> {"message": "Show progress = true"}
 LOG  onLog EVENT -> {"message": "The SDK state was changed: null -> com.sumsub.sns.core.data.model.SNSSDKState$Ready@d9d06be"}
 LOG  onLog EVENT -> {"message": "msdk:init payload: {source=msdk, isDKAEnabled=false, isRooted=true, isVideoIdentAvailable=true, isEidAvailable=true, isNFCAvailable=false, isXmlThemeUsed=false, kotlinVersion=2.0.21}"}
 LOG  onLog EVENT -> {"message": "onLoad"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating ..."}
 LOG  onLog EVENT -> {"message": "SNSMobileSDK: Api Url: https://api.sumsub.com/, Access Token: _ac ...,Modules: [Advanced 3D Face Scanning], isDebug: true"}
 LOG  onLog EVENT -> {"message": "JWT: header={\"alg\":\"none\"} payload={\"jti\":\"_act-sbx-4d0c8044-6fa3-4238-8647-ae4432887aec-v2\",\"url\":\"https://api.sumsub.com\"}"}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/featureFlags/msdk -> https://api.sumsub.com/resources/featureFlags/msdk"}
 LOG  onLog EVENT -> {"message": "Lifecycle: Preparing view model"}
 LOG  onLog EVENT -> {"message": "sdkState: com.sumsub.sns.core.data.model.SNSSDKState$Ready@d9d06be, SDK is ready"}
 LOG  onLog EVENT -> {"message": "onProgress: progress=false, internalProgress=1, internalState=SNSOldViewModelInternalState(hideLogo=false, areStringsReady=false, isViewModelPrepared=false, poweredByText=null, progressText=null)"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "DataKeeper refresh: waiting for existing update job to finish"}
 LOG  onLog EVENT -> {"message": "onProviderInstalled"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updated"}
 LOG  onLog EVENT -> {"message": "DataKeeper getAsResult, need to refresh, force=true"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating ..."}
 LOG  onLog EVENT -> {"message": "updating config, reinit=null, language=en"}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/sdkIntegrations/msdkInit?lang=en -> https://api.sumsub.com/resources/sdkIntegrations/msdkInit?lang=en"}
 LOG  onLog EVENT -> {"message": "DataKeeper getAsResult, need to refresh, force=false"}
 LOG  onLog EVENT -> {"message": "DataKeeper refresh: waiting for existing update job to finish"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updated"}
 LOG  onLog EVENT -> {"message": "updateDataFlow: skipping ..."}
 LOG  onLog EVENT -> {"message": "DataKeeper getAsResult, need to refresh, force=true"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating ..."}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one -> https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one"}
 LOG  onLog EVENT -> {"message": "updateDataFlow: skipping ..."}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "DataKeeper refresh: waiting for existing update job to finish"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating ..."}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating ..."}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/sdk/applicant/requiredIdDocsStatus -> https://api.sumsub.com/resources/sdk/applicant/requiredIdDocsStatus"}
 LOG  onLog EVENT -> {"message": "failure on https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating failed: kotlinx.serialization.SerializationException: com.sumsub.sns.internal.features.data.model.common.IdentitySide does not contain element with name 'INCORPORATION_CERT' at path $.requiredIdDocs.docSets[2].subTypes[0]"}
 LOG  onLog EVENT -> {"message": "updateDataFlow: skipping ..."}
 LOG  onLog EVENT -> {"message": "The SDK state was changed: com.sumsub.sns.core.data.model.SNSSDKState$Ready@d9d06be -> com.sumsub.sns.core.data.model.SNSSDKState$Failed$InitialLoadingFailed@6564222"}
 LOG  onStatusChanged EVENT -> {"newStatus": "InitialLoadingFailed", "prevStatus": "Ready"}
 LOG  📊 Sumsub status change: {"newStatus": "InitialLoadingFailed", "prevStatus": "Ready"}
 LOG  onLog EVENT -> {"message": "An error happened"}
 LOG  onLog EVENT -> {"message": "kotlinx.serialization.SerializationException: com.sumsub.sns.internal.features.data.model.common.IdentitySide does not contain element with name 'INCORPORATION_CERT' at path $.requiredIdDocs.docSets[2].subTypes[0]"}
 LOG  onLog EVENT -> {"message": "sending ..."}
 LOG  onLog EVENT -> {"message": "An error while preparing the sdk..."}
 LOG  onLog EVENT -> {"message": "onProgress: progress=false, internalProgress=1, internalState=SNSOldViewModelInternalState(hideLogo=false, areStringsReady=true, isViewModelPrepared=true, poweredByText=null, progressText=null)"}
 LOG  onLog EVENT -> {"message": "DataRepository: sns_alert_action_ok is not found"}
 LOG  onLog EVENT -> {"message": "fireEvent: ErrorEvent(error=com.sumsub.sns.internal.features.data.model.common.o$c@52be1e9, idDocSetType=TYPE_UNKNOWN, buttonText=null)"}
 LOG  onLog EVENT -> {"message": "event: ErrorEvent(error=com.sumsub.sns.internal.features.data.model.common.o$c@52be1e9, idDocSetType=TYPE_UNKNOWN, buttonText=null)"}
 LOG  onLog EVENT -> {"message": "Stop listening NFC"}
 LOG  onLog EVENT -> {"message": "Detach called but attachedActivity is null"}
 LOG  onLog EVENT -> {"message": "ShowFragment, tag = ErrorFragment"}
 LOG  onLog EVENT -> {"message": "onProgress: progress=false, internalProgress=0, internalState=SNSOldViewModelInternalState(hideLogo=false, areStringsReady=true, isViewModelPrepared=true, poweredByText=null, progressText=null)"}
 LOG  onLog EVENT -> {"message": "Lifecycle b{eb4e521} (d3846bed-7405-47e0-ab74-5a28ecf2bad1 id=0x7f09020a tag=ErrorFragment).onCreate"}
 LOG  onLog EVENT -> {"message": "Lifecycle b{eb4e521} (d3846bed-7405-47e0-ab74-5a28ecf2bad1 id=0x7f09020a tag=ErrorFragment).onViewCreated"}
 LOG  onLog EVENT -> {"message": "user:opened:screen payload: {source=msdk, screenName=oopsFatalScreen, stepName=TYPE_UNKNOWN, screen=oopsFatalScreen, enabledFFs=[androidVideoIdentConfig, androidAllowFaceScanFrameCalibration]}"}
 LOG  onLog EVENT -> {"message": "opening oopsFatalScreen"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "user:opened:screen payload: {source=msdk, screenName=oopsFatalScreen, stepName=TYPE_UNKNOWN, screen=oopsFatalScreen, error=kotlinx.serialization.SerializationException: com.sumsub.sns.internal.features.data.model.common.IdentitySide does not contain element with name 'INCORPORATION_CERT' at path $.requiredIdDocs.docSets[2].subTypes[0], enabledFFs=[androidVideoIdentConfig, androidAllowFaceScanFrameCalibration]}"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating ..."}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "Lifecycle b{eb4e521} (d3846bed-7405-47e0-ab74-5a28ecf2bad1 id=0x7f09020a tag=ErrorFragment).onStart"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "Lifecycle New state: com.sumsub.sns.core.presentation.base.f$f$c@636972a"}
 LOG  onLog EVENT -> {"message": "onProgress: progress=true, internalProgress=0, internalState=SNSOldViewModelInternalState(hideLogo=false, areStringsReady=true, isViewModelPrepared=true, poweredByText=null, progressText=null)"}
 LOG  onLog EVENT -> {"message": "Lifecycle b{eb4e521} (d3846bed-7405-47e0-ab74-5a28ecf2bad1 id=0x7f09020a tag=ErrorFragment).onResume"}
 LOG  onLog EVENT -> {"message": "DataKeeper refresh: waiting for existing update job to finish"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, refreshing"}
 LOG  onLog EVENT -> {"message": "DataKeeper refresh: waiting for existing update job to finish"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updated"}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/msdk/i18n?lang=en -> https://api.sumsub.com/resources/msdk/i18n?lang=en"}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/serviceLogger/warn -> https://api.sumsub.com/resources/serviceLogger/warn"}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one -> https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one"}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/sdkIntegrations/agreements -> https://api.sumsub.com/resources/sdkIntegrations/agreements"}
 LOG  onLog EVENT -> {"message": "failure on https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updating failed: kotlinx.serialization.SerializationException: com.sumsub.sns.internal.features.data.model.common.IdentitySide does not contain element with name 'INCORPORATION_CERT' at path $.requiredIdDocs.docSets[2].subTypes[0]"}
 LOG  onLog EVENT -> {"message": "DataKeeper Updated"}
 LOG  onLog EVENT -> {"message": "DataKeeper refreshIfEmpty, no need to refresh"}
 LOG  onLog EVENT -> {"message": "onProgress: progress=true, internalProgress=0, internalState=SNSOldViewModelInternalState(hideLogo=false, areStringsReady=true, isViewModelPrepared=true, poweredByText=Powered by dspartners.law, progressText=Please wait...)"}
 LOG  onLog EVENT -> {"message": "Lifecycle: ViewModel prepared"}
 LOG  onLog EVENT -> {"message": "updateDataFlow: skipping ..."}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/sdkIntegrations/-/clientIntegrationSettings -> https://api.sumsub.com/resources/sdkIntegrations/-/clientIntegrationSettings"}
 LOG  onLog EVENT -> {"message": "updateDataFlow: skipping ..."}
 LOG  onLog EVENT -> {"message": "onProgress: progress=true, internalProgress=0, internalState=SNSOldViewModelInternalState(hideLogo=true, areStringsReady=true, isViewModelPrepared=true, poweredByText=Powered by dspartners.law, progressText=Please wait...)"}
 LOG  onLog EVENT -> {"message": "sending ..."}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/tracking/trackEventsComp -> https://api.sumsub.com/resources/tracking/trackEventsComp"}
 LOG  onLog EVENT -> {"message": "An error happened"}
 LOG  onLog EVENT -> {"message": "Data flow timed out"}
 LOG  onLog EVENT -> {"message": "An error happened"}
 LOG  onLog EVENT -> {"message": "sending ..."}
 LOG  onLog EVENT -> {"message": "Data flow timed out"}
 LOG  onLog EVENT -> {"message": "sending ..."}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/serviceLogger/warn -> https://api.sumsub.com/resources/serviceLogger/warn"}
 LOG  onLog EVENT -> {"message": "Lifecycle New state: com.sumsub.sns.internal.features.presentation.error.c$b$c@e881806"}
 LOG  onLog EVENT -> {"message": "onProgress: progress=false, internalProgress=0, internalState=SNSOldViewModelInternalState(hideLogo=true, areStringsReady=true, isViewModelPrepared=true, poweredByText=Powered by dspartners.law, progressText=Please wait...)"}
 LOG  onLog EVENT -> {"message": "Substitute url: https://api.sumsub.com/resources/serviceLogger/warn -> https://api.sumsub.com/resources/serviceLogger/warn"}
 LOG  onLog EVENT -> {"message": "Show progress = false"}