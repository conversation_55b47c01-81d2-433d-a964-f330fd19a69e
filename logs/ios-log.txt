 LOG  GET TOKEN URL -> https://api.profitecosystem.com/api/v1/SumSub/get-tier-sdk-userid-level-token?userid=fb6a589e-3b55-4197-ba96-8b91081b0d07&levelName=kybtier1
 LOG  RESPONSE STATUS -> 200
 LOG  NORMALIZED RESPONSE -> {"data": "{
  \"token\": \"_act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC1jMjRjZDMyYi0zMTQ3LTQ0ZTMtODMzNy05MTJkYTdkYjZiMWYtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2\",
  \"userId\": \"fb6a589e-3b55-4197-ba96-8b91081b0d07\"
}", "errors": null, "isSuccess": true}
 LOG  SUMSUB TOKEN -> _act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC1jMjRjZDMyYi0zMTQ3LTQ0ZTMtODMzNy05MTJkYTdkYjZiMWYtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2
 LOG  ✅ Token received: {"token": "_act-sbx-jwt-eyJhbGciOiJub25lIn0.eyJqdGkiOiJfYWN0LXNieC1jMjRjZDMyYi0zMTQ3LTQ0ZTMtODMzNy05MTJkYTdkYjZiMWYtdjIiLCJ1cmwiOiJodHRwczovL2FwaS5zdW1zdWIuY29tIn0.-v2", "userEmail": "<EMAIL>"}
 LOG  🚀 Launching Sumsub SDK...
 LOG  ✅ Sumsub SDK launched successfully
 LOG  onLog EVENT -> {"level": 3, "message": "IdensicMobileSDK 1.35.1/********"}
 LOG  onLog EVENT -> {"level": 3, "message": "Started on arm64 with iOS 18.4"}
 LOG  onLog EVENT -> {"level": 4, "message": "POST https://api.sumsub.com/resources/sdkIntegrations/msdkInit?lang=en
request.body={\"email\":\"<EMAIL>\"}"}
 LOG  onLog EVENT -> {"level": 3, "message": "POST https://api.sumsub.com/resources/sdkIntegrations/msdkInit?lang=en (got 200)
response.json={
  \"uiConf\" : {
    \"useTextSelfiePhrase\" : false,
    \"livenessSaveMode\" : false
  },
  \"sumsubIdConfig\" : {
    \"type\" : \"disabled\"
  },
  \"applicantId\" : \"6835990618bd4ccb0bbca247\",
  \"flowType\" : \"standalone\",
  \"documentsByCountries\" : \"0 records (use `settings.logReponseBodyAsIs` to see the full response)\",
  \"accessToken\" : \"_act-sbx-c24cd32b-3147-44e3-8337-912da7db6b1f-v2\",
  \"sdkDict\" : {
    \"countryDependingFields\" : {
      \"PER\" : {
        \"number\" : \"DNI\"
      },
      \"NOR\" : {
        \"tin\" : \"National Identity Number\"
      },
      \"CHN\" : {
        \"tin\" : \"Resident Identity Card Number\"
      },
      \"SGP\" : {
        \"tin\" : \"NRIC\"
      },
      \"CRI\" : {
        \"number\" : \"National ID number\"
      },
      \"FIN\" : {
        \"tin\" : \"PIC\"
      },
      \"ESP\" : {
        \"tin\" : \"DNI(NIE)\"
      },
      \"CAN\" : {
        \"tin\" : \"SIN\",
        \"company\" : {
          \"taxId\" : {
            \"label\" : \"Business Number (BN)\",
            \"caption\" : \"9-digit number, received from Canada Revenue Agency (CRA)\"
          },
          \"registrationNumber\" : {
            \"label\" : \"Corporation Number\",
            \"caption\" : \"Company ID, 7-digit number without letter prefixes ON, BC, A\"
          }
        }
      },
      \"NZL\" : {
        \"number\" : \"Driver license number\"
      },
      \"SWE\" : {
        \"tin\" : \"PIN\"
      },
      \"JPN\" : {
        \"tin\" : \"My Number\"
      },
      \"KEN\" : {
        \"number\" : \"National ID number\"
      },
      \"ZAF\" : {
        \"number\" : \"National ID number\"
      },
      \"DNK\" : {
        \"tin\" : \"CPR\"
      },
      \"GHA\" : {
        \"number\" : \"Voter ID number\"
      },
      \"COL\" : {
        \"tin\" : \"NUIP\"
      },
      \"ARG\" : {
        \"number\" : \"DNI\"
      },
      \"HKG\" : {
        \"tin\" : \"HKID\"
      },
      \"ITA\" : {
        \"tin\" : \"Codice Fiscale\"
      },
      \"CHL\" : {
        \"tin\" : \"RUN(RUT)\"
      },
      \"MEX\" : {
        \"tin\" : \"RFC\",
        \"number\" : \"CURP\"
      },
      \"GBR\" : {
        \"company\" : {
          \"taxId\" : {
            \"label\" : \"UTR\",
            \"caption\" : \"Unique Taxpayer Reference, 10-digit number\"
          },
          \"registrationNumber\" : {
            \"label\" : \"CRN\",
            \"caption\" : \"Registration number, 8 digits or 2 letters + 6 digits\"
          }
        }
      },
      \"IND\" : {
        \"tin\" : \"PAN\"
      },
      \"THA\" : {
        \"number\" : \"Thai ID number\"
      },
      \"IDN\" : {
        \"tin\" : \"NIK\"
      },
      \"BRA\" : {
        \"tin\" : \"CPF\"
      },
      \"MYS\" : {
        \"tin\" : \"NRIC\"
      },
      \"POL\" : {
        \"number\" : \"PESEL\"
      },
      \"USA\" : {
        \"tin\" : \"SSN\",
        \"company\" : {
          \"taxId\" : {
            \"label\" : \"EIN\",
            \"caption\" : \"Federal tax identification number\"
          },
          \"registrationNumber\" : {
            \"label\" : \"File Number\",
            \"caption\" : \"Can be found on your registration documents you receive from the registrar such as account reminders\"
          }
        }
      },
      \"TUR\" : {
        \"number\" : \"T.C. Kimlik No\"
      },
      \"PHL\" : {
        \"number\" : \"SSS number\"
      },
      \"NGA\" : {
        \"tin\" : \"BVN\"
      }
    },
    \"languages\" : {
      \"zh\" : \"简体中文\",
      \"zh-tw\" : \"繁體中文\",
      \"ko\" : \"한국어\",
      \"id\" : \"Indonesia\",
      \"bn\" : \"বাংলা\",
      \"he\" : \"עִברִית\",
      \"fi\" : \"Suomi\",
      \"tr\" : \"Türkçe\",
      \"sgn-de\" : \"German sign language\",
      \"ru\" : \"Русский\",
      \"hu\" : \"magyar\",
      \"it\" : \"Italiano\",
      \"nl\" : \"Nederlands\",
      \"lo\" : \"ລາວ\",
      \"ur\" : \"اردو\",
      \"el\" : \"Ελληνικά\",
      \"ar\" : \"العربية\",
      \"hi\" : \"हिन्दी\",
      \"fl\" : \"Philippine\",
      \"no\" : \"norsk\",
      \"pl\" : \"polski\",
      \"en\" : \"English\",
      \"si\" : \"සිංහල\",
      \"th\" : \"ไทย\",
      \"hy\" : \"Հայերեն\",
      \"pt-br\" : \"Português Brasileiro\",
      \"lt\" : \"Lietuvių\",
      \"ms\" : \"Melayu\",
      \"cs\" : \"Čeština\",
      \"sk\" : \"Slovenský\",
      \"fa\" : \"فارسی\",
      \"bg\" : \"Български\",
      \"lv\" : \"Latviešu\",
      \"de\" : \"German\",
      \"vi\" : \"Tiếng Việt\",
      \"es\" : \"Español\",
      \"uk\" : \"Українська\",
      \"fr\" : \"Français\",
      \"ro\" : \"română\",
      \"et\" : \"eesti\",
      \"pt\" : \"Português\",
      \"km\" : \"ខ្មែរ\",
      \"am\" : \"አማርኛ\",
      \"my\" : \"မြန်မာ\",
      \"ja\" : \"日本語\"
    },
    \"idDocErrors\" : {
      \"forbiddenDocument_restrictedCountry\" : \"Verification cannot be completed with documents issued in {country} because it's restricted.\",
      \"documentDeclinedBefore\" : \"This document has been uploaded before and is not suitable for verification. Upload a different one\",
      \"forbiddenDocument_specificDocumentType\" : \"The uploaded {uploadedDocumentType} is not accepted. The approved documents for {country} are:\\n\\n{docTypesForUploadedIdDocTypeCountry}\",
      \"forbiddenDocument_noSelector\" : \"This document type is not accepted for verification.\",
      \"default_noSelector\" : \"Information from your document cannot be read. Make sure that the data is visible and clear.\",
      \"noIdDocFacePhoto\" : \"Ensure that your face is visible on the document. Upload a new image.\",
      \"documentWayTooMuchOutside\" : \"Your document does not fit the frame. Upload a new one.\",
      \"forbiddenDocument\" : \"This document type is not accepted for verification in this country. The accepted documents for this country include:\\n\\n{docTypes}\",
      \"shouldBeDoubleSided\" : \"Upload photos of each side of your document separately.\",
      \"needTranslation\" : \"Upload a certified English translation of your document or try a different identity document with latin characters.\",
      \"differentDocTypeOrCountry\" : \"The uploaded documents do not match the country or document type selected. Please upload documents consistent with your selections or choose the country and type of document you intend to upload.\",
      \"selfieFaceBadQuality\" : \"Ensure that your face is visible and fits the frame. Please try again.\",
      \"dataNotReadable_noSelector\" : \"Information from your document cannot be read. Make sure that the data is visible and clear.\",
      \"missingDob\" : \"Upload a photo of an identity document that contains your full date of birth.\",
      \"incompleteDob\" : \"Upload a photo of an identity document that contains your full date of birth.\",
      \"graphicEditors\" : \"Make sure your photo isn't edited with a graphics editor.\",
      \"differentDocTypeOrCountry_noSelector\" : \"Information from your document cannot be read. Make sure that the data is visible and clear.\",
      \"missingImportantInfo\" : \"Important information is missing or cannot be read. Upload photos that are clear and readable.\",
      \"screenRecapture\" : \"We do not accept photos from a screen. Upload a new image.\",
      \"sameSides\" : \"Ensure that you uploaded the front and back side of your document.\",
      \"shouldBeMrzDocument\" : \"Use a document containing a machine readable zone.\",
      \"docExpiresSoon\" : \"Please upload a document with a longer validity period.\",
      \"expiredDoc\" : \"Your document appears to be expired. Please upload a valid document.\",
      \"shouldBeDoublePaged\" : \"Upload both pages of your document as a single.\",
      \"screenshot\" : \"We do not accept screenshots. Upload a new image.\",
      \"dataNotReadable\" : \"Information from your document cannot be read. Make sure that the data is visible and clear. Also ensure that you selected the right document type and country.\",
      \"default\" : \"Make sure that the document is fully visible and all data is readable.\"
    },
    \"countries\" : {
      \"ITA\" : \"Italy\",
      \"VEN\" : \"Venezuela\",
      \"ASM\" : \"American Samoa\",
      \"NIC\" : \"Nicaragua\",
      \"NLD\" : \"Netherlands\",
      \"BDI\" : \"Burundi\",
      \"DMA\" : \"Dominica\",
      \"HKG\" : \"Hong Kong\",
      \"MLI\" : \"Mali\",
      \"KOR\" : \"South Korea\",
      \"KIR\" : \"Kiribati\",
      \"GTM\" : \"Guatemala\",
      \"IND\" : \"India\",
      \"AGO\" : \"Angola\",
      \"CAF\" : \"Central African Republic\",
      \"HTI\" : \"Haiti\",
      \"USA\" : \"United States of America\",
      \"UGA\" : \"Uganda\",
      \"GEO\" : \"Georgia\",
      \"SVK\" : \"Slovakia\",
      \"ZWE\" : \"Zimbabwe\",
      \"GNQ\" : \"Equatorial Guinea\",
      \"QAT\" : \"Qatar\",
      \"SPM\" : \"Saint Pierre and Miquelon\",
      \"CYM\" : \"Cayman Islands\",
      \"KNA\" : \"Saint Kitts and Nevis\",
      \"MCO\" : \"Monaco\",
      \"NFK\" : \"Norfolk Island\",
      \"SJM\" : \"Svalbard and Jan Mayen\",
      \"SVN\" : \"Slovenia\",
      \"GBR\" : \"United Kingdom\",
      \"DJI\" : \"Djibouti\",
      \"NCL\" : \"New Caledonia\",
      \"SDN\" : \"Sudan\",
      \"BGR\" : \"Bulgaria\",
      \"VGB\" : \"British Virgin Islands\",
      \"TJK\" : \"Tajikistan\",
      \"CAN\" : \"Canada\",
      \"BVT\" : \"Bouvet Island\",
      \"CYP\" : \"Cyprus\",
      \"MUS\" : \"Mauritius\",
      \"YEM\" : \"Yemen\",
      \"LUX\" : \"Luxembourg\",
      \"ALA\" : \"Åland Islands\",
      \"SGP\" : \"Singapore\",
      \"AIA\" : \"Anguilla\",
      \"ESH\" : \"Western Sahara\",
      \"MRT\" : \"Mauritania\",
      \"SYR\" : \"Syrian Arab Republic\",
      \"BMU\" : \"Bermuda\",
      \"ALB\" : \"Albania\",
      \"LKA\" : \"Sri Lanka\",
      \"MLT\" : \"Malta\",
      \"UMI\" : \"U.S. Minor Outlying Islands\",
      \"SMR\" : \"San Marino\",
      \"CMR\" : \"Cameroon\",
      \"PRI\" : \"Puerto Rico\",
      \"SGS\" : \"South Georgia and the South Sandwich Islands\",
      \"NOR\" : \"Norway\",
      \"TGO\" : \"Togo\",
      \"ARE\" : \"United Arab Emirates\",
      \"GMB\" : \"Gambia\",
      \"BRA\" : \"Brazil\",
      \"GAB\" : \"Gabon\",
      \"PRK\" : \"North Korea\",
      \"BFA\" : \"Burkina Faso\",
      \"BRB\" : \"Barbados\",
      \"SAU\" : \"Saudi Arabia\",
      \"ARG\" : \"Argentina\",
      \"POL\" : \"Poland\",
      \"NRU\" : \"Nauru\",
      \"CPV\" : \"Cabo Verde\",
      \"AFG\" : \"Afghanistan\",
      \"FJI\" : \"Fiji\",
      \"MOZ\" : \"Mozambique\",
      \"NIU\" : \"Niue\",
      \"MKD\" : \"Macedonia \",
      \"ESP\" : \"Spain\",
      \"PCN\" : \"Pitcairn\",
      \"SRB\" : \"Serbia\",
      \"KHM\" : \"Cambodia\",
      \"CUB\" : \"Cuba\",
      \"MNE\" : \"Montenegro\",
      \"SLB\" : \"Solomon Islands\",
      \"FSM\" : \"Micronesia\",
      \"HMD\" : \"Heard Island and McDonald Islands\",
      \"KEN\" : \"Kenya\",
      \"MNG\" : \"Mongolia\",
      \"COD\" : \"Congo (Kinshasa)\",
      \"ARM\" : \"Armenia\",
      \"BIH\" : \"Bosnia and Herzegovina\",
      \"SLE\" : \"Sierra Leone\",
      \"EST\" : \"Estonia\",
      \"MWI\" : \"Malawi\",
      \"TCA\" : \"Turks and Caicos Islands\",
      \"WSM\" : \"Samoa\",
      \"PRT\" : \"Portugal\",
      \"COG\" : \"Congo (Brazzaville)\",
      \"LBN\" : \"Lebanon\",
      \"KWT\" : \"Kuwait\",
      \"BOL\" : \"Bolivia\",
      \"ZMB\" : \"Zambia\",
      \"TCD\" : \"Chad\",
      \"CRI\" : \"Costa Rica\",
      \"BLM\" : \"Saint Barthélemy\",
      \"MHL\" : \"Marshall Islands\",
      \"JEY\" : \"Jersey\",
      \"BRN\" : \"Brunei Darussalam\",
      \"PLW\" : \"Palau\",
      \"AUS\" : \"Australia\",
      \"VAT\" : \"Vatican\",
      \"AUT\" : \"Austria\",
      \"COK\" : \"Cook Islands\",
      \"EGY\" : \"Egypt\",
      \"LBR\" : \"Liberia\",
      \"PRY\" : \"Paraguay\",
      \"SXM\" : \"Sint Maarten\",
      \"CCK\" : \"Cocos (Keeling) Islands\",
      \"COL\" : \"Colombia\",
      \"NZL\" : \"New Zealand\",
      \"SOM\" : \"Somalia\",
      \"MNP\" : \"Northern Mariana Islands\",
      \"LTU\" : \"Lithuania\",
      \"COM\" : \"Comoros\",
      \"MTQ\" : \"Martinique\",
      \"ZAF\" : \"South Africa\",
      \"RUS\" : \"Russia\",
      \"FRA\" : \"France\",
      \"BLR\" : \"Belarus\",
      \"ISL\" : \"Iceland\",
      \"ATA\" : \"Antarctica\",
      \"ROU\" : \"Romania\",
      \"LVA\" : \"Latvia\",
      \"PNG\" : \"Papua New Guinea\",
      \"IMN\" : \"Isle of Man\",
      \"SUR\" : \"Suriname\",
      \"TUN\" : \"Tunisia\",
      \"CXR\" : \"Christmas Island\",
      \"DOM\" : \"Dominican Republic\",
      \"ERI\" : \"Eritrea\",
      \"IDN\" : \"Indonesia\",
      \"TON\" : \"Tonga\",
      \"LBY\" : \"Libya\",
      \"RWA\" : \"Rwanda\",
      \"AND\" : \"Andorra\",
      \"AZE\" : \"Azerbaijan\",
      \"BWA\" : \"Botswana\",
      \"GGY\" : \"Guernsey\",
      \"NER\" : \"Niger\",
      \"GIB\" : \"Gibraltar\",
      \"GRC\" : \"Greece\",
      \"ATF\" : \"French Southern Territories\",
      \"ISR\" : \"Israel\",
      \"JPN\" : \"Japan\",
      \"JAM\" : \"Jamaica\",
      \"GRD\" : \"Grenada\",
      \"TUR\" : \"Turkey\",
      \"ATG\" : \"Antigua and Barbuda\",
      \"MEX\" : \"Mexico\",
      \"SLV\" : \"El Salvador\",
      \"MDA\" : \"Moldova\",
      \"BLZ\" : \"Belize\",
      \"PHL\" : \"Philippines\",
      \"CIV\" : \"Côte d'Ivoire\",
      \"CUW\" : \"Curaçao\",
      \"TLS\" : \"Timor-Leste\",
      \"GUF\" : \"French Guiana\",
      \"WLF\" : \"Wallis and Futuna\",
      \"MAC\" : \"Macao\",
      \"FLK\" : \"Falkland Islands\",
      \"TUV\" : \"Tuvalu\",
      \"NGA\" : \"Nigeria\",
      \"MAF\" : \"Saint Martin\",
      \"SWE\" : \"Sweden\",
      \"TZA\" : \"Tanzania\",
      \"FIN\" : \"Finland\",
      \"CZE\" : \"Czech Republic\",
      \"FRO\" : \"Faroe Islands\",
      \"DZA\" : \"Algeria\",
      \"MDG\" : \"Madagascar\",
      \"PER\" : \"Peru\",
      \"THA\" : \"Thailand\",
      \"CHE\" : \"Switzerland\",
      \"GRL\" : \"Greenland\",
      \"GUM\" : \"Guam\",
      \"LSO\" : \"Lesotho\",
      \"ECU\" : \"Ecuador\",
      \"VIR\" : \"U.S. Virgin Islands\",
      \"LAO\" : \"Laos\",
      \"BEL\" : \"Belgium\",
      \"GIN\" : \"Guinea\",
      \"VUT\" : \"Vanuatu\",
      \"UZB\" : \"Uzbekistan\",
      \"BTN\" : \"Bhutan\",
      \"URY\" : \"Uruguay\",
      \"VCT\" : \"Saint Vincent and the Grenadines\",
      \"GLP\" : \"Guadeloupe\",
      \"BEN\" : \"Benin\",
      \"ANT\" : \"Netherlands Antilles\",
      \"HUN\" : \"Hungary\",
      \"CHL\" : \"Chile\",
      \"NPL\" : \"Nepal\",
      \"SHN\" : \"Saint Helena\",
      \"SEN\" : \"Senegal\",
      \"BHR\" : \"Bahrain\",
      \"MSR\" : \"Montserrat\",
      \"IRL\" : \"Ireland\",
      \"CHN\" : \"China\",
      \"KGZ\" : \"Kyrgyzstan\",
      \"MMR\" : \"Myanmar\",
      \"MYS\" : \"Malaysia\",
      \"KAZ\" : \"Kazakhstan\",
      \"NAM\" : \"Namibia\",
      \"BHS\" : \"Bahamas\",
      \"ABW\" : \"Aruba\",
      \"BES\" : \"Bonaire, Sint Eustatius and Saba\",
      \"DNK\" : \"Denmark\",
      \"MAR\" : \"Morocco\",
      \"PSE\" : \"Palestine\",
      \"MYT\" : \"Mayotte\",
      \"PYF\" : \"French Polynesia\",
      \"STP\" : \"Sao Tome and Principe\",
      \"TKL\" : \"Tokelau\",
      \"ETH\" : \"Ethiopia\",
      \"IRN\" : \"Iran\",
      \"RKS\" : \"Kosovo\",
      \"REU\" : \"Réunion\",
      \"TKM\" : \"Turkmenistan\",
      \"TWN\" : \"Taiwan\",
      \"LCA\" : \"Saint Lucia\",
      \"TTO\" : \"Trinidad and Tobago\",
      \"GUY\" : \"Guyana\",
      \"GHA\" : \"Ghana\",
      \"GNB\" : \"Guinea-Bissau\",
      \"IRQ\" : \"Iraq\",
      \"MDV\" : \"Maldives\",
      \"OMN\" : \"Oman\",
      \"HRV\" : \"Croatia\",
      \"LIE\" : \"Liechtenstein\",
      \"PAK\" : \"Pakistan\",
      \"IOT\" : \"British Indian Ocean Territory\",
      \"BGD\" : \"Bangladesh\",
      \"PAN\" : \"Panama\",
      \"SWZ\" : \"Eswatini\",
      \"SYC\" : \"Seychelles\",
      \"JOR\" : \"Jordan\",
      \"VNM\" : \"Viet Nam\",
      \"HND\" : \"Honduras\",
      \"DEU\" : \"Germany\",
      \"UKR\" : \"Ukraine\",
      \"SSD\" : \"South Sudan\"
    },
    \"agreements\" : {
      \"defaultTitle\" : \"Non-US resident\",
      \"USA\" : \"US resident\",
      \"titleKey\" : {
        \"default\" : \"All countries except USA\",
        \"USA\" : \"United States of America\"
      },
      \"custom\" : {
        \"defaultDescription\" : \"Before you continue, you must consent to the processing of your personal data, as described in the [Notification to Processing of Personal Data]({link})\"
      }
    },
    \"countryStates\" : {
      \"USA\" : {
        \"MD\" : \"Maryland\",
        \"VT\" : \"Vermont\",
        \"NC\" : \"North Carolina\",
        \"DC\" : \"District of Columbia\",
        \"MS\" : \"Mississippi\",
        \"ME\" : \"Maine\",
        \"PA\" : \"Pennsylvania\",
        \"ND\" : \"North Dakota\",
        \"TX\" : \"Texas\",
        \"MT\" : \"Montana\",
        \"CT\" : \"Connecticut\",
        \"IL\" : \"Illinois\",
        \"DE\" : \"Delaware\",
        \"GA\" : \"Georgia\",
        \"NE\" : \"Nebraska\",
        \"OR\" : \"Oregon\",
        \"WV\" : \"West Virginia\",
        \"KY\" : \"Kentucky\",
        \"AK\" : \"Alaska\",
        \"IN\" : \"Indiana\",
        \"WI\" : \"Wisconsin\",
        \"MI\" : \"Michigan\",
        \"AZ\" : \"Arizona\",
        \"AL\" : \"Alabama\",
        \"IA\" : \"Iowa\",
        \"NV\" : \"Nevada\",
        \"NH\" : \"New Hampshire\",
        \"TN\" : \"Tennessee\",
        \"WY\" : \"Wyoming\",
        \"OH\" : \"Ohio\",
        \"NJ\" : \"New Jersey\",
        \"SC\" : \"South Carolina\",
        \"NY\" : \"New York\",
        \"SD\" : \"South Dakota\",
        \"ID\" : \"Idaho\",
        \"VA\" : \"Virginia\",
        \"LA\" : \"Louisiana\",
        \"MN\" : \"Minnesota\",
        \"OK\" : \"Oklahoma\",
        \"NM\" : \"New Mexico\",
        \"MO\" : \"Missouri\",
        \"CO\" : \"Colorado\",
        \"AR\" : \"Arkansas\",
        \"CA\" : \"California\",
        \"MA\" : \"Massachusetts\",
        \"RI\" : \"Rhode Island\",
        \"HI\" : \"Hawaii\",
        \"FL\" : \"Florida\",
        \"KS\" : \"Kansas\",
        \"WA\" : \"Washington\",
        \"UT\" : \"Utah\"
      },
      \"RUS\" : {
        \"11\" : \"Коми Республика\",
        \"55\" : \"Омская Область\",
        \"12\" : \"Марий Эл Республика\",
        \"99\" : \"Байконур Город\",
        \"56\" : \"Оренбургская Область\",
        \"13\" : \"Мордовия Республика\",
        \"80\" : \"Забайкальский край Агинский Бурятский Округ\",
        \"57\" : \"Орловская Область\",
        \"14\" : \"Саха \\/Якутия\\/ Республика\",
        \"81\" : \"Коми-Пермяцкий Автономный округ\",
        \"58\" : \"Пензенская Область\",
        \"15\" : \"Северная Осетия - Алания Республика\",
        \"82\" : \"Корякский Автономный округ\",
        \"59\" : \"Пермский Край\",
        \"16\" : \"Татарстан Республика\",
        \"83\" : \"Ненецкий Автономный округ\",
        \"40\" : \"Калужская Область\",
        \"17\" : \"Тыва Республика\",
        \"84\" : \"Таймырский (Долгано-Ненецкий) Автономный округ\",
        \"41\" : \"Камчатский Край\",
        \"18\" : \"Удмуртская Республика\",
        \"85\" : \"Иркутская обл Усть-Ордынский Бурятский Округ\",
        \"42\" : \"Кемеровская Область\",
        \"19\" : \"Хакасия Республика\",
        \"86\" : \"Ханты-Мансийский Автономный округ - Югра Автономный округ\",
        \"43\" : \"Кировская Область\",
        \"87\" : \"Чукотский Автономный округ\",
        \"44\" : \"Костромская Область\",
        \"01\" : \"Адыгея Республика\",
        \"88\" : \"Эвенкийский Автономный округ\",
        \"45\" : \"Курганская Область\",
        \"02\" : \"Башкортостан Республика\",
        \"89\" : \"Ямало-Ненецкий Автономный округ\",
        \"46\" : \"Курская Область\",
        \"03\" : \"Бурятия Республика\",
        \"47\" : \"Ленинградская Область\",
        \"70\" : \"Томская Область\",
        \"04\" : \"Алтай Республика\",
        \"48\" : \"Липецкая Область\",
        \"71\" : \"Тульская Область\",
        \"05\" : \"Дагестан Республика\",
        \"49\" : \"Магаданская Область\",
        \"72\" : \"Тюменская Область\",
        \"06\" : \"Ингушетия Республика\",
        \"73\" : \"Ульяновская Область\",
        \"30\" : \"Астраханская Область\",
        \"07\" : \"Кабардино-Балкарская Республика\",
        \"74\" : \"Челябинская Область\",
        \"31\" : \"Белгородская Область\",
        \"08\" : \"Калмыкия Республика\",
        \"75\" : \"Забайкальский Край\",
        \"32\" : \"Брянская Область\",
        \"09\" : \"Карачаево-Черкесская Республика\",
        \"76\" : \"Ярославская Область\",
        \"33\" : \"Владимирская Область\",
        \"77\" : \"Москва Город\",
        \"34\" : \"Волгоградская Область\",
        \"78\" : \"Санкт-Петербург Город\",
        \"35\" : \"Вологодская Область\",
        \"79\" : \"Еврейская Автономная область\",
        \"36\" : \"Воронежская Область\",
        \"37\" : \"Ивановская Область\",
        \"60\" : \"Псковская Область\",
        \"38\" : \"Иркутская Область\",
        \"61\" : \"Ростовская Область\",
        \"39\" : \"Калининградская Область\",
        \"62\" : \"Рязанская Область\",
        \"63\" : \"Самарская Область\",
        \"20\" : \"Чеченская Республика\",
        \"64\" : \"Саратовская Область\",
        \"21\" : \"Чувашская Республика - Чувашия\",
        \"65\" : \"Сахалинская Область\",
        \"22\" : \"Алтайский Край\",
        \"66\" : \"Свердловская Область\",
        \"23\" : \"Краснодарский Край\",
        \"67\" : \"Смоленская Область\",
        \"90\" : \"Запорожская область\",
        \"24\" : \"Красноярский Край\",
        \"91\" : \"Крым Республика\",
        \"68\" : \"Тамбовская Область\",
        \"25\" : \"Приморский Край\",
        \"92\" : \"Севастополь Город\",
        \"69\" : \"Тверская Область\",
        \"26\" : \"Ставропольский Край\",
        \"93\" : \"Донецкая Народная Республика\",
        \"50\" : \"Московская Область\",
        \"27\" : \"Хабаровский Край\",
        \"94\" : \"Луганская Народная Республика\",
        \"28\" : \"Амурская Область\",
        \"51\" : \"Мурманская Область\",
        \"95\" : \"Херсонская область\",
        \"29\" : \"Архангельская Область\",
        \"52\" : \"Нижегородская Область\",
        \"53\" : \"Новгородская Область\",
        \"10\" : \"Карелия Республика\",
        \"54\" : \"Новосибирская Область\"
      },
      \"CAN\" : {
        \"NT\" : \"Northwest Territories\",
        \"NB\" : \"New Brunswick\",
        \"MB\" : \"Manitoba\",
        \"NL\" : \"Newfoundland and Labrador\",
        \"NU\" : \"Nunavut\",
        \"SK\" : \"Saskatchewan\",
        \"PE\" : \"Prince Edward Island\",
        \"ON\" : \"Ontario\",
        \"YT\" : \"Yukon\",
        \"NS\" : \"Nova Scotia\",
        \"AB\" : \"Alberta\",
        \"BC\" : \"British Columbia\",
        \"QC\" : \"Quebec\"
      }
    },
    \"ekyc\" : {
      \"sources\" : {
        \"vnm_gov_dl_docfree\" : {
          \"name\" : \"Driver's license\",
          \"fields\" : {
            \"number\" : \"Driver's license number\",
            \"dob\" : \"Birth date\"
          },
          \"instructions\" : \"Enter your national driver's license number and birth date\"
        },
        \"nga_gov_dl_docfree\" : {
          \"name\" : \"Drivers license\",
          \"fields\" : {
            \"number\" : \"Driver licence number\"
          },
          \"instructions\" : \"Enter your national driver's license number\"
        },
        \"nga_gov_bvn\" : {
          \"name\" : \"BVN\",
          \"instructions\" : \"You can obtain your BVN from your bank.\"
        },
        \"nor_bankid_nbid_bis\" : {
          \"name\" : \"BankID Biometric\",
          \"submitData\" : \"Login\",
          \"instructions\" : \"Login to your BankID account to complete the identity verification process.<br><br> **Important:**\\n\\n *After clicking 'Login', you will be redirected to the BankID platform.*\\n\\n *Please follow the displayed instructions. If you exit the webpage without completing the necessary steps, the verification will NOT be successful.*\"
        },
        \"gbr_crd_bank\" : {
          \"name\" : \"Bank account\",
          \"submitData\" : \"Log in to your bank account\",
          \"instructions\" : \"Login to your bank account to complete the identity verification process\"
        },
        \"gbr_gov_evisa\" : {
          \"name\" : \"eVisa\",
          \"fields\" : {
            \"number\" : \"Share Code\",
            \"dob\" : \"Birth date\"
          },
          \"instructions\" : \"**Generating a Share Code**\\n\\n1. Make sure you have created an [account on the UKVI platform](https:\\/\\/www.gov.uk\\/update-uk-visas-immigration-account-details\\/update-your-ukvi-account) to access your eVisa.\\nNote: British and Irish citizens should continue the onboarding using another verification method.\\n\\n2. To successfully complete the verification process, generate a Share Code by selecting the **\\\"To prove my immigration status for anything else\\\"** option on the UK Home Office platform.\\n\\n3. Please follow the steps [here](https:\\/\\/view-immigration-status.service.gov.uk\\/get-share-code) to generate a share code correctly.\\n\\nBy clicking Continue you agree to share all personal information linked to your eVisa record, managed by the UK Visas and Immigration (UKVI) Department under the Home Office, for the purpose of identity verification.\"
        },
        \"arg_int_dni\" : {
          \"name\" : \"DEMO\",
          \"fields\" : {
            \"number\" : \"DNI\"
          },
          \"instructions\" : \"DEMO provider for testing purposes only.\\n\\n All inputs GREEN except: \\n\\n1. *****001 - DB is down\\n2. *****002 - Not found in DB\\n3. *****003 - No photo in DB\\n4. *****004 - External face match RED\\n5. *****005 - External face match FAILED\\n6. *****006 - External face match retry\\n7. *****007 - Provided doc expired\\n\\n\"
        },
        \"ken_gov_passport_docfree\" : {
          \"name\" : \"Passport\",
          \"fields\" : {
            \"number\" : \"Passport number\"
          },
          \"instructions\" : \"Enter your passport number\"
        },
        \"dnk_bankid_mitid\" : {
          \"name\" : \"MitID verification\",
          \"submitData\" : \"Login\",
          \"instructions\" : \"Login to your MitID account to complete the identity verification process.<br><br>**Important:**\\n\\n *After clicking 'Login', you will be redirected to the MitID application.*\\n\\n *Please follow the displayed instructions. If you exit the webpage without completing the necessary steps, the verification will NOT be successful.*\"
        },
        \"gha_gov_voter\" : {
          \"name\" : \"Voter ID\",
          \"fields\" : {
            \"number\" : \"Voter ID\"
          },
          \"instructions\" : \"Enter your Voter ID details\"
        },
        \"mex_gov_curp_ekyc\" : {
          \"name\" : \"CURP\",
          \"fields\" : {
            \"number\" : \"CURP\"
          },
          \"instructions\" : \"You can find the CURP number printed on any valid identity document. CURP number is a unique personal identifier that consists of 18 digits and letters.\"
        },
        \"zaf_gov_id_docfree\" : {
          \"fields\" : {
            \"number\" : \"National ID number\"
          },
          \"errors\" : {
            \"9100\" : \"Incorrect data entered or less than 18 years old\"
          },
          \"name\" : \"National ID\",
          \"instructions\" : \"Enter your National ID number\"
        },
        \"ind_gov_aadhaar_digi\" : {
          \"name\" : \"Aadhaar via Digilocker\",
          \"instructions\" : \"Login to your Digilocker account to complete the identity verification process.\\n\\n **Important:**\\n\\n Please note that after clicking 'Login' you will be redirected to the Digilocker webpage.\\n\\n Please follow the steps as indicated on the webpage. If you EXIT the webpage without completing the necessary steps, the verification will NOT be successful.\"
        },
        \"aus_gov_dvs\" : {
          \"name\" : \"Passport\"
        },
        \"usa_gov_ssn\" : {
          \"name\" : \"SSN\",
          \"instructions\" : \"You can obtain your SSN from the Social Security Administration.\"
        },
        \"intl_crd_pennydrop\" : {
          \"name\" : \"Penny Drop\",
          \"submitData\" : \"Proceed\",
          \"instructions\" : \"Log into your banking app and make a nominal payment to verify your identity and confirm your presence. Processing may take up to 24 hours, depending on your bank. The amount will be refunded within 48 hours.\"
        },
        \"mex_gov_curp\" : {
          \"name\" : \"CURP\",
          \"fields\" : {
            \"additionalNumber\" : \"CURP\"
          },
          \"instructions\" : \"You can find the CURP number printed on any valid identity document. CURP number is a unique personal identifier that consists of 18 digits and letters.\"
        },
        \"intl_prp_residency_2x2__aus__passport\" : {
          \"name\" : \"Address verification (Passport)\",
          \"fields\" : {
            \"number\" : \"Passport number\"
          },
          \"instructions\" : \"Enter your personal information and address details\"
        },
        \"intl_prp_residency_2x2__aus__visa\" : {
          \"name\" : \"Address verification (Visa)\",
          \"fields\" : {
            \"number\" : \"Foreign passport number\"
          },
          \"instructions\" : \"Enter your personal information and address details\"
        },
        \"aus_gov_dvs_visa\" : {
          \"name\" : \"Passport\",
          \"fields\" : {
            \"number\" : \"Foreign passport number\"
          }
        },
        \"nld_crd_idin\" : {
          \"name\" : \"IDIN\",
          \"submitData\" : \"Log in to your bank account\",
          \"instructions\" : \"Login to your bank account to complete the identity verification process\"
        },
        \"gbr_prp_residency_1x1\" : {
          \"name\" : \"Address verification\",
          \"instructions\" : \"Enter your personal information and address details\"
        },
        \"uzb_gov_pinfl\" : {
          \"name\" : \"Personal identification number (PINFL)\",
          \"fields\" : {
            \"tin\" : \"Personal identification number (PINFL)\",
            \"firstName\" : \"Given name\",
            \"lastName\" : \"Surname\"
          },
          \"instructions\" : \"You can find your PINFL on the National ID Card or Passport. PINFL is a unique personal identification number that consists of 14 digits.\"
        },
        \"intl_prp_residency_1x1\" : {
          \"name\" : \"Address verification\",
          \"instructions\" : \"Enter your personal information and address details\"
        },
        \"nga_gov_nin\" : {
          \"name\" : \"National ID (NIN)\",
          \"fields\" : {
            \"number\" : \"NIN\"
          },
          \"instructions\" : \"You can find your NIN on your ID card\"
        },
        \"arg_gov_dni\" : {
          \"name\" : \"DNI\",
          \"fields\" : {
            \"number\" : \"DNI\"
          },
          \"instructions\" : \"You can find your DNI on your ID card, passport, driver license or residence permit.\"
        },
        \"uzb_gov_id\" : {
          \"name\" : \"National ID \\/ Passport number\",
          \"fields\" : {
            \"number\" : \"National ID or Passport number\",
            \"firstName\" : \"Given name\",
            \"lastName\" : \"Surname\"
          },
          \"instructions\" : \"Enter your National ID Card or Passport document number.\"
        },
        \"gbr_prp_residency_2x2\" : {
          \"name\" : \"Address verification\",
          \"instructions\" : \"Enter your personal information and address details\"
        },
        \"ind_gov_aadhaar\" : {
          \"name\" : \"Aadhaar via UIDAI\",
          \"fields\" : {
            \"number\" : \"AADHAAR\"
          },
          \"instructions\" : \"Enter your AADHAAR to receive a verification code\"
        },
        \"intl_prp_residency_2x2__aus__tax_payer_number_doc\" : {
          \"name\" : \"Address verification\",
          \"instructions\" : \"Enter your personal information and address details\"
        },
        \"intl_prp_residency_2x2__aus__drivers\" : {
          \"name\" : \"Address verification (Drivers)\",
          \"fields\" : {
            \"number\" : \"Driver's license number\",
            \"additionalNumber\" : \"Driver's card number\"
          },
          \"instructions\" : \"Enter your personal information and address details\"
        },
        \"bra_gov_cpf\" : {
          \"name\" : \"CPF\",
          \"instructions\" : \"You can find your CPF on your ID card, driver license or residence permit.\"
        },
        \"eu_crd_ob\" : {
          \"fields\" : {
            \"europe_crd_ob_bank_id\" : \"Bank\"
          },
          \"name\" : \"Bank Account\",
          \"instructions\" : \"Login to your bank account\",
          \"placeholders\" : {
            \"europe_crd_ob_bank_id\" : \"Select your bank\"
          }
        },
        \"deu_gov_eid\" : {
          \"name\" : \"eID\",
          \"instructions\" : \"Please have your German eID card and the corresponding PIN ready.\\n\\nBe prepared, once you tap Continue you will have _5 minutes_ to complete the identification process.\"
        },
        \"aus_gov_dvs__aus__passport\" : {
          \"name\" : \"Passport\",
          \"fields\" : {
            \"number\" : \"Passport number\"
          }
        },
        \"aus_gov_dvs__aus__drivers\" : {
          \"name\" : \"Drivers license\",
          \"fields\" : {
            \"number\" : \"Driver's license number\",
            \"additionalNumber\" : \"Driver's card number\"
          }
        },
        \"fra_pst_eid\" : {
          \"name\" : \"Verification via L'Identité Numérique\",
          \"instructions\" : \"You will be redirected to France L'Identité Numérique Verification portal\"
        },
        \"bgd_gov_nid\" : {
          \"name\" : \"NID\",
          \"fields\" : {
            \"number\" : \"NID\",
            \"firstName\" : \"Full name\"
          },
          \"instructions\" : \"You can find your NID on your ID card.\"
        },
        \"nga_gov_nin_slip\" : {
          \"name\" : \"National ID (NIN)\",
          \"fields\" : {
            \"number\" : \"NIN\"
          },
          \"instructions\" : \"You can find your NIN on your ID card\"
        },
        \"ken_gov_id_docfree\" : {
          \"name\" : \"National ID\",
          \"fields\" : {
            \"number\" : \"National ID number\"
          },
          \"instructions\" : \"Enter your National ID number (not serial number)\"
        },
        \"nor_bankid_nbid_bid\" : {
          \"name\" : \"BankID High\",
          \"submitData\" : \"Login\",
          \"instructions\" : \"Login to your BankID account to complete the identity verification process.<br><br> **Important:**\\n\\n *After clicking 'Login', you will be redirected to the BankID platform.*\\n\\n *Please follow the displayed instructions. If you exit the webpage without completing the necessary steps, the verification will NOT be successful.*\"
        },
        \"idn_gov_nik\" : {
          \"name\" : \"NIK\",
          \"fields\" : {
            \"firstName\" : \"Full name\"
          },
          \"instructions\" : \"You can find your NIK on your ID card (KTP).\"
        },
        \"intl_prp_residency_2x2\" : {
          \"name\" : \"Address verification\",
          \"instructions\" : \"Enter your personal information and address details\"
        }
      }
    },
    \"errorCodes\" : {
      \"6005\" : \"Too many SMS from the sandbox environment.\",
      \"9504\" : \"The corporate registry is temporarily unavailable. Please report to the support and try again later.\",
      \"10402\" : \"Not authorized to access this resource\",
      \"6001\" : \"You are not allowed to send an SMS right now. Please wait a bit and try again.\",
      \"9105\" : \"You have made too many attempts. Please try again later or use a different method.\",
      \"9101\" : \"We're experiencing some issues right now. Please try again later.\",
      \"1009\" : \"This applicant has been rejected and cannot be processed.\",
      \"8000\" : \"Your license key has been exhausted. Please renew your subscription or contact support.\",
      \"1005\" : \"This file format is not supported. Select another file\",
      \"1001\" : \"You've exceeded the document limit. Contact support for more information\",
      \"7001\" : \"Invalid email address. Please check the email address and try again.\",
      \"6006\" : \"You've requested too many codes recently. Please try again in a few minutes.\",
      \"6002\" : \"Verification for this country code is unavailable. Please try a different country code and phone number.\",
      \"3000\" : \"The applicant is already in this state. No further action is needed.\",
      \"9106\" : \"Payment was cancelled or not successful. Please try again\",
      \"9501\" : \"No data source is enabled for the selected country\",
      \"6010\" : \"Monthly limit for video ident attempts in the sandbox environment has been reached.\",
      \"9102\" : \"We're experiencing some issues right now. Please try again later.\",
      \"1006\" : \"Your information is being reviewed. Please wait a while and try again\",
      \"1002\" : \"The file size is too big. Select a smaller file size\",
      \"6007\" : \"Invalid email address. Please check the email address and try again.\",
      \"1010\" : \"The document type is not in the list of required documents.\",
      \"6003\" : \"Verification for this country code is unavailable. Please try a different country code and phone number.\",
      \"9107\" : \"We cannot verify the data you provided. Please try again with different data.\",
      \"9502\" : \"Selected state is not supported\",
      \"2006\" : \"This link can't be opened. Disable your VPN and try again. Contact support if the issue persists.\",
      \"9103\" : \"We're experiencing some issues right now. Please try again later.\",
      \"9600\" : \"Invalid signature or wallet address, try again.\",
      \"1007\" : \"The file size exceeds the limit set in the configurations.\",
      \"1003\" : \"This file is empty. Select another file\",
      \"6008\" : \"Too many created applicants from the sandbox environment.\",
      \"1011\" : \"The file is encrypted and cannot be processed.\",
      \"6004\" : \"The number you are trying to verify has opted out of receiving messages\",
      \"6000\" : \"You have exceeded the SMS limit. Try an alternative method or contact support.\",
      \"9104\" : \"We couldn't verify the data you provided. Please try again later or provide different data.\",
      \"9100\" : \"Invalid data provided. Please check correctness of provided data and try again\",
      \"1008\" : \"This applicant has been marked as deleted and cannot be processed.\",
      \"1004\" : \"This file has been corrupted. Select another file\",
      \"1000\" : \"This file has already been uploaded\",
      \"6009\" : \"Daily limit for video ident attempts in the sandbox environment has been reached.\"
    },
    \"genders\" : {
      \"M\" : \"Male\",
      \"F\" : \"Female\"
    },
    \"supportedLanguages\" : {
      \"fl\" : \"Filipino\",
      \"ur\" : \"Urdu\",
      \"lt\" : \"Lithuanian\",
      \"id\" : \"Indonesian\",
      \"ja\" : \"Japanese\",
      \"no\" : \"Norwegian\",
      \"ms\" : \"Malay\",
      \"lv\" : \"Latvian\",
      \"hi\" : \"Hindi\",
      \"es\" : \"Spanish\",
      \"ka\" : \"Georgian\",
      \"pl\" : \"Polish\",
      \"et\" : \"Estonian\",
      \"zh\" : \"Chinese Simplified\",
      \"fr\" : \"French\",
      \"zh-tw\" : \"Chinese Traditional\",
      \"bg\" : \"Bulgarian\",
      \"my\" : \"Burmese\",
      \"da\" : \"Danish\",
      \"uz\" : \"Uzbek\",
      \"si\" : \"Sinhala\",
      \"am\" : \"Amharic\",
      \"hr\" : \"Croatian\",
      \"de\" : \"German\",
      \"pt\" : \"Portuguese\",
      \"th\" : \"Thai\",
      \"sk\" : \"Slovak\",
      \"ro\" : \"Romanian\",
      \"kk\" : \"Kazakh\",
      \"fa\" : \"Farsi\",
      \"hu\" : \"Hungarian\",
      \"bn\" : \"Bengali\",
      \"km\" : \"Central Khmer\",
      \"ar\" : \"Arabic\",
      \"it\" : \"Italian\",
      \"pt-br\" : \"Portuguese (Brazilian)\",
      \"ko\" : \"Korean\",
      \"zu\" : \"Zulu\",
      \"uk\" : \"Ukrainian\",
      \"hy\" : \"Armenian\",
      \"ru\" : \"Russian\",
      \"vi\" : \"Vietnamese\",
      \"sr\" : \"Serbian\",
      \"ha\" : \"Hausa\",
      \"lo\" : \"Lao\",
      \"el\" : \"Greek\",
      \"tr\" : \"Turkish\",
      \"fi\" : \"Finnish\",
      \"cs\" : \"Czech\",
      \"sv\" : \"Swedish\",
      \"nl\" : \"Dutch\",
      \"en\" : \"English\",
      \"he\" : \"Hebrew\",
      \"sw\" : \"Swahili\",
      \"az\" : \"Azerbaijani\"
    },
    \"smsTexts\" : {
      \"mobileSmsConfirmationText\" : \"Your verification code is:\"
    },
    \"customPhrases\" : {
      \"SELFIE\" : \"I am not a robot\",
      \"PAYMENT_SELFIE\" : \"This payment method is mine\",
      \"VIDEO_SELFIE\" : \"I am not a deepfake\"
    },
    \"instructions\" : {
      \"videoident\" : {
        \"title\" : \"Video identification\",
        \"actionTitle\" : \"Continue\",
        \"subtitle\" : \"You will have a video call with an operator. It won't take long\",
        \"contentBlocks\" : [
          {
            \"type\" : \"header\",
            \"header\" : \"Follow instructions:\"
          },
          {
            \"type\" : \"listItems\",
            \"listItems\" : [
              {
                \"image\" : \"iconID\",
                \"title\" : \"Have your ID documents ready\",
                \"subtitle\" : \"You will be asked to present one or two documents proving your identity\"
              },
              {
                \"image\" : \"iconHouse\",
                \"title\" : \"No Public Spaces\",
                \"subtitle\" : \"Avoid cafes, streets etc.\"
              },
              {
                \"image\" : \"iconPersons\",
                \"title\" : \"Alone in the room\",
                \"subtitle\" : \"Be the only person present\"
              },
              {
                \"image\" : \"iconLight\",
                \"title\" : \"Quiet, Lit Room\",
                \"subtitle\" : \"Find a quiet, well-lit room\"
              },
              {
                \"image\" : \"iconCamera\",
                \"title\" : \"Clear Camera\",
                \"subtitle\" : \"Make sure the camera isn't covered\"
              },
              {
                \"image\" : \"iconWifi\",
                \"title\" : \"Stable Internet\",
                \"subtitle\" : \"Use a stable Internet connection\"
              }
            ]
          }
        ]
      }
    }
  }
}"}
 LOG  onLog EVENT -> {"level": 4, "message": "GET https://api.sumsub.com/resources/sdkIntegrations/-/clientIntegrationSettings"}
 LOG  onLog EVENT -> {"level": 4, "message": "GET https://api.sumsub.com/resources/msdk/i18n?mergeWithDefault=true&lang=en"}
 LOG  onLog EVENT -> {"level": 4, "message": "POST https://api.sumsub.com/resources/featureFlags/msdk
request.body={\"requestedFlags\":[\"unsatisfactoryPhotosMobileToggle\",\"documentAutocaptureMobileToggle\",\"msdkSeamlessDocapture\",\"msdkCameraV2\",\"unsatisfactoryPhotosMobileConfigV2\",\"documentAutocaptureMobileConfigV2\",\"seamlessDocaptureMobileConfig\",\"makeCountryStateDropdownRequiredIfNeeded\",\"dontLimitCountriesOnAppDataStep\",\"msdkSkipAgreementSigning\",\"msdkEnableVerificationExitPopup\",\"iosAllowFaceScanFrameCalibration\",\"iosDisableAALCardUnblocking\",\"msdkExitSurvey\",\"msdkExitSurveyConfig\",\"msdkHideStepsOnPendingScreen\"]}"}
 LOG  onLog EVENT -> {"level": 3, "message": "GET https://api.sumsub.com/resources/sdkIntegrations/-/clientIntegrationSettings (got 200)
response.body={
  \"clientDisplayName\" : \"dspartners.law\",
  \"geoIpCountry\" : \"SRB\",
  \"twilioSettingsEnabled\" : false,
  \"imageConstraints\" : {
    \"minFileSize\" : 0,
    \"maxFileSize\" : null
  },
  \"minimalisticUi\" : true,
  \"verificationCodeLength\" : 6,
  \"sandboxEnv\" : true,
  \"allowSmsNotifications\" : false
}"}
 LOG  onLog EVENT -> {"level": 3, "message": "POST https://api.sumsub.com/resources/featureFlags/msdk (got 200)
response.body={
  \"msdkCameraV2\" : {
    \"experiment\" : true,
    \"enabled\" : false
  },
  \"iosAllowFaceScanFrameCalibration\" : {
    \"enabled\" : true
  }
}"}
 LOG  onLog EVENT -> {"level": 3, "message": "GET https://api.sumsub.com/resources/msdk/i18n?mergeWithDefault=true&lang=en (got 200)
response.body={
  \"sns_agreement_special_title\" : \"Begin Your Verification\",
  \"sns_agreement_special_subtitle\" : \"A simple step to confirm your identity and ensure your security.\",
  \"sns_instructions_hint_turnOver\" : \"Turn the document over\",
  \"sns_instructions_hint_allSet\" : \"All set!\",
  \"sns_autocapture_hint_targetAt\" : \"Target your camera at the document\",
  \"sns_autocapture_hint_moveIn\" : \"Move the camera closer to the document\",
  \"sns_autocapture_hint_moveOut\" : \"Move the camera away from the document\",
  \"sns_autocapture_hint_keepFocusing\" : \"Keep focusing to make sure the document is easy to read\",
  \"sns_autocapture_hint_holdLikeThis\" : \"Great! Hold like this for a moment\",
  \"sns_autocapture_action_auto\" : \"Auto\",
  \"sns_autocapture_action_manual\" : \"Manual\",
  \"sns_agreement_action_continue\" : \"Continue\",
  \"sns_agreement_footerHtml\" : \"By selecting agree and continue I agree that I have read the <a href='https://sumsub.com/privacy-notice-service/'>Privacy Notice</a> and I agree to the processing of my personal data, as described in <a href='pp'>Consent</a>.\",
  \"sns_agreement_header\" : \"I'm a resident of or live in:\",
  \"sns_alert_action_cancel\" : \"Cancel\",
  \"sns_alert_action_confirm\" : \"Confirm\",
  \"sns_alert_action_dismiss\" : \"Dismiss\",
  \"sns_alert_action_ok\" : \"OK\",
  \"sns_alert_action_settings\" : \"Settings\",
  \"sns_alert_lackOfCameraPermissions\" : \"Allow app to use your camera\",
  \"sns_alert_lackOfLocationPermissions\" : \"Allow app to use your geolocation\",
  \"sns_alert_lackOfMicrophonePermissions\" : \"Allow app to use your microphone\",
  \"sns_alert_lackOfPhotoLibraryPermissions\" : \"This app does not have permission to use the Photo Library, change your privacy settings\",
  \"sns_alert_lackOfSettingsPermissions\" : \"Allow app to adjust screen brightness settings for Liveness accuracy\",
  \"sns_alert_unableToCapturePhoto\" : \"Unable to capture photo\",
  \"sns_alert_unableToCaptureVideo\" : \"Unable to capture video\",
  \"sns_alert_unableToFindBackCamera\" : \"Unable to find rear camera\",
  \"sns_alert_unableToFindFrontCamera\" : \"Unable to find front camera\",
  \"sns_alert_unableToFindMicrophone\" : \"Unable to find microphone\",
  \"sns_alert_unableToRunPhotoSession\" : \"Unable to run photo session\",
  \"sns_alert_unableToRunVideoSession\" : \"Unable to run video session\",
  \"sns_alert_unableToUseBackCamera\" : \"Unable to use rear camera\",
  \"sns_alert_unableToUseFrontCamera\" : \"Unable to use front camera\",
  \"sns_alert_unableToUseMicrophone\" : \"Unable to use microphone\",
  \"sns_confirmation_contact_email_title\" : \"Verify your email address\",
  \"sns_confirmation_contact_email_subtitle\" : \"Enter your email address to receive a verification code.\",
  \"sns_confirmation_contact_email_placeholder\" : \"Your email\",
  \"sns_confirmation_contact_email_isNotValid\" : \"Your email address should look <NAME_EMAIL>.\",
  \"sns_confirmation_contact_phone_title\" : \"Verify your phone number\",
  \"sns_confirmation_contact_phone_subtitle\" : \"Enter your phone number to receive a verification code.\",
  \"sns_confirmation_contact_action_send\" : \"Continue\",
  \"sns_confirmation_code_ekyc_subtitle\" : \"Enter the verification code\",
  \"sns_confirmation_code_ekyc_title\" : \"Non-Doc verification\",
  \"sns_confirmation_code_email_title\" : \"Enter code to verify your email\",
  \"sns_confirmation_code_email_subtitle\" : \"We’ve sent the code to {email}. Check your email.\",
  \"sns_confirmation_code_phone_title\" : \"Enter code to verify your phone number\",
  \"sns_confirmation_code_phone_subtitle\" : \"We’ve sent the code to mobile number {phone}. Check your messages.\",
  \"sns_confirmation_code_isNotValid\" : \"The code is incorrect\",
  \"sns_confirmation_code_resendCountdown\" : \"Resend code in {time}\",
  \"sns_confirmation_code_action_resend\" : \"Resend code\",
  \"sns_confirmation_result_ekyc_failure_title\" : \"We couldn't verify your data\",
  \"sns_confirmation_result_ekyc_submitted_title\" : \"Your data has been submitted successfully!\",
  \"sns_confirmation_result_email_success_title\" : \"Your email has been verified!\",
  \"sns_confirmation_result_email_failure_title\" : \"We couldn't verify your email\",
  \"sns_confirmation_result_phone_success_title\" : \"Your phone number has been verified!\",
  \"sns_confirmation_result_phone_failure_title\" : \"We couldn't verify your phone number\",
  \"sns_confirmation_result_action_tryAgain\" : \"Please try again\",
  \"sns_countries_search_placeholder\" : \"Search\",
  \"sns_data_action_done\" : \"Done\",
  \"sns_data_action_next\" : \"Next\",
  \"sns_data_action_prev\" : \"Previous\",
  \"sns_data_action_submit\" : \"Continue\",
  \"sns_data_alert_validationFailed\" : \"Complete all the required fields and provide the correct information for the fields you fill out.\",
  \"sns_data_error_fieldIsMalformed\" : \"It looks like the field you entered is in the wrong format\",
  \"sns_data_error_fieldIsRequired\" : \"This field is required\",
  \"sns_data_field_buildingNumber\" : \"Building number\",
  \"sns_data_field_country\" : \"Country\",
  \"sns_data_field_countryOfBirth\" : \"Country of birth\",
  \"sns_data_field_dob\" : \"Date of birth\",
  \"sns_data_field_email\" : \"Email\",
  \"sns_data_field_firstName\" : \"First name\",
  \"sns_data_field_flatNumber\" : \"Flat number\",
  \"sns_data_field_gender\" : \"Gender\",
  \"sns_data_field_issuedDate\" : \"Issue date\",
  \"sns_data_field_lastName\" : \"Last name\",
  \"sns_data_field_legalName\" : \"Legal name\",
  \"sns_data_field_middleName\" : \"Middle name\",
  \"sns_data_field_nationality\" : \"Nationality\",
  \"sns_data_field_phone\" : \"Contact number\",
  \"sns_data_field_placeOfBirth\" : \"Place of birth\",
  \"sns_data_field_postCode\" : \"Post code\",
  \"sns_data_field_state\" : \"State\",
  \"sns_data_field_stateOfBirth\" : \"State of birth\",
  \"sns_data_field_street\" : \"Address line 1\",
  \"sns_data_field_subStreet\" : \"Address line 2\",
  \"sns_data_field_taxResidenceCountry\" : \"Tax residence country\",
  \"sns_data_field_tin\" : \"TIN\",
  \"sns_data_field_town\" : \"City\",
  \"sns_data_placeholder_optional\" : \"optional\",
  \"sns_data_placeholder_required\" : \"required\",
  \"sns_ekyc_action_continue\" : \"Continue\",
  \"sns_ekyc_action_skip\" : \"Verify documents instead\",
  \"sns_ekyc_country_placeholder\" : \"Select your country\",
  \"sns_ekyc_country_title\" : \"Country\",
  \"sns_ekyc_form_subtitle\" : \"Provide your data\",
  \"sns_ekyc_form_title\" : \"Non-Doc verification\",
  \"sns_ekyc_source_title\" : \"Method\",
  \"sns_facescan_action_retry\" : \"Please try again\",
  \"sns_facescan_hint_facePosition\" : \"Position your face to fit the frame\",
  \"sns_facescan_hint_lookStraight\" : \"Look straight into the camera\",
  \"sns_facescan_hint_processing\" : \"Processing...\",
  \"sns_facescan_hint_processingTakesTooLong\" : \"It seems the network is slow, please wait\",
  \"sns_data_hint_tin\" : \"Example: {example}\",
  \"sns_facescan_result_GREEN_text\" : \"\",
  \"sns_facescan_result_GREEN_title\" : \"Your face scan is complete\",
  \"sns_facescan_result_RED_text\" : \"\\nMake sure you're in a well lit environment and your face is not covered by light reflection. Also, try taking the check in a neutral background\\n\",
  \"sns_facescan_result_RED_title\" : \"We couldn't process your face scan\",
  \"sns_facescan_result_YELLOW_text\" : \"\",
  \"sns_facescan_result_YELLOW_title\" : \"Your data has been submitted\",
  \"sns_gallery_action_files\" : \"Files…\",
  \"sns_general_loadingTakesTooLong\" : \"Loading is taking longer than usual, please wait\",
  \"sns_general_poweredBy\" : \"Powered by dspartners.law\",
  \"sns_general_progress_text\" : \"Please wait...\",
  \"sns_general_search_placeholder\" : \"Search\",
  \"sns_general_waitingForNetwork\" : \"Waiting for network...\",
  \"sns_geolocation_action_allowAccess\" : \"Share Your location\",
  \"sns_geolocation_action_continue\" : \"Continue\",
  \"sns_geolocation_action_tryAgain\" : \"Please try again\",
  \"sns_geolocation_action_uploadDocument\" : \"Upload document\",
  \"sns_geolocation_detection_blocked\" : \"We can't use your geolocation. Allow access in your device settings to proceed.\",
  \"sns_geolocation_detection_invalidLocation\" : \"We can't determine your location. Please change your location and try again.\",
  \"sns_geolocation_detection_cameraFallback\" : \"As an option, you can upload one of these suitable documents (bank statements, utility bills, etc) to confirm your residence.\",
  \"sns_geolocation_detection_description\" : \"Please provide us with your geolocation data to prove your current location. We won't be tracking it after this step has been passed.\",
  \"sns_geolocation_form_subtitle\" : \"Please check your address details\",
  \"sns_iddoc_listing_join\" : \" • \",
  \"sns_iddoc_listing_join_details\" : \"\\n• \",
  \"sns_iddoc_status_approved\" : \"\",
  \"sns_iddoc_status_declined\" : \"Rejected\",
  \"sns_iddoc_status_notSubmitted\" : \"Not submitted\",
  \"sns_iddoc_status_reviewing\" : \"Under review\",
  \"sns_iddoc_status_submitted\" : \"Submitted\",
  \"sns_iddoc_type_AGREEMENT\" : \"Data and Privacy\",
  \"sns_iddoc_type_BANK_CARD\" : \"Bank card\",
  \"sns_iddoc_type_BANK_STATEMENT\" : \"Bank statement\",
  \"sns_iddoc_type_COMPANY_DOC\" : \"Company document\",
  \"sns_iddoc_type_CONTRACT\" : \"Contract\",
  \"sns_iddoc_type_SIGNED_CONTRACT\" : \"Signed contract\",
  \"sns_iddoc_type_DRIVERS\" : \"Driver license\",
  \"sns_iddoc_type_DRIVERS_TRANSLATION\" : \"Driver license translation\",
  \"sns_iddoc_type_EMPLOYMENT_CERTIFICATE\" : \"Employment certificate\",
  \"sns_iddoc_type_ID_CARD\" : \"ID card\",
  \"sns_iddoc_type_ID_DOC_PHOTO\" : \"ID Doc photo\",
  \"sns_iddoc_type_INCOME_SOURCE\" : \"Income source\",
  \"sns_iddoc_type_INSURANCE_CERTIFICATE\" : \"Insurance certificate\",
  \"sns_iddoc_type_INVESTOR_DOC\" : \"Investor document\",
  \"sns_iddoc_type_LEGAL_ENTITY\" : \"Legal entity document\",
  \"sns_iddoc_type_MRZ_DOCUMENT\" : \"MRZ document\",
  \"sns_iddoc_type_OTHER\" : \"Other\",
  \"sns_iddoc_type_PASSPORT\" : \"Passport\",
  \"sns_iddoc_type_PROFILE_IMAGE\" : \"Profile image\",
  \"sns_iddoc_type_PROFILE_SCREENSHOT\" : \"Profile screenshot\",
  \"sns_iddoc_type_PROOF_OF_PAYMENT\" : \"Proof of payment\",
  \"sns_iddoc_type_RESIDENCE_PERMIT\" : \"Residence permit\",
  \"sns_iddoc_type_SELFIE\" : \"Selfie\",
  \"sns_iddoc_type_SNILS\" : \"Pension card (SNILS)\",
  \"sns_iddoc_type_STUDENT_ID\" : \"Student ID\",
  \"sns_iddoc_type_TAXI_LICENSE\" : \"Taxi license\",
  \"sns_iddoc_type_TRAVEL_PASSPORT\" : \"Travel passport\",
  \"sns_iddoc_type_UTILITY_BILL\" : \"Utility bill\",
  \"sns_iddoc_type_VEHICLE_PASSPORT\" : \"Vehicle passport\",
  \"sns_iddoc_type_VEHICLE_REGISTRATION_CERTIFICATE\" : \"Vehicle registration certificate\",
  \"sns_iddoc_type_VISA\" : \"VISA\",
  \"sns_iddoc_type_WORK_PATENT\" : \"Work patent\",
  \"sns_iddoc_type_WORK_PERMIT\" : \"Work permit\",
  \"sns_iddoc_type_TAX_PAYER_NUMBER_DOC\" : \"Tax document\",
  \"sns_iddoc_type_VOTER_ID\" : \"Voter ID\",
  \"sns_liveness_action_cancel\" : \"Cancel\",
  \"sns_liveness_action_continue\" : \"Continue\",
  \"sns_liveness_action_retry\" : \"Resume\",
  \"sns_liveness_check_failed\" : \"Liveness check failed, please try again.\",
  \"sns_liveness_check_submitted\" : \"Your data was sent\",
  \"sns_liveness_check_success\" : \"Successful!\",
  \"sns_liveness_fail_cameraInitialization\" : \"Unable to start front camera. Make sure your camera is functioning properly.\",
  \"sns_liveness_fail_contextSwitch\" : \"Don't lock the screen or switch to other apps during the process.\",
  \"sns_liveness_fail_deviceLocked\" : \"Device is locked, please try again later.\",
  \"sns_liveness_fail_networkFailure\" : \"Please check your internet connection and try again.\",
  \"sns_liveness_fail_sessionTimeout\" : \"You've been inactive for a while. [Resume].\",
  \"sns_liveness_fail_wrongOrientation\" : \"Place your device in portrait mode and try again.\",
  \"sns_liveness_initializing\" : \"Starting...\",
  \"sns_liveness_prepareForRetake\" : \"Just a second...\",
  \"sns_liveness_processing\" : \"Processing...\",
  \"sns_liveness_title\" : \"Liveness detection\",
  \"sns_general_dataAccepted\" : \"Your data has been accepted\",
  \"sns_general_dataSubmited\" : \"Your data has been submitted\",
  \"sns_mrtdscan_title\" : \"Scan your document\",
  \"sns_mrtdscan_title::PASSPORT\" : \"Scan your passport\",
  \"sns_mrtdscan_subtitle\" : \"We noticed that your document supports NFC. Scan your document for a precise recognition\",
  \"sns_mrtdscan_hint_prepare\" : \"Place your phone on top of your document\",
  \"sns_mrtdscan_hint_prepare::PASSPORT\" : \"To scan, place your phone on top of your passport\",
  \"sns_mrtdscan_hint_scanning\" : \"Scanning...\",
  \"sns_mrtdscan_hint_scanFailed\" : \"Please, try to place your phone over the document on the next attempt\",
  \"sns_mrtdscan_hint_processing\" : \"Processing...\",
  \"sns_mrtdscan_reader_prompt\" : \"Hold your phone near the middle of your document\",
  \"sns_mrtdscan_reader_inProgress\" : \"Hold still\",
  \"sns_mrtdscan_reader_error\" : \"We couldn't read the chip on your document.\",
  \"sns_mrtdscan_reader_done\" : \"Done!\",
  \"sns_mrtdscan_action_retry\" : \"Try again\",
  \"sns_mrtdscan_action_start\" : \"Start scanning\",
  \"sns_mrtdscan_action_skip\" : \"Skip this step\",
  \"sns_navigation_close\" : \"Close\",
  \"sns_navigation_cancel\" : \"Cancel\",
  \"sns_oops_action_goBack\" : \"Go back\",
  \"sns_oops_action_retry\" : \"Try again\",
  \"sns_oops_fatal_html\" : \"Something went wrong. Please try again or contact <a href='support'>support</a>.\",
  \"sns_oops_fatal_title\" : \"Something went wrong.\",
  \"sns_oops_network_html\" : \"Please check your internet connection and try again.\",
  \"sns_oops_network_title\" : \"It looks like your network is down.\",
  \"sns_preview_idDocError_action_retake\" : \"Try again\",
  \"sns_preview_idDocError_brief\" : \"Your document was rejected. Please upload a different document.\",
  \"sns_preview_idDocWarning_action_continue\" : \"Continue\",
  \"sns_preview_idDocWarning_action_retake\" : \"Try again\",
  \"sns_preview_idDocWarning_brief\" : \"Potential issues have been detected in your submission.\",
  \"sns_preview_imageIssues_subtitle\" : \"There seem to be some issues with the image. Please ensure that the document fits the frame and that all information is visible and easy to read.\",
  \"sns_preview_imageIssues_title\" : \"Issues detected\",
  \"sns_preview_photo_action_accept\" : \"Document is readable\",
  \"sns_preview_photo_action_retake\" : \"Retake photo\",
  \"sns_preview_photo_subtitle\" : \"Make sure that all the information on the document is visible and easy to read\",
  \"sns_preview_photo_title\" : \"Photo preview\",
  \"sns_preview_uploading_title\" : \"Uploading\",
  \"sns_preview_video_action_accept\" : \"Video is acceptable\",
  \"sns_preview_video_action_retake\" : \"Retake video\",
  \"sns_preview_video_subtitle\" : \"Make sure the video is not blurred and the sound is clear\",
  \"sns_preview_video_title\" : \"Video preview\",
  \"sns_prompt_doubleSide_action_no\" : \"No\",
  \"sns_prompt_doubleSide_action_yes\" : \"Yes\",
  \"sns_prompt_doubleSide_text\" : \"Is the document you scanned double-sided?\",
  \"sns_prompt_gallery_action_documentPicker\" : \"Upload a PDF...\",
  \"sns_prompt_gallery_action_protoLibrary\" : \"Choose a photo...\",
  \"sns_quiestionnaire_field_isRequired\" : \"This field is required\",
  \"sns_quiestionnaire_field_isNotValid\" : \"This field doesn't look right\",
  \"sns_quiestionnaire_action_addFile\" : \"Add file\",
  \"sns_quiestionnaire_action_clear\" : \"Clear\",
  \"sns_quiestionnaire_action_continue\" : \"Continue\",
  \"sns_status_APPROVED_footerHtml\" : \"\",
  \"sns_status_APPROVED_subtitle\" : \"Completed\",
  \"sns_status_APPROVED_title\" : \"Verification status\",
  \"sns_status_FINAL_REJECT_footerHtml\" : \"\",
  \"sns_status_FINAL_REJECT_subtitle\" : \"Sorry, we couldn't verify your identity.\",
  \"sns_status_FINAL_REJECT_title\" : \"Verification status\",
  \"sns_status_INCOMPLETE_action_continue\" : \"Continue\",
  \"sns_status_INCOMPLETE_footerHtml\" : \"To complete the verification process, you need to submit all the documents. If you experience any issues, contact <a href='support'>support</a>.\",
  \"sns_status_INCOMPLETE_subtitle\" : \"It will only take about 2 minutes\",
  \"sns_status_INCOMPLETE_title\" : \"Complete your verification\",
  \"sns_status_INITIAL_action_continue\" : \"Continue\",
  \"sns_status_INITIAL_footerHtml\" : \"By tapping Continue, you consent to processing your personal data according to our <a href='pp'>Consent to Personal data processing document</a>\",
  \"sns_status_INITIAL_footerHtml_noAgreement\" : \"\",
  \"sns_status_INITIAL_subtitle\" : \"It will only take 2 minutes\",
  \"sns_status_INITIAL_title\" : \"Verify your identity\",
  \"sns_status_PENDING_footerHtml\" : \"Your verification status will change automatically once the review is complete. If you experience any issues, contact <a href='support'>support</a>.\",
  \"sns_status_PENDING_subtitle\" : \"The system is reviewing your documents.\\nThis will take about 2 minutes.\",
  \"sns_status_PENDING_title\" : \"Verification status\",
  \"sns_status_REJECT_footerHtml\" : \"If you experience any issues, contact <a href='support'>support</a>.\",
  \"sns_status_REJECT_subtitle\" : \"There was a problem with your documents. Find the details below to see the issue and how you can resolve it.\",
  \"sns_status_REJECT_title\" : \"Verification status\",
  \"sns_status_VIDEO_IDENT_title\" : \"Video identification\",
  \"sns_status_VIDEO_IDENT_subtitle\" : \"You will have a video call with an operator. It won't take long\",
  \"sns_status_VIDEO_IDENT_header_image\" : \"default/videoident\",
  \"sns_status_VIDEO_IDENT_header_title\" : \"Prepare for the video call\",
  \"sns_status_VIDEO_IDENT_instructions_text\" : \"- Make sure: The room is well-lit\\n\\n- There is nobody in the room\\n\\n- Your device's camera is uncovered and working\\n\\n- Call will be handled in English or German language\",
  \"sns_status_VIDEO_IDENT_footerHtml\" : \"By proceeding, you accept the conditions on our <a href='pp'>Consent to personal data processing document</a>\",
  \"sns_status_VIDEO_IDENT_footerHtml_noAgreement\" : \"\",
  \"sns_status_VIDEO_IDENT_action_continue\" : \"Continue\",
  \"sns_status_defaults_title\" : \"Verification status\",
  \"sns_step_APPLICANT_DATA_prompt\" : \"Verify and complete your personal information.\",
  \"sns_step_APPLICANT_DATA_title\" : \"Profile details\",
  \"sns_step_E_KYC_title\" : \"Non-Doc verification\",
  \"sns_step_E_KYC_prompt\" : \"Complete your data verification\",
  \"sns_step_EMAIL_VERIFICATION_title\" : \"Email verification\",
  \"sns_step_EMAIL_VERIFICATION_prompt\" : \"We will send you a code to verify your email\",
  \"sns_step_IDENTITY2_prompt\" : \"Take a photo of your second document\",
  \"sns_step_IDENTITY2_scan_backSide_brief\" : \"Take a photo of the back side of your document.\",
  \"sns_step_IDENTITY2_scan_backSide_details\" : \"\\nTake a photo of the back side of your document.\\n\\nMake sure that the whole document is visible, and no information is covered with your fingers or light reflection.\\n\",
  \"sns_step_IDENTITY2_scan_backSide_title\" : \"Turn over your document\",
  \"sns_step_IDENTITY2_scan_frontSide_brief\" : \"Take a photo of the front side of your 2nd document.\",
  \"sns_step_IDENTITY2_scan_frontSide_details\" : \"\\nTake a photo of the front side of your 2nd document. \\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY2_scan_frontSide_title\" : \"Front side of 2nd ID\",
  \"sns_step_IDENTITY2_selector_country_placeholder\" : \"Country of issue\",
  \"sns_step_IDENTITY2_selector_country_prompt\" : \"Select country where your ID document was issued.\",
  \"sns_step_IDENTITY2_selector_footerHtml\" : \"If you can’t find your document type or country of issue, contact <a href='support'>support</a>.\",
  \"sns_step_IDENTITY2_selector_iddoc_listIsEmpty\" : \"Sorry, we do not accept documents issued by the selected country.\",
  \"sns_step_IDENTITY2_selector_iddoc_prompt\" : \"Choose your 2nd ID type\",
  \"sns_step_IDENTITY2_title\" : \"Identity document\",
  \"sns_step_IDENTITY2_videoident_prompt\" : \"You will need to show one of these documents: {doctypes}\",
  \"sns_step_IDENTITY3_prompt\" : \"Take a photo of your third document\",
  \"sns_step_IDENTITY3_scan_backSide_brief\" : \"Take a photo of the back side of your document.\",
  \"sns_step_IDENTITY3_scan_backSide_details\" : \"\\nTake a photo of the back side of your document.\\n\\nMake sure that the whole document is visible, and no information is covered with your fingers or light reflection.\\n\",
  \"sns_step_IDENTITY3_scan_backSide_title\" : \"Turn over your document\",
  \"sns_step_IDENTITY3_scan_frontSide_brief\" : \"Take a photo of the front side of your 3rd document.\",
  \"sns_step_IDENTITY3_scan_frontSide_details\" : \"\\nTake a photo of the front side of your 3rd document. \\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY3_scan_frontSide_title\" : \"Front side of 3rd ID\",
  \"sns_step_IDENTITY3_selector_country_placeholder\" : \"Country of issue\",
  \"sns_step_IDENTITY3_selector_country_prompt\" : \"Select country where your ID document was issued.\",
  \"sns_step_IDENTITY3_selector_footerHtml\" : \"If you can’t find your document type or country of issue, contact <a href='support'>support</a>.\",
  \"sns_step_IDENTITY3_selector_iddoc_listIsEmpty\" : \"Sorry, we do not accept documents issued by the selected country.\",
  \"sns_step_IDENTITY3_selector_iddoc_prompt\" : \"Choose your 3rd ID type\",
  \"sns_step_IDENTITY3_title\" : \"Identity document\",
  \"sns_step_IDENTITY3_videoident_prompt\" : \"You will need to show one of these documents: {doctypes}\",
  \"sns_step_IDENTITY4_prompt\" : \"Take a photo of your forth document\",
  \"sns_step_IDENTITY4_scan_backSide_brief\" : \"Take a photo of the back side of your document.\",
  \"sns_step_IDENTITY4_scan_backSide_details\" : \"\\nTake a photo of the back side of your document.\\n\\nMake sure that the whole document is visible, and no information is covered with your fingers or light reflection.\\n\",
  \"sns_step_IDENTITY4_scan_backSide_title\" : \"Turn over your document\",
  \"sns_step_IDENTITY4_scan_frontSide_brief\" : \"Take a photo of the front side of your 4th document.\",
  \"sns_step_IDENTITY4_scan_frontSide_details\" : \"\\nTake a photo of the front side of your 4th document. \\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY4_scan_frontSide_title\" : \"Front side of 4th ID\",
  \"sns_step_IDENTITY4_selector_country_placeholder\" : \"Country of issue\",
  \"sns_step_IDENTITY4_selector_country_prompt\" : \"Select country where your ID document was issued.\",
  \"sns_step_IDENTITY4_selector_footerHtml\" : \"If you can’t find your document type or country of issue, contact <a href='support'>support</a>.\",
  \"sns_step_IDENTITY4_selector_iddoc_listIsEmpty\" : \"Sorry, we do not accept documents issued by the selected country.\",
  \"sns_step_IDENTITY4_selector_iddoc_prompt\" : \"Choose your 4th ID type\",
  \"sns_step_IDENTITY4_title\" : \"Identity document\",
  \"sns_step_IDENTITY4_videoident_prompt\" : \"You will need to show one of these documents: {doctypes}\",
  \"sns_step_IDENTITY_prompt\" : \"Take a photo of your ID\",
  \"sns_step_IDENTITY_scan_backSide_brief\" : \"Take a photo of the back side of your document.\",
  \"sns_step_IDENTITY_scan_backSide_details\" : \"\\nTake a photo of the back side of your ID. Make sure that the whole document is visible.\\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY_scan_backSide_title\" : \"Back side of ID\",
  \"sns_step_IDENTITY_scan_frontSide_brief\" : \"Take a photo of the front side of your identity document.\",
  \"sns_step_IDENTITY_scan_frontSide_details\" : \"\\nTake a photo of the front side of your ID.\\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY_scan_frontSide_title\" : \"Front of ID\",
  \"sns_step_IDENTITY_selector_country_placeholder\" : \"Country of issue\",
  \"sns_step_IDENTITY_selector_country_prompt\" : \"Select country where your ID document was issued.\",
  \"sns_step_IDENTITY_selector_footerHtml\" : \"If you can’t find your document type or country of issue, contact <a href='support'>support</a>.\",
  \"sns_step_IDENTITY_selector_iddoc_listIsEmpty\" : \"Sorry, we do not accept documents issued by the selected country.\",
  \"sns_step_IDENTITY_selector_iddoc_prompt\" : \"Select your document type\",
  \"sns_step_IDENTITY_title\" : \"Identity document\",
  \"sns_step_IDENTITY_videoident_prompt\" : \"You will need to show one of these documents: {doctypes}\",
  \"sns_step_PHONE_VERIFICATION_title\" : \"Phone verification\",
  \"sns_step_PHONE_VERIFICATION_prompt\" : \"We will send the verification code via SMS\",
  \"sns_step_PROOF_OF_RESIDENCE_geolocation_prompt\" : \"Provide address using your location\",
  \"sns_step_PROOF_OF_RESIDENCE_prompt\" : \"Take a photo of your Proof of address\",
  \"sns_step_PROOF_OF_RESIDENCE_scan_frontSide_brief\" : \"Take a photo of your Proof of address. The document should contain your full name, address and must not be older than 3 month(s).\",
  \"sns_step_PROOF_OF_RESIDENCE_scan_frontSide_details\" : \"\\n✅ We accept:\\n\\n• Bank statements\\n• Utility bills\\n• Internet/Cable TV/House phone line bills\\n• Tax returns\\n• Council tax bills\\n• Government-issued certifications of residence\\n\\n\\uD83D\\uDEAB We don't accept:\\n\\n• Screenshots\\n• Mobile phone bills\\n• Medical bills\\n• Receipts\\n• Insurance statements\\n\\n\",
  \"sns_step_PROOF_OF_RESIDENCE_scan_frontSide_title\" : \"Proof of address\",
  \"sns_step_PROOF_OF_RESIDENCE_title\" : \"Proof of address\",
  \"sns_step_PROOF_OF_RESIDENCE2_geolocation_prompt\" : \"Provide address using your location\",
  \"sns_step_PROOF_OF_RESIDENCE2_prompt\" : \"Take a photo of your proof of address\",
  \"sns_step_PROOF_OF_RESIDENCE2_scan_frontSide_brief\" : \"Take a photo of your proof of address. The document should contain your full name, address and must not be older than 3 month(s).\",
  \"sns_step_PROOF_OF_RESIDENCE2_scan_frontSide_details\" : \"\\n✅ We accept:\\n\\n• Bank statements\\n• Utility bills\\n• Internet/Cable TV/House phone line bills\\n• Tax returns\\n• Council tax bills\\n• Government-issued certifications of residence\\n\\n\\uD83D\\uDEAB We don't accept:\\n\\n• Screenshots\\n• Mobile phone bills\\n• Medical bills\\n• Receipts for purchases\\n• Insurance statements\\n\\n\",
  \"sns_step_PROOF_OF_RESIDENCE2_scan_frontSide_title\" : \"Proof of address\",
  \"sns_step_PROOF_OF_RESIDENCE2_title\" : \"Proof of address\",
  \"sns_step_QUESTIONNAIRE_title\" : \"Questionnaire\",
  \"sns_step_QUESTIONNAIRE_prompt\" : \"We will ask you few questions\",
  \"sns_step_SELFIE2_photo_brief\" : \"Take a photo of yourself holding your document.\",
  \"sns_step_SELFIE2_photo_details\" : \"Take a photo of yourself holding your document. Both your face and the document must be: fully visible. The document must be the same one that you provided previously.\",
  \"sns_step_SELFIE2_photo_title\" : \"Selfie with document\",
  \"sns_step_SELFIE2_portrait_brief\" : \"Take a photo of yourself.\",
  \"sns_step_SELFIE2_portrait_details\" : \"Take a photo of yourself. Make sure please your face is fully visible.\",
  \"sns_step_SELFIE2_portrait_title\" : \"Selfie\",
  \"sns_step_SELFIE2_prompt\" : \"Take a selfie\",
  \"sns_step_SELFIE2_recording_header\" : \"Record a video selfie\",
  \"sns_step_SELFIE2_recording_instructions\" : \"Say the text on the screen aloud\",
  \"sns_step_SELFIE2_recording_readAloudText\" : \"“I'm not a robot”;“I confirm that I uploaded my own documents“\",
  \"sns_step_SELFIE2_title\" : \"Second Selfie\",
  \"sns_step_SELFIE_photo_brief\" : \"Take a photo of you holding your document.\",
  \"sns_step_SELFIE_photo_details\" : \"Take a photo of you holding your document. Both your face and the document must be: fully visible. The document must be the same one that you provided previously.\",
  \"sns_step_SELFIE_photo_title\" : \"Selfie with document\",
  \"sns_step_SELFIE_portrait_brief\" : \"Take a photo of yourself.\",
  \"sns_step_SELFIE_portrait_details\" : \"Take a photo of yourself. Make sure your face is fully visible.\",
  \"sns_step_SELFIE_portrait_title\" : \"Selfie\",
  \"sns_step_SELFIE_prompt\" : \"Take a selfie\",
  \"sns_step_SELFIE_recording_header\" : \"Record a video selfie\",
  \"sns_step_SELFIE_recording_instructions\" : \"Say the text on the screen aloud\",
  \"sns_step_SELFIE_recording_readAloudText\" : \"“I'm not a robot”;“I confirm that I uploaded my own documents“\",
  \"sns_step_SELFIE_title\" : \"Selfie\",
  \"sns_step_VIDEO_IDENT_prompt\" : \"You will have а video call with operator. It won't take long.\",
  \"sns_step_defaults_scan_backSide_brief\" : \"Make sure that all the details on the document are visible, free of blur and glare.\",
  \"sns_step_defaults_scan_backSide_details\" : \"\",
  \"sns_step_defaults_scan_backSide_title\" : \"Take a photo of the next page of the document\",
  \"sns_step_defaults_scan_frontSide_brief\" : \"Make sure that all the details on the document are visible, free of blur and glare.\",
  \"sns_step_defaults_scan_frontSide_details\" : \"\",
  \"sns_step_defaults_scan_frontSide_title\" : \"Scan the document\",
  \"sns_support_EMAIL_description\" : \"We'll be happy to help.\\nJust send us your question.\",
  \"sns_support_EMAIL_title\" : \"Email us\",
  \"sns_support_subtitle\" : \"You can contact us through the following ways:\",
  \"sns_support_title\" : \"Support\",
  \"sns_support_URL_description\" : \"You can find answers to most of your questions there.\",
  \"sns_support_URL_title\" : \"Visit help center\",
  \"sns_tos_GTC_html\" : \"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n    <meta http-equiv=\\\"X-UA-Compatible\\\" content=\\\"ie=edge\\\">\\n</head>\\n<body><br>\\n<div class=\\\"container\\\">\\n    <h1 class=\\\"title\\\">Consent to Personal Data Processing</h1>\\n    <p class=\\\"info\\\">\\n1. By clicking the “Next Step” button, I hereby acknowledge and express my voluntary, unequivocal and informed consent that my personal data which I am providing or have provided to the <strong>Data Controller</strong> – the organisation with which I wish to establish a business relationship after completion of KYC, <strong>will be processed by Sum and Substance Limited (hereinafter - the “Data Processor”)</strong>, incorporated and registered in England with company number 09688671, whose registered office is at 30 St. Mary Axe, London, United Kingdom, EC3A 8BF in order to verify my identity for the purposes of carrying out customer due diligence procedures in accordance with internal procedures and policies of the Data Controller.<br>\\n2. My name and other means of identification for the purposes of obtaining this consent shall be established in the course of the processing of my personal data carried out in accordance with this consent.<br><br>\\n3. Delegation of personal data processing by the Data Controller<br>\\n3.1. I hereby acknowledge and agree that I know the company details (including address) of the Data Controller.<br>\\n3.2. Any orders, directions or instructions for processing, designation of the purposes of processing, determination of personal data subject to processing and other similar matters are the responsibility of the Data Controller.<br>\\n3.3. I hereby acknowledge and agree that the Data Controller may entrust the processing of my personal data to data processors if it is necessary for the processing purposes as set out above.<br>\\n3.4. I hereby acknowledge and agree that my personal data may be disclosed to entities associated with the Data Controller to achieve the purpose of processing under this consent. The Data Controller guarantees that such entities, as well as other data processors to which it discloses personal data implement appropriate technical and organisational measures to ensure safety of the personal data.<br>\\n4. Data processing methods<br>\\n4.1. I hereby acknowledge and agree that my personal data shall be processed by means of automated text extraction, verification of authenticity/validity and other methods of automated processing of photos and scanned copies of documents.<br>\\n4.2. The consent expressed hereby covers the following processing activities: collection, recording, organisation, structuring, storage, adaptation or alteration, retrieval, consultation, use, disclosure by transmission to the Data Controller and data processors, dissemination or otherwise making available for the performance of a task carried out in the public interest or in the exercise of official authority, transfer (including cross-border transfer, where necessary), alignment or combination, restriction, erasure and destruction.<br>\\n4.3. The personal data may be checked in multiple databases, including International Politically Exposed Persons (PEPs), Sanctions, Country-Specific Sanctions Lists, Criminal Lists, and Financial Lists. It may also be reviewed in media information sources.<br>\\n5. Types of personal data subject to processing<br>\\n5.1. The consent for the processing expressed hereby includes the following personal data:<br>\\n5.1.1. general personal data: full name, sex, personal identification code or number, date of birth, legal capacity, nationality and citizenship, location (street, city, country, postcode);<br>\\n5.1.2. identity document data: document type, issuing country, number, expiry date, MRZ, information embedded into document barcodes (may vary depending on the document), security features;<br>\\n5.1.3. facial image data; photos of face (including selfie images) and photo or scan of face on the identification document, videos, sound recordings;<br>\\n5.1.4. biometrical data: facial features;<br>\\n5.1.5. banking details: card holder name, expiry date, first 6 and last 4 digits of the card number, data extracted from documents provided as proof of source of funds/wealth;<br>\\n5.1.6. contact details: address, e-mail address, phone number, IP address;<br>\\n5.1.7. technical data: information regarding the date, time and activity in the Services; IP address and domain name; software and hardware attributes (camera name and type); general geographic location (e.g., city, country) from Data Subject’s device;<br>\\n5.1.8. unique identifier (Applicant ID) created only for association Data Subject’s and its personal data inside the Informational System;<br>\\n5.1.9. relevant publicly available data: information regarding a person being a Politically Exposed Person (PEP) or included in sanctions lists;<br>\\n5.1.10. personal information that the Data Processor has received from the Controller, such as contact details;<br>\\n5.1.11. personal information additionally provided by Data Subjects, such as data obtained during their communications with the Data Processor (e.g., requests, reports).<br>\\n5.2. I hereby acknowledge and agree that facial images of myself are processed to confirm the liveliness of my face, and/or to confirm that a given identity document is presented by me, its legitimate owner.<br>\\n6. Data subject rights<br>\\n6.1. I hereby represent that I have been informed about my rights to:<br>\\n6.1.1. withdraw this consent to personal data processing;<br>\\n6.1.2. access and adjust my personal data;<br>\\n6.1.3. make a justified demand in writing to suspend the processing of my personal data due to particular reason;<br>\\n6.1.4. object to the processing of my personal data;<br>\\n6.1.5. object to the transfer of my personal data; including the right to object to engaging any third parties to process my personal data;<br>\\n6.1.6. object to being subject to a decision based solely on automated processing/profiling;<br>\\n6.1.7. make a justified demand in writing to erase my personal data subject to applicable laws and regulations;<br>\\n6.1.8. all of which may be exercised by contacting the Data Controller directly or with a respective notice at <i><EMAIL></i>.<br>\\n7. I hereby represent that I have carefully read all of the above provisions and do voluntarily and unequivocally agree with them.<br>\\n</div></body><br>\\n</html>\",
  \"sns_tos_GTC_url\" : \"\",
  \"sns_tos_PP_html\" : \"# Consent to Personal data processing\\n\\n1. By selecting “Next step”, I hereby acknowledge and express my voluntary, unequivocal and informed consent that my personal data which I am providing or have provided to the **Data Controller** – the organisation with which I wish to establish a business relationship after completion of KYC, **will be processed by Sum and Substance Limited (hereinafter - the “Data Processor”)**, incorporated and registered in England with company number 09688671, whose registered office is at 30 St. Mary Axe, London, United Kingdom, EC3A 8BF in order to verify my identity for the purposes of carrying out customer due diligence procedures in accordance with internal procedures and policies of the Data Controller.\\n\\n2. My name and other means of identification for the purposes of obtaining this consent shall be established in the course of processing my personal data carried out in accordance with this consent.\\n\\n3. Delegation of personal data processing by the Data Controller\\n\\n   1. I hereby acknowledge and agree that I know the company details (including address) of the Data Controller.\\n   2. Any orders, directions or instructions for processing, designation of the purposes of processing, determination of personal data subject to processing and other similar matters are the responsibility of the Data Controller.\\n   3. I hereby acknowledge and agree that the Data Controller may entrust the processing of my personal data to data processors if it is necessary for the processing purposes as set out above.\\n   4. I hereby acknowledge and agree that my personal data may be disclosed to entities associated with the Data Controller to achieve the purpose of processing under this consent. The Data Controller guarantees that such entities, as well as other data processors to which it discloses personal data implement appropriate technical and organisational measures to ensure the safety of the personal data.\\n\\n4. Data processing methods\\n\\n   1. I hereby acknowledge and agree that my personal data shall be processed by means of automated text extraction, verification of authenticity/validity and other methods of automated processing of photos and scanned copies of documents.\\n   2. The consent expressed hereby covers the following processing activities: collection, recording, organisation, structuring, storage, adaptation or alteration, retrieval, consultation, use, disclosure by transmission to the Data Controller and data processors, dissemination or otherwise making available for the performance of a task carried out in the public interest or in the exercise of official authority, transfer (including cross-border transfer, where necessary), alignment or combination, restriction, erasure and destruction.\\n   3. The personal data may be checked in multiple databases, including International Politically Exposed Persons (PEPs), Sanctions, Country-Specific Sanctions Lists, Criminal Lists, and Financial Lists. It may also be reviewed in media information sources.\\n\\n5. Types of personal data subject to processing\\n\\n   1. The consent for the processing expressed hereby includes the following personal data:\\n      - general personal data: full name, sex, personal identification code or number, date of birth, legal capacity, nationality and citizenship, location (street, city, country, postcode);\\n      - identity document data: document type, issuing country, number, expiry date, MRZ, information embedded into document barcodes (may vary depending on the document), security features;\\n      - facial image data; photos of a face (including selfie images) and photo or scan of a face on the identification document, videos, sound recordings;\\n      - biometrical data: facial features;\\n      - banking details: card holder name, expiry date, first 6 and last 4 digits of the card number, data extracted from documents provided as proof of source of funds/wealth;\\n      - contact details: address, e-mail address, phone number, IP address;\\n      - technical data: information regarding the date, time and activity in the Services; IP address and domain name; software and hardware attributes (camera name and type); general geographic location (e.g., city, country) from Data Subject’s device;\\n      - unique identifier (Applicant ID) created only for association Data Subject’s and its personal data inside the Informational System;\\n      - relevant publicly available data: information regarding a person being a Politically Exposed Person (PEP) or included in sanctions lists;\\n      - personal information that the Data Processor has received from the Controller, such as contact details;\\n      - personal information additionally provided by Data Subjects, such as data obtained during their communications with the Data Processor (e.g., requests, reports).\\n   2. I hereby acknowledge and agree that facial images of myself are processed to confirm the liveliness of my face, and/or to confirm that a given identity document is presented by me, its legitimate owner.\\n\\n6. Data subject rights\\n\\n   1. I hereby represent that I have been informed about my rights to:\\n      - withdraw this consent to personal data processing;\\n      - access and adjust my personal data;\\n      - make a justified demand in writing to suspend the processing of my personal data due to particular reason;\\n      - object to the processing of my personal data;\\n      - object to the transfer of my personal data; including the right to object to engaging any third parties to process my personal data;\\n      - object to being subject to a decision based solely on automated processing/profiling;\\n      - make a justified demand in writing to erase my personal data subject to applicable laws and regulations;\\n      - all of which may be exercised by contacting the Data Controller directly or with a respective notice at *<EMAIL>*.\\n\\n7. I hereby represent that I have carefully read all of the above provisions and do voluntarily and unequivocally agree with them.\",
  \"sns_tos_PP_url\" : \"\",
  \"sns_tos_action_accept\" : \"I agree\",
  \"sns_videoident_alert_aboutToExit\" : \"You're about to exit the verification process. Are you sure you want to exit?\",
  \"zoom_accessibility_cancel_button\" : \"Cancel\",
  \"zoom_action_im_ready\" : \"I'm ready\",
  \"zoom_action_ok\" : \"OK\",
  \"zoom_camera_permission_enable_camera\" : \"Turn on camera\",
  \"zoom_camera_permission_header\" : \"Turn on camera\",
  \"zoom_camera_permission_launch_settings\" : \"Open Settings\",
  \"zoom_camera_permission_message_auth\" : \"The app needs access to your camera. Allow access in Settings.\",
  \"zoom_camera_permission_message_enroll\" : \"Please turn on your selfie camera.\",
  \"zoom_feedback_center_face\" : \"Position your face in the center\",
  \"zoom_feedback_face_not_found\" : \"Position your face in the center\",
  \"zoom_feedback_face_not_looking_straight_ahead\" : \"Look at the camera\",
  \"zoom_feedback_face_not_upright\" : \"Keep your head up\",
  \"zoom_feedback_hold_steady\" : \"Don't move\",
  \"zoom_feedback_move_phone_away\" : \"Move back\",
  \"zoom_feedback_move_phone_closer\" : \"Move closer\",
  \"zoom_feedback_move_phone_even_closer\" : \"Even closer\",
  \"zoom_feedback_move_phone_to_eye_level\" : \"Raise your chin\",
  \"zoom_feedback_use_even_lighting\" : \"Too dark\",
  \"zoom_instructions_header_ready\" : \"Get ready for\\nyour video selfie\",
  \"zoom_instructions_message_ready\" : \"Fit your face in\\nthe small oval, and then the big oval\",
  \"zoom_result_facemap_upload_message\" : \"Processing\",
  \"zoom_retry_header\" : \"Let's try that again\",
  \"zoom_retry_ideal_image_label\" : \"Sample image\",
  \"zoom_retry_instruction_message_1\" : \"Neutral expression, no smiling\",
  \"zoom_retry_instruction_message_2\" : \"No glare or poor lighting\",
  \"zoom_retry_instruction_message_3\" : \"\",
  \"zoom_retry_subheader_message\" : \"Check your photo and see what needs to be corrected.\",
  \"zoom_retry_your_image_label\" : \"Your selfie\",
  \"sns_alert_action_dont_show\" : \"Do not show\",
  \"sns_instructions_action_seeMoreGuidance\" : \"See more guidance\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_title\" : \"Take a photo of your document\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_title::PASSPORT\" : \"Take a photo of your passport\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_doImage\" : \"default/do_idCard\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_doImage::PASSPORT\" : \"default/do_passport\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_subtitle\" : \"Make sure the document is fully visible and all information is readable. Some more info here.\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_doHeader\" : \"Please do\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_doText\" : \"- Capture all four corners of the document\\n- Ensure all fields and text are clearly readable\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_dontImage\" : \"default/dont_idCard\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_dontImage::PASSPORT\" : \"default/dont_passport\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_dontHeader\" : \"Please don’t\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_dontText\" : \"- Capture only one section of the document \\n- Use screenshots or printed images\",
  \"sns_step_IDENTITY_scan_frontSide_instructions_action_continue\" : \"Take photo\",
  \"sns_step_SELFIE_facescan_instructions_title\" : \"Get ready for Liveness check\",
  \"sns_step_SELFIE_facescan_instructions_image\" : \"default/facescan\",
  \"sns_step_SELFIE_facescan_instructions_subtitle\" : \"We will open camera to analyze your face for verification purposes.\",
  \"sns_step_SELFIE_facescan_instructions_header\" : \"Follow these steps\",
  \"sns_step_SELFIE_facescan_instructions_text\" : \"- Make sure that it’s just your face in the frame\\n\\n- Take off your sunglasses, hat or any items that obstruct your face\\n\\n- Make sure your face isn’t backlit by a light source that could darken the image of your face\",
  \"sns_step_SELFIE_facescan_instructions_action_continue\" : \"Continue\",
  \"sns_step_defaults_videoident_frontSide_title\" : \"Show the document\",
  \"sns_step_defaults_videoident_frontSide_text\" : \"Make sure that all the details on the document are visible, free of blur and glare.\",
  \"sns_step_defaults_videoident_backSide_title\" : \"Show the next page of the document\",
  \"sns_step_defaults_videoident_backSide_text\" : \"Make sure that all the details on the document are visible, free of blur and glare.\",
  \"sns_step_IDENTITY_videoident_frontSide_title\" : \"Front side of ID\",
  \"sns_step_IDENTITY_videoident_frontSide_text\" : \"Show the front side of your identity document.\",
  \"sns_step_IDENTITY_videoident_backSide_title\" : \"Back side of ID\",
  \"sns_step_IDENTITY_videoident_backSide_text\" : \"Show the back side of your document.\",
  \"sns_step_PROOF_OF_RESIDENCE_videoident_frontSide_title\" : \"Proof of address\",
  \"sns_step_PROOF_OF_RESIDENCE_videoident_frontSide_text\" : \"Show your proof of address. The document should contain your full name, address and must not be older than 3 month(s).\",
  \"sns_step_SELFIE_videoident_title\" : \"Selfie\",
  \"sns_step_SELFIE_videoident_text\" : \"Look straight and position your face to fit the camera frame.\",
  \"sns_videoident_state_checkingCamera_title\" : \"Checking the camera\",
  \"sns_videoident_state_checkingCamera_text\" : \"You'll be connected with one of our experts who will guide you through the identification process via live video. Be prepared to show your identity document.\",
  \"sns_videoident_state_followIntructions_title\" : \"Video identification\",
  \"sns_videoident_state_followIntructions_text\" : \"Our expert will guide you through each step of the process. Simply follow their instructions.\",
  \"sns_videoident_state_connecting\" : \"Connecting\",
  \"sns_videoident_state_uploading\" : \"Uploading\",
  \"sns_videoident_warning_waitForConnect\" : \"We are currently experiencing a high volume of calls. Please stay on the line. Your call will be answered as soon as possible. Thank you for your patience.\",
  \"sns_videoident_warning_waitForConnect_adaptive\" : \"We are currently experiencing a high volume of calls. Please stay on the line. Your call will be answered as soon as possible. Thank you for your patience.\",
  \"sns_videoident_notification_waitingForExpert\" : \"Waiting for an identification expert\",
  \"sns_videoident_notification_expertJoined\" : \"The identification expert has joined the call\",
  \"sns_videoident_error_connectionFailed_title\" : \"No connection\",
  \"sns_videoident_error_connectionFailedFatal\" : \"We are unable to connect at the moment. Please try again later.\",
  \"sns_videoident_error_connectionFailedNetwork\" : \"The network is down. Please check your network connection and try again.\",
  \"sns_videoident_error_connectionLost_title\" : \"Connection is lost\",
  \"sns_videoident_error_connectionLostFatal\" : \"Please try calling again.\",
  \"sns_videoident_error_connectionLostNetwork\" : \"The network is down. Please check your network connection.\",
  \"sns_videoident_error_uploadFailed_title\" : \"Upload failed\",
  \"sns_videoident_error_uploadFailedFatal\" : \"Please try again.\",
  \"sns_videoident_error_uploadFailedNetwork\" : \"The network is down. Please check your network connection.\",
  \"sns_videoident_action_start\" : \"Start call\",
  \"sns_videoident_action_callAgain\" : \"Call again\",
  \"sns_videoident_action_pickUp\" : \"Upload {doctype}\",
  \"sns_videoident_action_retry\" : \"Try again\",
  \"sns_videoident_action_upload\" : \"Upload\",
  \"sns_videoident_action_cancel\" : \"Cancel\",
  \"sns_step_VIDEO_IDENT_title\" : \"Video identification\",
  \"sns_step_VIDEO_IDENT_videoident_instructions_title\" : \"Video identification\",
  \"sns_step_VIDEO_IDENT_videoident_instructions_subtitle\" : \"You will have a video call with an operator. It won't take long\",
  \"sns_step_VIDEO_IDENT_videoident_instructions_image\" : \"default/videoident\",
  \"sns_step_VIDEO_IDENT_videoident_instructions_header\" : \"Prepare for the video call\",
  \"sns_step_VIDEO_IDENT_videoident_instructions_text\" : \"- Make sure: The room is well-lit\\n\\n- There is nobody in the room\\n\\n- Your device's camera is uncovered and working\\n\\n- Call will be handled in English or German language\",
  \"sns_step_VIDEO_IDENT_videoident_instructions_action_continue\" : \"Continue\",
  \"sns_alert_aboutToExitVerification\" : \"You're about to exit the verification process. Are you sure you want to exit?\",
  \"sns_videoident_langPanel_text\" : \"The language you speak\",
  \"sns_videoident_langPanel_action_change\" : \"Change\",
  \"sns_videoident_langSelector_title\" : \"Select the language you speak\",
  \"sns_videoident_langSelector_waitTime_prefix\" : \"\",
  \"sns_videoident_langSelector_waitTime_text\" : \"\",
  \"sns_step_VIDEO_IDENT_videoident_instructions_definitionKey\" : \"instructions.videoident\",
  \"sns_seamless_error_notEnoughStorage\" : \"There is not enough available storage to complete the document capture.\",
  \"sns_seamless_error_timeExceeded\" : \"The allowed session time has been exceeded. Please start again.\",
  \"sns_seamless_error_general\" : \"Something went wrong. Please start again.\",
  \"sns_eid_pinSelection_title\" : \"ID Card and PIN\",
  \"sns_eid_pinSelection_subtitle\" : \"Please prepare your German ID Card* and choose whether you have a 5- or 6-digit PIN.\\n\\n*Alternatively, a Residence Permit or an eID Card for EU citizens.\",
  \"sns_eid_pinSelection_action_pinInfo\" : \"Which PIN should I select?\",
  \"sns_eid_pinSelection_action_changeTransportPin\" : \"5-digit Activation PIN\",
  \"sns_eid_pinSelection_action_proceedToIdent\" : \"6-digit PIN\",
  \"sns_eid_pinInfo_text\" : \"# Which PIN should I select?\\n## First-time users\\nIf you are using the eID function for the very first time, please follow these simple steps:\\n1. Proceed with the **5-digit Activation PIN** option.\\n2. Enter the 5-digit Activation PIN, which was sent to you via mail by the local authorities.\\n3. Choose a new 6-digit PIN, which will be enabled for future use.\\n## I already have a 6-digit PIN\\nGreat! Simply proceed with the **6-digit PIN** option and follow the verification instructions. Do not share this code with anyone. If you have forgotten the 6-digit PIN, please contact your local authorities to have it changed.\",
  \"sns_eid_identInfo_title\" : \"eID Verification\",
  \"sns_eid_identInfo_subtitle\" : \"The process is performed over a secure connection, encrypting your shared personal data.\",
  \"sns_eid_identInfo_action_continue\" : \"Continue\",
  \"sns_eid_identInfo_action_learnMore\" : \"Learn more\",
  \"sns_eid_serviceInfo_title\" : \"Data Processing Notice\",
  \"sns_eid_serviceInfo_fieldsHeader\" : \"The following information will be read from your ID Card to complete the user verification process:\",
  \"sns_eid_nfcScan_title\" : \"Scan your ID Card\",
  \"sns_eid_nfcScan_subtitle\" : \"Hold the ID Card to the top edge of your device\",
  \"sns_eid_nfcScan_hint_readyToStart\" : \"Tap \\\"Start scanning\\\", then place your ID card near the top of your device and keep it there until the process is complete.\",
  \"sns_eid_nfcScan_hint_scanning\" : \"Scanning...\",
  \"sns_eid_nfcScan_hint_processing\" : \"Processing...\",
  \"sns_eid_nfcScan_hint_searching\" : \"Waiting for card...\",
  \"sns_eid_nfcScan_error_cardLost\" : \"Scanning is failed. Please try again.\",
  \"sns_eid_nfcScan_action_start\" : \"Start scanning\",
  \"sns_eid_canRequired_title\" : \"CAN is required\",
  \"sns_eid_canRequired_subtitle\" : \"CAN entry is required to allow the last PIN attempt.\\n\\nPlease find the CAN number at the lower right corner of the front side of your ID card - there are 6 digits printed in bold there.\",
  \"sns_eid_canRequired_action\" : \"Enter CAN\",
  \"sns_eid_unlockSuccess_title\" : \"Unlocking the ID card was successful\",
  \"sns_eid_unlockSuccess_subtitle\" : \"You can now use your PIN again.\",
  \"sns_eid_unlockSuccess_action_continue\" : \"Continue\",
  \"sns_eid_pinChangeSuccess_title\" : \"PIN setup successful!\",
  \"sns_eid_pinChangeSuccess_subtitle\" : \"You have successfully set up a new 6-digit PIN! It can now be used to complete your identity verification.\",
  \"sns_eid_pinChangeSuccess_action_continue\" : \"Continue\",
  \"sns_eid_pinpad_pin_title\" : \"Enter your 6-digit PIN\",
  \"sns_eid_pinpad_pin_subtitle\" : \"\",
  \"sns_eid_pinpad_transportPin_title\" : \"Enter your Activation PIN\",
  \"sns_eid_pinpad_transportPin_subtitle\" : \"\",
  \"sns_eid_pinpad_newPin_title\" : \"Create a new 6-digit PIN\",
  \"sns_eid_pinpad_newPin_subtitle\" : \"Please make sure to write down and remember your new PIN for future use. Do not share it with anyone.\",
  \"sns_eid_pinpad_newPinRepeat_title\" : \"Repeat your new 6-digit PIN\",
  \"sns_eid_pinpad_newPinRepeat_subtitle\" : \"Please enter the exact same PIN once more to complete the process.\",
  \"sns_eid_pinpad_can_title\" : \"Enter your 6-digit CAN\",
  \"sns_eid_pinpad_can_subtitle\" : \"CAN is a 6-digit number printed on the bottom right corner of the front side of your ID Card.\",
  \"sns_eid_pinpad_puk_title\" : \"Unlock with PUK\",
  \"sns_eid_pinpad_puk_subtitle\" : \"Please enter 10-digit PUK number to unlock your card\",
  \"sns_eid_error_newPinMismatch_title\" : \"The numbers do not match\",
  \"sns_eid_error_newPinMismatch_subtitle\" : \"Please enter the new PIN again.\",
  \"sns_eid_error_newPinMismatch_action\" : \"Re-enter new PIN\",
  \"sns_eid_error_wrongTransportPin_title\" : \"Incorrect PIN\",
  \"sns_eid_error_wrongTransportPin_subtitle_firstAttempt\" : \"You have two more attempts before your card is locked.\\n\\nIf you have already changed Transport PIN, please use \\\"6-digit PIN\\\" option at the next screen.\",
  \"sns_eid_error_wrongTransportPin_subtitle_lastAttempt\" : \"You have one LAST attempt before your card is locked!\\n\\nIf you have already changed Transport PIN, please use \\\"6-digit PIN\\\" option at the next screen.\",
  \"sns_eid_error_wrongTransportPin_action\" : \"Continue\",
  \"sns_eid_error_wrongCan_title\" : \"Incorrect CAN\",
  \"sns_eid_error_wrongCan_subtitle\" : \"Please make sure you are entering the correct CAN number - 6 digits printed in bold at the lower right corner of the front side of your ID card.\",
  \"sns_eid_error_wrongCan_action\" : \"Re-enter CAN\",
  \"sns_eid_error_wrongPin_title\" : \"Incorrect PIN\",
  \"sns_eid_error_wrongPin_subtitle_firstAttempt\" : \"You have entered an incorrect PIN. You have two more attempts to enter your PIN correctly before your ID Card is locked.\",
  \"sns_eid_error_wrongPin_subtitle_lastAttempt\" : \"You have entered an incorrect PIN twice. You have one more attempt to enter your PIN correctly before your ID Card is locked. Unlocking it will require entering a PUK code.\",
  \"sns_eid_error_wrongPin_action\" : \"Re-enter PIN\",
  \"sns_eid_error_cardLocked_title\" : \"ID Card is locked!\",
  \"sns_eid_error_cardLocked_subtitle\" : \"Your ID Card is locked for security reasons. You can unlock it using a 10-digit Personal Unblocking Key (PUK), which was sent to you via mail by the local authorities. Please note that the PUK can be used no more than ten times.\",
  \"sns_eid_error_cardLocked_action\" : \"Enter PUK\",
  \"sns_eid_error_cardDeactivated_title\" : \"ID card is deactivated\",
  \"sns_eid_error_cardDeactivated_subtitle\" : \"The eID function of your card is deactivated and cannot be used. The function can be activated at the responsible authority.\",
  \"sns_eid_error_wrongPuk_title\" : \"Incorrect PUK\",
  \"sns_eid_error_wrongPuk_subtitle\" : \"You have entered an incorrect PUK. If you cannot find the correct key, please contact your local authorities.\",
  \"sns_eid_error_wrongPuk_action\" : \"Re-enter PUK\",
  \"sns_eid_error_title\" : \"An error has occurred.\",
  \"sns_eid_error_timeout\" : \"The waiting time has expired. Please start the process again.\",
  \"sns_eid_error_noEidCard\" : \"No compatible ID card was recognized. Please try again with your ID card that has the online ID function activated.\",
  \"sns_eid_error_incompatibleVersion\" : \"This is an deprecated version of the app. Please update to the newest version of the app.\",
  \"sns_eid_error_incompatibleDevice\" : \"Unfortunately, the device is not compatible with the online functionality of your ID card.\",
  \"sns_step_E_SIGN_title\" : \"Document signing\",
  \"sns_step_E_SIGN_prompt\" : \"Sign documents using a qualified signature\",
  \"sns_esign_agreement_title\" : \"Document signing\",
  \"sns_esign_agreement_subtitle\" : \"Confirm the following to request your digital certificate and continue with document signing\",
  \"sns_esign_agreement_action_continue\" : \"Continue\",
  \"sns_esign_documents_title\" : \"Document signing\",
  \"sns_esign_documents_subtitle\" : \"Tick the required box and continue with OTP authentication to finish signing. A unique code will be sent to you via SMS.\",
  \"sns_esign_documents_hint_downloading\" : \"Downloading\",
  \"sns_esign_documents_action_continue\" : \"Finish signing\",
  \"sns_confirmation_code_esign_title\" : \"One-time password (OTP)\",
  \"sns_confirmation_code_esign_subtitle\" : \"Enter the OTP sent to\\n{phone} via SMS\",
  \"sns_confirmation_result_esign_failure_title\" : \"Something went wrong.\",
  \"sns_confirmation_result_esign_submitted_title\" : \"Your data has been submitted successfully!\",
  \"sns_support_caseId\" : \"Your case ID\",
  \"sns_general_action_copy\" : \"Copy\",
  \"sns_general_copiedToClipboard\" : \"Copied to clipboard\",
  \"sns_status_PENDING_title_noSteps\" : \"We are processing your data\",
  \"sns_status_PENDING_subtitle_noSteps\" : \"The status will change automatically once the review is complete.\",
  \"sns_status_PENDING_footerHtml_noSteps\" : \"If you experience any issues, contact <a href='support'>support</a>.\",
  \"sns_exit_survey_title\" : \"Why are you leaving?\",
  \"sns_exit_survey_option_dontWantProvideDocuments\" : \"I don't want to provide documents\",
  \"sns_exit_survey_option_dontHaveDocumentWithMe\" : \"I don't have documents with me\",
  \"sns_exit_survey_option_cantTakeGoodPhoto\" : \"Can't take a good photo\",
  \"sns_exit_survey_option_cantTakeSelfie\" : \"Can't take a selfie\",
  \"sns_exit_survey_option_technicalIssues\" : \"Technical issues\",
  \"sns_exit_survey_option_networkIssues\" : \"Problems with internet connection\",
  \"sns_exit_survey_option_other\" : \"Other\",
  \"sns_exit_survey_action_continue\" : \"Continue\",
  \"sns_exit_survey_action_skip\" : \"Skip\",
  \"sns_exit_survey_thanks\" : \"Thank you for your feedback\",
  \"sns_alert_nfcIsNotSupported\" : \"NFC is not supported on this device.\",
  \"sns_alert_nfcIsNotActive\" : \"NFC is not active. Please enable NFC and try again.\",
  \"sns_dialog_nfc_title\" : \"NFC is required for this action\",
  \"sns_dialog_nfc_message\" : \"Enable NFC in the Settings to continue\",
  \"sns_dialog_nfc_action_settings\" : \"Open NFC Settings\",
  \"sns_step_IDENTITY_scan_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY_scan_frontSide_brief::PASSPORT\" : \"Take a photo of the passport page with your photo.\",
  \"sns_step_IDENTITY_scan_frontSide_details::PASSPORT\" : \"\\nTake a photo of the passport page with your photo.\\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY_videoident_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY_videoident_frontSide_text::PASSPORT\" : \"Show the passport page with your photo.\",
  \"sns_step_IDENTITY2_scan_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY2_scan_frontSide_brief::PASSPORT\" : \"Take a photo of the passport page with your photo.\",
  \"sns_step_IDENTITY2_scan_frontSide_details::PASSPORT\" : \"\\nTake a photo of the passport page with your photo.\\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY2_videoident_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY2_videoident_frontSide_text::PASSPORT\" : \"Show the passport page with your photo.\",
  \"sns_step_IDENTITY3_scan_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY3_scan_frontSide_brief::PASSPORT\" : \"Take a photo of the passport page with your photo.\",
  \"sns_step_IDENTITY3_scan_frontSide_details::PASSPORT\" : \"\\nTake a photo of the passport page with your photo.\\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY3_videoident_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY3_videoident_frontSide_text::PASSPORT\" : \"Show the passport page with your photo.\",
  \"sns_step_IDENTITY4_scan_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY4_scan_frontSide_brief::PASSPORT\" : \"Take a photo of the passport page with your photo.\",
  \"sns_step_IDENTITY4_scan_frontSide_details::PASSPORT\" : \"\\nTake a photo of the passport page with your photo.\\n\\nAll the information should be readable, without any reflections or blur.\\n\",
  \"sns_step_IDENTITY4_videoident_frontSide_title::PASSPORT\" : \"Passport page with your photo\",
  \"sns_step_IDENTITY4_videoident_frontSide_text::PASSPORT\" : \"Show the passport page with your photo.\",
  \"sns_autocapture_hint_takingPhoto\" : \"Taking photo...\",
  \"sns_esign_documents_pages\" : \"Page(s) to be signed: {pages}\",
  \"sns_sumsubid_banner_title\" : \"Speed up your verification with Sumsub ID\",
  \"sns_sumsubid_banner_subtitle\" : \"Sumsub ID allows you to re-use your securely stored data to speed up the verification process\",
  \"sns_sumsubid_agreement_title\" : \"Save your data with Sumsub ID\",
  \"sns_sumsubid_agreement_subtitle\" : \"Securely store your data in a Sumsub ID account to make future identity verification faster and easier.\",
  \"sns_sumsubid_agreement_option_privacy\" : \"I consent to the processing of my biometric data in accordance with the [Privacy User Consent](https://id.sumsub.com/consent) and [Privacy Notice](https://sumsub.com/privacy-notice-service)\",
  \"sns_sumsubid_agreement_benefit_instantly\" : \"Speed through verifications quickly and hassle-free\",
  \"sns_sumsubid_agreement_benefit_securely\" : \"Securely store and manage your data\",
  \"sns_sumsubid_agreement_benefit_optionally\" : \"Decide when to share your data during verification\",
  \"sns_sumsubid_agreement_footer\" : \"By clicking \\\"Create my account\\\" I confirm that I have also read and agreed to the [Terms and conditions](https://id.sumsub.com/terms)\",
  \"sns_sumsubid_agreement_action_continue\" : \"Create my account\",
  \"sns_sumsubid_agreement_action_skip\" : \"Not now\",
  \"sns_sumsubid_reuse_title\" : \"Welcome back, {name}\",
  \"sns_sumsubid_reuse_title_noname\" : \"Welcome back\",
  \"sns_sumsubid_reuse_subtitle\" : \"Select the data you want to share for verification\",
  \"sns_sumsubid_reuse_doc_address_label\" : \"Address\",
  \"sns_sumsubid_reuse_doc_address_none\" : \"None of them\",
  \"sns_sumsubid_reuse_data_address_label\" : \"Address\",
  \"sns_sumsubid_reuse_data_address_none\" : \"None of the addresses\",
  \"sns_sumsubid_reuse_footer\" : \"By selecting \\\"Share selected data\\\", I confirm that the information provided is accurate and up-to-date.\",
  \"sns_sumsubid_reuse_action_continue\" : \"Share selected data\",
  \"sns_sumsubid_reuse_noData_title\" : \"You don't have any reusable data in your Sumsub ID account\",
  \"sns_sumsubid_reuse_noData_subtitle\" : \"Complete the verification to save your data and use it for 1-click verification next time\",
  \"sns_sumsubid_reuse_noData_action_continue\" : \"Continue with verification\",
  \"sns_sumsubid_reuse_action_profile\" : \"Go to profile\",
  \"sns_sumsubid_reuse_action_logout\" : \"Log out\",
  \"sns_sumsubid_final_failure_title\" : \"We couldn't verify your email.\",
  \"sns_sumsubid_final_dataProcessed_title\" : \"Your data has been processed\",
  \"sns_sumsubid_final_accountCreated_title\" : \"Sumsub ID account successfully created\",
  \"sns_sumsubid_final_accountCreated_subtitle\" : \"Next time you need to verify with a Sumsub-supported app, you can use the data from your current verification.\",
  \"sns_confirmation_code_sumsubid_title\" : \"Verify your email to use Sumsub ID\",
  \"sns_confirmation_code_sumsubid_subtitle\" : \"Enter the verification code we sent to {email}.\",
  \"sns_confirmation_contact_sumsubid_title\" : \"Provide your email address to use Sumsub ID\",
  \"sns_confirmation_contact_sumsubid_subtitle\" : \"\",
  \"sns_poa_form_title\" : \"Enter your address\",
  \"sns_poa_form_subtitle\" : \"Please provide us with your residential address details.\"
}"}
 LOG  onLog EVENT -> {"level": 4, "message": "GET https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one"}
 LOG  onLog EVENT -> {"level": 3, "message": "GET https://api.sumsub.com/resources/applicants/6835990618bd4ccb0bbca247/one (got 200)
response.body={
  \"id\" : \"6835990618bd4ccb0bbca247\",
  \"createdAt\" : \"2025-05-27 10:50:46\",
  \"key\" : \"MDLCNHMYVUSNXQ\",
  \"clientId\" : \"ds_partners_115308\",
  \"inspectionId\" : \"6835990618bd4ccb0bbca247\",
  \"externalUserId\" : \"fb6a589e-3b55-4197-ba96-8b91081b0d07\",
  \"email\" : \"<EMAIL>\",
  \"applicantPlatform\" : \"Android\",
  \"agreement\" : {
    \"items\" : [ {
      \"id\" : \"23eb88b6-d782-437e-b1ec-009d4f082eab\",
      \"acceptedAt\" : \"2025-05-27 11:03:05\",
      \"source\" : \"msdk2\",
      \"type\" : \"onboarding\",
      \"recordIds\" : [ \"678639c2f26d8087f1ec2d04\" ]
    } ],
    \"createdAt\" : \"2025-05-27 11:03:05\",
    \"acceptedAt\" : \"2025-05-27 11:03:05\",
    \"source\" : \"msdk2\",
    \"recordIds\" : [ \"678639c2f26d8087f1ec2d04\" ]
  },
  \"requiredIdDocs\" : {
    \"docSets\" : [ {
      \"idDocSetType\" : \"COMPANY_DATA\",
      \"fields\" : [ {
        \"name\" : \"companyName\",
        \"required\" : true,
        \"prefill\" : false,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"country\",
        \"required\" : true,
        \"prefill\" : false,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"registrationNumber\",
        \"required\" : true,
        \"prefill\" : false,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"type\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"registrationLocation\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"legalAddress\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"postalAddress\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"taxId\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"incorporatedOn\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"email\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"phone\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      }, {
        \"name\" : \"website\",
        \"required\" : false,
        \"prefill\" : true,
        \"immutableIfPresent\" : null
      } ]
    }, {
      \"idDocSetType\" : \"COMPANY_BENEFICIARIES\",
      \"companyBeneficiaryDefinitions\" : [ {
        \"category\" : \"representatives\",
        \"canSkip\" : false,
        \"individual\" : {
          \"enabled\" : true,
          \"levelName\" : \"tier1\",
          \"fields\" : [ {
            \"name\" : \"firstName\",
            \"required\" : true,
            \"prefill\" : true,
            \"immutableIfPresent\" : null
          }, {
            \"name\" : \"lastName\",
            \"required\" : true,
            \"prefill\" : true,
            \"immutableIfPresent\" : null
          }, {
            \"name\" : \"middleName\",
            \"required\" : false,
            \"prefill\" : true,
            \"immutableIfPresent\" : null
          }, {
            \"name\" : \"dob\",
            \"required\" : false,
            \"prefill\" : true,
            \"immutableIfPresent\" : null
          }, {
            \"name\" : \"email\",
            \"required\" : true,
            \"prefill\" : false,
            \"immutableIfPresent\" : null
          }, {
            \"name\" : \"phone\",
            \"required\" : true,
            \"prefill\" : false,
            \"immutableIfPresent\" : null
          } ],
          \"customFields\" : null
        }
      } ]
    }, {
      \"idDocSetType\" : \"COMPANY_DOCUMENTS\",
      \"subTypes\" : [ \"INCORPORATION_CERT\", \"INCORPORATION_ARTICLES\", \"STATE_REGISTRY\" ],
      \"companyDocsGroupDefinitions\" : [ {
        \"label\" : \"legalPresence\",
        \"subTypes\" : [ \"INCORPORATION_CERT\", \"INCORPORATION_ARTICLES\", \"STATE_REGISTRY\" ],
        \"required\" : true
      } ]
    } ],
    \"kybSettings\" : {
      \"shareholderThreshold\" : 5.0,
      \"uboThreshold\" : 25.0,
      \"disableCompanySearchAndPrefill\" : null
    }
  },
  \"review\" : {
    \"reviewId\" : \"PjnCd\",
    \"attemptId\" : \"yPxVm\",
    \"attemptCnt\" : 0,
    \"levelName\" : \"kybtier1\",
    \"levelAutoCheckMode\" : null,
    \"createDate\" : \"2025-05-27 10:50:46+0000\",
    \"reviewStatus\" : \"init\",
    \"priority\" : 0
  },
  \"lang\" : \"en\",
  \"type\" : \"company\",
  \"creationInfo\" : null
}"}
 LOG  onLog EVENT -> {"level": 4, "message": "GET https://api.sumsub.com/resources/sdk/applicant/requiredIdDocsStatus"}
 LOG  onLog EVENT -> {"level": 3, "message": "GET https://api.sumsub.com/resources/sdk/applicant/requiredIdDocsStatus (got 200)
response.body={
  \"inspectionReview\" : {
    \"reviewId\" : \"PjnCd\",
    \"attemptId\" : \"yPxVm\",
    \"attemptCnt\" : 0,
    \"levelName\" : \"kybtier1\",
    \"levelAutoCheckMode\" : null,
    \"createDate\" : \"2025-05-27 10:50:46+0000\",
    \"reviewStatus\" : \"init\",
    \"priority\" : 0
  },
  \"requiredIdDocsStatus\" : {
    \"COMPANY_DATA\" : null,
    \"COMPANY_BENEFICIARIES\" : null,
    \"COMPANY_DOCUMENTS\" : null
  }
}"}
 LOG  onLog EVENT -> {"level": 3, "message": "sdk.applicant: applicantId=6835990618bd4ccb0bbca247 externalUserId=fb6a589e-3b55-4197-ba96-8b91081b0d07 lang=en reviewStatus=init"}
 LOG  onLog EVENT -> {"level": 3, "message": "sdk.status: [Initial]"}
 LOG  onStatusChanged EVENT -> {"newStatus": "Initial", "prevStatus": "Ready"}
 LOG  📊 Sumsub status change: {"newStatus": "Initial", "prevStatus": "Ready"}
 LOG  onLog EVENT -> {"level": 3, "message": "Step [COMPANY_DATA]: started"}
 LOG  onLog EVENT -> {"level": 1, "message": "Unsupported step for idDocSet with type 'COMPANY_DATA' Error Domain=SNSErrorDomain Code=-1017 \"Unsupported step\" UserInfo={NSLocalizedDescription=Unsupported step}"}
 LOG  onLog EVENT -> {"level": 4, "message": "POST https://api.sumsub.com/resources/tracking/trackEvents
request.body=[{\"activity\":\"msdk:init\",\"payload\":{\"hasMrtdModule\":true,\"hasVideoIdentModule\":true,\"openSSLVersion\":\"30300030\",\"isEIDAvailable\":true,\"source\":\"msdk\",\"hasEIDModule\":true,\"isVideoIdentAvailable\":true,\"isMrtdAvailable\":true,\"levelName\":\"kybtier1\",\"enabledFFs\":[\"iosAllowFaceScanFrameCalibration\"]},\"ts\":\"2025-05-27 11:06:39 GMT\"},{\"activity\":\"user:opened:screen\",\"payload\":{\"isSumsubIdPromoAttached\":false,\"screenName\":\"statusScreen\",\"screenState\":\"loading\",\"stepName\":\"Status\",\"enabledFFs\":[\"iosAllowFaceScanFrameCalibration\"],\"levelName\":\"kybtier1\",\"isSumsubIdRequested\":false,\"source\":\"msdk\"},\"ts\":\"2025-05-27 11:06:40 GMT\"},{\"activity\":\"user:opened:screen\",\"payload\":{\"isSumsubIdPromoAttached\":false,\"source\":\"msdk\",\"levelName\":\"kybtier1\",\"screenName\":\"statusScreen\",\"screenState\":\"initial\",\"isSumsubIdRequested\":false,\"stepName\":\"Status\",\"enabledFFs\":[\"iosAllowFaceScanFrameCalibration\"]},\"ts\":\"2025-05-27 11:06:40 GMT\"},{\"ts\":\"2025-05-27 11:06:43 GMT\",\"payload\":{\"enabledFFs\":[\"iosAllowFaceScanFrameCalibration\"],\"source\":\"msdk\",\"isSumsubIdPromoAttached\":false,\"levelName\":\"kybtier1\",\"isSumsubIdRequested\":false,\"objectName\":\"continueButton\",\"screenName\":\"statusScreen\",\"stepName\":\"Status\",\"screenState\":\"initial\"},\"activity\":\"user:clicked:button\"},{\"ts\":\"2025-05-27 11:06:43 GMT\",\"payload\":{\"levelName\":\"kybtier1\",\"enabledFFs\":[\"iosAllowFaceScanFrameCalibration\"],\"screenName\":\"statusScreen\",\"stepName\":\"COMPANY_DATA\",\"source\":\"msdk\",\"screenState\":\"initial\"},\"activity\":\"user:started:step\"},{\"activity\":\"user:opened:screen\",\"payload\":{\"levelName\":\"kybtier1\",\"screenName\":\"oopsFatalScreen\",\"enabledFFs\":[\"iosAllowFaceScanFrameCalibration\"],\"country\":\"SRB\",\"source\":\"msdk\",\"isWordless\":false,\"error\":\"Unsupported step\",\"stepName\":\"COMPANY_DATA\"},\"ts\":\"2025-05-27 11:06:43 GMT\"}]"}
 LOG  onLog EVENT -> {"level": 3, "message": "POST https://api.sumsub.com/resources/tracking/trackEvents (got 200)
response.body={\"ok\": 1}"}