// Auth input types
export enum RegistrationTypeOption {
  KYC = "individual",
  KYB = "company",
}

export enum SUMSUB_LEVEL {
  TIER0 = "tier0",
  TIER1 = "tier1",
  TIER2 = "tier2",
  TIER3 = "tier3",
  KYB_TIER0 = "kybtier0",
  KYB_TIER1 = "kybtier1",
  KYB_TIER2 = "kybtier2",
  KYB_TIER3 = "kybtier3",
  LIVE = "live",
  INTERVIEW = "interview",
}

export interface LoginInputs {
  email: string;
  password: string;
}

export interface RegistrationInputs {
  email: string;
  password: string;
  confirmPassword: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  registrationType?: RegistrationTypeOption;
}

// API response types
export interface ApiResponseBase {
  isSuccess: boolean;
  errors?: ApiError[];
}

export interface Role {
  role_id: number;
  role_name: string;
}

export interface Company {
  company_id: number;
  logo: string;
  image: string;
  description: string;
  name: string;
  category: string;
  required_individual_tier: number;
  required_corporate_tier: number;
  webhook_target_url: string;
  s3LogoUrl: string;
  s3ImageUrl: string;
}

// User individual data interface
export interface UserIndividual {
  id: number;
  applicantId: string;
  userId: string;
  email: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  birthDate: string;
  country: string;
  countryOfResidence: string | null;
  taxResidenceCountry: string | null;
  address: string;
  tin: string;
}

// User company data interface
export interface UserCompany {
  id: number;
  applicantId: string | null;
  userId: string;
  email: string | null;
  phoneNumber: string | null;
  companyName: string | null;
  registrationNumber: string | null;
  country: string | null;
  legalAddress: string | null;
  incorporationDate: string | null;
  taxId: string | null;
  registrationLocation: string | null;
  website: string | null;
  postalAddress: string | null;
  beneficiaryApplicantId?: string | null;
  beneficiaryUserId?: string | null;
}

// User info interface
export interface UserInfo {
  id: number;
  state: string;
  registrationType: string;
  applicantId: string;
  userId: string;
  applicationUrl: string | null;
  interviewCompletedDate: string | null;
}

export interface User {
  user_id: string;
  email: string;
  password_hash?: string;
  signup_date: string;
  state: string;
  registrationtype: string;
  applicant_id: string;
  name: string;
  roles?: Role[];
  companies?: Company[];
  token?: string;
  // Additional properties from API response
  userIndividual?: UserIndividual;
  userCompany?: UserCompany | null;
  userInfo?: UserInfo;
  applicationUrl?: string;
  tierNumber?: string;
}

export interface LoginResponse extends ApiResponseBase {
  data?: User;
}

export interface RegistrationResponse extends ApiResponseBase {
  data?: User;
}

export interface GetCurrentUserResponse extends ApiResponseBase {
  data?: User;
}

// Secure storage types
export interface SecureStorageUserData {
  email: string;
  state: string;
  registrationtype: string;
  applicant_id: string;
}

// User state types
export interface UserState {
  isLoading: boolean;
  isAuthenticated: boolean;
  userData: SecureStorageUserData | null;
  error: string | null;
}

// Additional types as needed
export interface AuthToken {
  token: string;
  expiresAt: number;
}

export interface ApiResponse {
  isSuccess: boolean;
  errors?: ApiError[];
}

export interface ApiError {
  code: number;
  message: string;
  source: string;
  severity: number;
  customMessage: string;
}

// User data interfaces
export interface UserData {
  user_id: string;
  email: string;
  state: string;
  registrationtype: RegistrationTypeOption;
  applicant_id?: string;
  name?: string;
}
