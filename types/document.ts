import { ApiResponse } from "./auth";

export interface Document {
  fileS3Path: string;
  fileS3PathThumbnail: string;
  fileName: string;
  idDocDefCountry: string;
  idDocDefIdDocType: string;
  idDocDefIdDocSubType: string;
  creationDate: string; // ISO date string
  modificationDate: string; // ISO date string
}

export interface GetDocumentsResponse extends ApiResponse {
  data?: Document[];
}

export interface TieredDocument extends Document {
  documentId: string;
  documentPreviewId: string;
  fileType: string;
  state: string;
}

export interface TierDocuments {
  state: string;
  files: TieredDocument[];
}

export interface TieredDocumentsResponse extends ApiResponse {
  data?: {
    applicantId: string;
    tiers: TierDocuments[];
  };
}
