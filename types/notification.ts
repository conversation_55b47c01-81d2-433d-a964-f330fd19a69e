export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  createdAt: string;
  data?: Record<string, any>; // Additional data for specific notification types
}

export enum NotificationType {
  DOCUMENT_APPROVED = "document_approved",
  DOCUMENT_REJECTED = "document_rejected",
  DOCUMENT_PENDING = "document_pending",
  COMPANY_CONSENT = "company_consent",
  TIER_UPGRADE = "tier_upgrade",
  INTERVIEW = "interview",
  SYSTEM = "system",
  WARNING = "warning",
}

export interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  addNotification: (notification: Omit<Notification, "id">) => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  isLoading: boolean;
}

export interface NotificationSettings {
  enablePushNotifications: boolean;
  enableInAppNotifications: boolean;
  documentUpdates: boolean;
  companyConsents: boolean;
  tierUpgrades: boolean;
  systemMessages: boolean;
}
