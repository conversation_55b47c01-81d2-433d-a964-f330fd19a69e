// Sumsub SDK Type Definitions
// These types can be imported directly in your code

export type SumsubStatus =
  | "Ready" // SDK is initialized and ready to be presented
  | "Failed" // SDK fails for some reasons (see errorType and errorMsg for details)
  | "Initial" // No verification steps are passed yet
  | "Incomplete" // Some but not all of the verification steps have been passed over
  | "Pending" // Verification is pending
  | "TemporarilyDeclined" // Applicant has been declined temporarily
  | "FinallyRejected" // Applicant has been finally rejected
  | "Approved" // Applicant has been approved
  | "ActionCompleted"; // Applicant action has been completed

export type SumsubEventType =
  | "ApplicantLoaded"
  | "StepInitiated"
  | "StepCompleted"
  | "Analytics";

export interface SumsubEvent {
  eventType: SumsubEventType;
  payload: {
    applicantId?: string;
    idDocSetType?: string;
    isCancelled?: boolean;
    eventName?: string;
    eventPayload?: any;
  };
}

export interface SumsubStatusChangedEvent {
  prevStatus: SumsubStatus;
  newStatus: SumsubStatus;
}

export interface SumsubLogEvent {
  message: string;
  severity?: "Info" | "Warning" | "Error";
}

export interface SumsubResult {
  success: boolean;
  status: SumsubStatus;
  errorType?: string;
  errorMsg?: string;
  actionResult?: SumsubActionResult;
}

export interface SumsubActionResult {
  actionId: string;
  actionType?: string;
  answer: "GREEN" | "RED" | "ERROR";
  allowContinuing?: boolean;
}

export interface SumsubError {
  code: number;
  message: string;
  source: string;
  severity: number;
  customMessage: string;
}

export interface SumsubSuccessResponse {
  isSuccess: true;
  data: string;
  errors: SumsubError[];
}

export interface SumsubValidationError {
  userid?: string[];
  levelName?: string[];
}

export interface SumsubErrorResponse {
  errors: SumsubValidationError;
  type: string;
  title: string;
  status: number;
  traceId: string;
}
