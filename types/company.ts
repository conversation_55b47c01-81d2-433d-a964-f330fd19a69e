import { ApiResponse } from "./auth";

export interface Company {
  company_id: number;
  logo: string;
  image: string;
  description: string;
  name: string;
  category: string;
  required_individual_tier: number;
  required_corporate_tier: number;
  webhook_target_url: string;
  s3LogoUrl: string;
  s3ImageUrl: string;
}

export interface CompanyPaginatedData {
  pageSize: number;
  pageNumber: number;
  totalPages: number;
  totalCount: number;
  result: Company[];
}

export interface GetCompanyByIDResponse extends ApiResponse {
  data?: Company;
}

export interface GetCompaniesResponse extends ApiResponse {
  data?: CompanyPaginatedData;
}

export interface GetUserCompaniesConsentsResponse extends ApiResponse {
  data: Company[];
}

export interface UserCompanyConsentResponse extends ApiResponse {
  data: boolean;
}
