// Interview notification types based on web implementation

export interface NotificationTypeDto {
  id: number;
  name: string;
  description: string | null;
}

export interface NotificationDto {
  id: number;
  message: string;
  userId: string | null;
  applicantId: string | null;
  createdAt: string;
  notificationType: NotificationTypeDto;
}

export interface SendNotificationRequest {
  userId: string;
  message: string;
  title: string;
  type: string;
}

export interface InterviewNotification {
  id?: number; // Optional ID for API operations
  message: string;
  title: string;
  type: string;
  timestamp: Date;
}

export interface UserInfo {
  interviewCompletedDate: string | null;
  // Add other user info properties as needed
}

// API Response types
export interface NotificationApiResponse {
  isSuccess: boolean;
  data: NotificationDto[] | null;
  errors: any[] | null;
}

export interface BooleanApiResponse {
  isSuccess: boolean;
  data: boolean;
  errors: any[] | null;
}
