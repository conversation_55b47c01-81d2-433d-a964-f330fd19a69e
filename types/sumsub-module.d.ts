// TypeScript module declaration for @sumsub/react-native-mobilesdk-module
// This file provides type definitions for the Sumsub React Native SDK

declare module "@sumsub/react-native-mobilesdk-module" {
  export type Status =
    | "Ready" // SDK is initialized and ready to be presented
    | "Failed" // SDK fails for some reasons (see errorType and errorMsg for details)
    | "Initial" // No verification steps are passed yet
    | "Incomplete" // Some but not all of the verification steps have been passed over
    | "Pending" // Verification is pending
    | "TemporarilyDeclined" // Applicant has been declined temporarily
    | "FinallyRejected" // Applicant has been finally rejected
    | "Approved" // Applicant has been approved
    | "ActionCompleted"; // Applicant action has been completed

  export type EventType =
    | "ApplicantLoaded"
    | "StepInitiated"
    | "StepCompleted"
    | "Analytics";

  export interface SumsubEvent {
    eventType: EventType;
    payload: {
      applicantId?: string;
      idDocSetType?: string;
      isCancelled?: boolean;
      eventName?: string;
      eventPayload?: any;
    };
  }

  export interface StatusChangedEvent {
    prevStatus: Status;
    newStatus: Status;
  }

  export interface LogEvent {
    message: string;
    severity?: "Info" | "Warning" | "Error";
  }

  export interface Result {
    success: boolean;
    status: Status;
    errorType?: string;
    errorMsg?: string;
    actionResult?: ActionResult;
  }

  export interface ActionResult {
    actionId: string;
    actionType?: string;
    answer: "GREEN" | "RED" | "ERROR";
    allowContinuing?: boolean;
  }

  export interface SNSMobileSDKBuilder {
    withHandlers: (handlers: {
      onStatusChanged?: (event: StatusChangedEvent) => void;
      onLog?: (event: LogEvent) => void;
      onEvent?: (event: SumsubEvent) => void;
      onError?: (event: any) => void;
    }) => SNSMobileSDKBuilder;
    withDebug: (enabled: boolean) => SNSMobileSDKBuilder;
    withLocale: (locale: string) => SNSMobileSDKBuilder;
    withApplicantConf: (config: {
      email?: string;
      phone?: string;
      firstName?: string;
      lastName?: string;
      dob?: string;
      country?: string;
      [key: string]: any;
    }) => SNSMobileSDKBuilder;
    withTheme: (theme: object) => SNSMobileSDKBuilder;
    withAnalyticsEnabled: (enabled: boolean) => SNSMobileSDKBuilder;
    withStrings: (strings: object) => SNSMobileSDKBuilder;
    build: () => SNSMobileSDK;
  }

  export interface SNSMobileSDK {
    launch: () => Promise<Result>;
    dismiss: () => void;
  }

  export interface SNSMobileSDKStatic {
    init: (
      accessToken: string,
      tokenExpirationHandler: () => Promise<string>
    ) => SNSMobileSDKBuilder;
  }

  const SNSMobileSDK: SNSMobileSDKStatic;
  export default SNSMobileSDK;
}
