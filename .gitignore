# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

.idea
# dependencies
node_modules/

# Trust Nexus
*.code-workspace


# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision



# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example

#Ai
.ai
.augment