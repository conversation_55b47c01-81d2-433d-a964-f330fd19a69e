#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Paths
const PODFILE_PATH = path.join(__dirname, "../ios/Podfile");
const INFO_PLIST_PATH = path.join(__dirname, "../ios/trustnexus/Info.plist");
const ANDROID_BUILD_GRADLE_PATH = path.join(
  __dirname,
  "../android/build.gradle"
);
const ANDROID_APP_BUILD_GRADLE_PATH = path.join(
  __dirname,
  "../android/app/build.gradle"
);
const SUMSUB_MODULE_BUILD_GRADLE_PATH = path.join(
  __dirname,
  "../node_modules/@sumsub/react-native-mobilesdk-module/android/build.gradle"
);
const APP_JSON_PATH = path.join(__dirname, "../app.json");
const XCODE_PROJECT_PATH = path.join(
  __dirname,
  "../ios/trustnexus.xcodeproj/project.pbxproj"
);

// Configuration
const SUMSUB_VERSION = "1.35.1";

const SUMSUB_PODFILE_CONFIG = `source 'https://cdn.cocoapods.org/'
source 'https://github.com/SumSubstance/Specs.git'

# Enable MRTDReader (NFC) module
ENV['IDENSIC_WITH_MRTDREADER'] = 'true'

# Enable VideoIdent module
ENV['IDENSIC_WITH_VIDEOIDENT'] = 'true'

# Enable EID module
ENV['IDENSIC_WITH_EID'] = 'true'

`;

const OPENSSL_WORKAROUND = `  
  # Ensure \`OpenSSL-Universal\` dependency to be included in Release builds
  if ENV['IDENSIC_WITH_MRTDREADER']
    pod 'OpenSSL-Universal', :configurations => ['Debug', 'Release']
  end
`;

const ANDROID_MAVEN_REPO = `    // Sumsub Maven repository - required for idensic-mobile-sdk
    maven { url "https://maven.sumsub.com/repository/maven-public/" }`;

const IOS_PERMISSIONS = {
  NSCameraUsageDescription: "Let us take a photo",
  NSMicrophoneUsageDescription: "Time to record a video",
  NSPhotoLibraryUsageDescription: "Let us pick a photo",
  NSLocationWhenInUseUsageDescription:
    "Please provide us with your geolocation data to prove your current location",
  NFCReaderUsageDescription:
    "Let us scan the document for more precise recognition",
};

const NFC_IDENTIFIERS = [
  "A0000002471001",
  "A0000002472001",
  "00000000000000",
  "E80704007F00070302", // For eID
];

// Utility functions
function log(message, type = "info") {
  const colors = {
    info: "\x1b[36m", // cyan
    success: "\x1b[32m", // green
    warning: "\x1b[33m", // yellow
    error: "\x1b[31m", // red
    reset: "\x1b[0m",
  };
  console.log(`${colors[type]}${message}${colors.reset}`);
}

function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

function runCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      stdio: options.silent ? "pipe" : "inherit",
      cwd: path.join(__dirname, ".."),
      ...options,
    });
    return options.silent ? result.toString().trim() : true;
  } catch (error) {
    if (!options.ignoreError) {
      log(`Command failed: ${command}`, "error");
      log(error.message, "error");
    }
    return false;
  }
}

// iOS Setup Functions
function setupiOSPodfile() {
  try {
    if (!fileExists(PODFILE_PATH)) {
      log(
        "❌ iOS Podfile not found. Run 'npx expo prebuild' first.",
        "warning"
      );
      return false;
    }

    let podfileContent = fs.readFileSync(PODFILE_PATH, "utf8");

    // Check if already configured
    if (podfileContent.includes("SumSubstance/Specs.git")) {
      log("✅ Sumsub iOS sources already configured", "success");
    } else {
      // Add Sumsub config at the beginning
      podfileContent = SUMSUB_PODFILE_CONFIG + podfileContent;
      log("✅ Added Sumsub iOS sources and ENV variables", "success");
    }

    // Check for OpenSSL workaround
    if (!podfileContent.includes("OpenSSL-Universal")) {
      // Add OpenSSL workaround after use_expo_modules!
      podfileContent = podfileContent.replace(
        /use_expo_modules!/,
        `use_expo_modules!${OPENSSL_WORKAROUND}`
      );
      log(
        "✅ Added OpenSSL-Universal workaround for Release builds",
        "success"
      );
    }

    // Check for platform version (VideoIdent requires iOS 12.2+)
    const platformMatch = podfileContent.match(
      /platform\s*:\s*ios,\s*['"]?([\d.]+)['"]?/
    );
    if (platformMatch) {
      const currentVersion = parseFloat(platformMatch[1]);
      if (currentVersion < 12.2) {
        log(
          `⚠️  Current iOS platform version (${currentVersion}) is below VideoIdent requirement (12.2+)`,
          "warning"
        );
        podfileContent = podfileContent.replace(
          /platform\s*:\s*ios,\s*['"]?[\d.]+['"]?/,
          `platform :ios, '12.2'`
        );
        log("✅ Updated iOS platform version to 12.2", "success");
      }
    }

    fs.writeFileSync(PODFILE_PATH, podfileContent);
    return true;
  } catch (error) {
    log(`❌ Error setting up iOS Podfile: ${error.message}`, "error");
    return false;
  }
}

function setupiOSInfoPlist() {
  try {
    if (!fileExists(INFO_PLIST_PATH)) {
      log("❌ Info.plist not found. Run 'npx expo prebuild' first.", "warning");
      return false;
    }

    let infoPlistContent = fs.readFileSync(INFO_PLIST_PATH, "utf8");
    let modified = false;

    // Add permissions
    for (const [key, value] of Object.entries(IOS_PERMISSIONS)) {
      if (!infoPlistContent.includes(`<key>${key}</key>`)) {
        // Find the last key-value pair before </dict>
        const insertPosition = infoPlistContent.lastIndexOf("</dict>");
        const insertion = `    <key>${key}</key>\n    <string>${value}</string>\n`;
        infoPlistContent =
          infoPlistContent.slice(0, insertPosition) +
          insertion +
          infoPlistContent.slice(insertPosition);
        modified = true;
        log(`✅ Added ${key}`, "success");
      }
    }

    // Add NFC identifiers
    if (
      !infoPlistContent.includes(
        "com.apple.developer.nfc.readersession.iso7816.select-identifiers"
      )
    ) {
      const insertPosition = infoPlistContent.lastIndexOf("</dict>");
      const nfcArray = NFC_IDENTIFIERS.map(
        (id) => `        <string>${id}</string>`
      ).join("\n");
      const insertion = `    <key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>\n    <array>\n${nfcArray}\n    </array>\n`;
      infoPlistContent =
        infoPlistContent.slice(0, insertPosition) +
        insertion +
        infoPlistContent.slice(insertPosition);
      modified = true;
      log("✅ Added NFC identifiers", "success");
    }

    if (modified) {
      fs.writeFileSync(INFO_PLIST_PATH, infoPlistContent);
    } else {
      log("✅ All iOS permissions already configured", "success");
    }

    return true;
  } catch (error) {
    log(`❌ Error setting up Info.plist: ${error.message}`, "error");
    return false;
  }
}

function enableiOSCapabilities() {
  try {
    if (!fileExists(XCODE_PROJECT_PATH)) {
      log(
        "⚠️  Xcode project not found. NFC and Background modes need to be enabled manually.",
        "warning"
      );
      log("   Open Xcode and enable:", "warning");
      log("   - Near Field Communication Tag Reading", "warning");
      log(
        "   - Background Modes > Audio, AirPlay, and Picture in Picture",
        "warning"
      );
      return false;
    }

    // Note: Modifying .pbxproj file programmatically is complex and risky
    // It's better to document this as a manual step
    log("ℹ️  iOS Capabilities need to be enabled manually in Xcode:", "info");
    log("   1. Open ios/trustnexus.xcworkspace in Xcode", "info");
    log("   2. Select your project target", "info");
    log("   3. Go to 'Signing & Capabilities' tab", "info");
    log("   4. Add 'Near Field Communication Tag Reading' capability", "info");
    log("   5. Add 'Background Modes' capability and select:", "info");
    log("      - Audio, AirPlay, and Picture in Picture", "info");

    return true;
  } catch (error) {
    log(`❌ Error checking iOS capabilities: ${error.message}`, "error");
    return false;
  }
}

// Android Setup Functions
function setupAndroidBuildGradle() {
  try {
    if (!fileExists(ANDROID_BUILD_GRADLE_PATH)) {
      log(
        "❌ Android build.gradle not found. Run 'npx expo prebuild' first.",
        "warning"
      );
      return false;
    }

    let buildGradleContent = fs.readFileSync(ANDROID_BUILD_GRADLE_PATH, "utf8");

    // Check if Maven repository is already added
    if (buildGradleContent.includes("maven.sumsub.com")) {
      log("✅ Sumsub Android Maven repository already exists", "success");
      return true;
    }

    // Add Maven repository in allprojects > repositories
    const repositoriesRegex =
      /(allprojects\s*\{\s*repositories\s*\{[^}]*)(}\s*)/;
    if (repositoriesRegex.test(buildGradleContent)) {
      buildGradleContent = buildGradleContent.replace(
        repositoriesRegex,
        `$1${ANDROID_MAVEN_REPO}\n  $2`
      );

      fs.writeFileSync(ANDROID_BUILD_GRADLE_PATH, buildGradleContent);
      log("✅ Sumsub Android Maven repository added", "success");
      return true;
    } else {
      log(
        "❌ Could not find allprojects > repositories section in build.gradle",
        "error"
      );
      return false;
    }
  } catch (error) {
    log(`❌ Error setting up Android build.gradle: ${error.message}`, "error");
    return false;
  }
}

function checkAndroidRequirements() {
  try {
    if (!fileExists(ANDROID_APP_BUILD_GRADLE_PATH)) {
      log("❌ Android app/build.gradle not found", "warning");
      return false;
    }

    const appBuildGradle = fs.readFileSync(
      ANDROID_APP_BUILD_GRADLE_PATH,
      "utf8"
    );

    // Check minSdkVersion (must be 21+)
    const minSdkMatch = appBuildGradle.match(/minSdkVersion\s*=?\s*(\d+)/);
    if (minSdkMatch) {
      const minSdk = parseInt(minSdkMatch[1]);
      if (minSdk < 21) {
        log(
          `❌ minSdkVersion (${minSdk}) is below Sumsub requirement (21+)`,
          "error"
        );
        log(
          "   Update minSdkVersion to 21 or higher in your app configuration",
          "error"
        );
        return false;
      } else {
        log(`✅ minSdkVersion (${minSdk}) meets requirements`, "success");
      }
    }

    // Check Kotlin version (should be 1.7.10+)
    const rootBuildGradle = fs.readFileSync(ANDROID_BUILD_GRADLE_PATH, "utf8");
    const kotlinMatch = rootBuildGradle.match(
      /kotlin_version\s*=\s*["']?([\d.]+)["']?/
    );
    if (kotlinMatch) {
      const kotlinVersion = kotlinMatch[1];
      log(
        `ℹ️  Kotlin version: ${kotlinVersion} (Sumsub requires 1.7.10+)`,
        "info"
      );
    }

    return true;
  } catch (error) {
    log(`❌ Error checking Android requirements: ${error.message}`, "error");
    return false;
  }
}

function setupAndroidModules() {
  try {
    if (!fileExists(SUMSUB_MODULE_BUILD_GRADLE_PATH)) {
      log(
        "❌ Sumsub module build.gradle not found. Is the package installed?",
        "warning"
      );
      return false;
    }

    let moduleContent = fs.readFileSync(
      SUMSUB_MODULE_BUILD_GRADLE_PATH,
      "utf8"
    );
    let modified = false;

    // Enable VideoIdent module
    if (
      moduleContent.includes(
        '// implementation "com.sumsub.sns:idensic-mobile-sdk-videoident:'
      )
    ) {
      moduleContent = moduleContent.replace(
        /\/\/\s*implementation "com\.sumsub\.sns:idensic-mobile-sdk-videoident:[\d.]+"/,
        `implementation "com.sumsub.sns:idensic-mobile-sdk-videoident:${SUMSUB_VERSION}"`
      );
      modified = true;
      log("✅ Enabled VideoIdent module for Android", "success");
    }

    // Enable eID module
    if (
      moduleContent.includes(
        '// implementation "com.sumsub.sns:idensic-mobile-sdk-eid:'
      )
    ) {
      moduleContent = moduleContent.replace(
        /\/\/\s*implementation "com\.sumsub\.sns:idensic-mobile-sdk-eid:[\d.]+"/,
        `implementation "com.sumsub.sns:idensic-mobile-sdk-eid:${SUMSUB_VERSION}"`
      );
      modified = true;
      log("✅ Enabled eID module for Android", "success");
    }

    // Add core SDK if missing
    if (
      !moduleContent.includes("idensic-mobile-sdk:") ||
      moduleContent.includes("idensic-mobile-sdk-videoident")
    ) {
      const dependenciesMatch = moduleContent.match(
        /(dependencies\s*\{[^}]*)(})/
      );
      if (dependenciesMatch) {
        const coreSdk = `\n    // SumSub core SDK - required\n    implementation "com.sumsub.sns:idensic-mobile-sdk:${SUMSUB_VERSION}"`;
        if (!moduleContent.includes(coreSdk)) {
          moduleContent = moduleContent.replace(
            dependenciesMatch[0],
            dependenciesMatch[1] + coreSdk + "\n" + dependenciesMatch[2]
          );
          modified = true;
          log("✅ Added core SDK dependency", "success");
        }
      }
    }

    if (modified) {
      fs.writeFileSync(SUMSUB_MODULE_BUILD_GRADLE_PATH, moduleContent);
    } else {
      log("✅ Android modules already configured", "success");
    }

    return true;
  } catch (error) {
    log(`❌ Error setting up Android modules: ${error.message}`, "error");
    return false;
  }
}

// App.json Configuration
function updateAppJson() {
  try {
    if (!fileExists(APP_JSON_PATH)) {
      log("❌ app.json not found", "error");
      return false;
    }

    let appJson = JSON.parse(fs.readFileSync(APP_JSON_PATH, "utf8"));
    let modified = false;

    // Ensure expo.ios exists
    if (!appJson.expo) appJson.expo = {};
    if (!appJson.expo.ios) appJson.expo.ios = {};
    if (!appJson.expo.ios.infoPlist) appJson.expo.ios.infoPlist = {};

    // Add iOS permissions
    for (const [key, value] of Object.entries(IOS_PERMISSIONS)) {
      if (!appJson.expo.ios.infoPlist[key]) {
        appJson.expo.ios.infoPlist[key] = value;
        modified = true;
      }
    }

    // Add NFC identifiers
    if (
      !appJson.expo.ios.infoPlist[
        "com.apple.developer.nfc.readersession.iso7816.select-identifiers"
      ]
    ) {
      appJson.expo.ios.infoPlist[
        "com.apple.developer.nfc.readersession.iso7816.select-identifiers"
      ] = NFC_IDENTIFIERS;
      modified = true;
    }

    // Add plugin if not exists
    if (!appJson.expo.plugins) appJson.expo.plugins = [];
    if (!appJson.expo.plugins.includes("./plugins/sumsub-config-plugin.js")) {
      appJson.expo.plugins.push("./plugins/sumsub-config-plugin.js");
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(APP_JSON_PATH, JSON.stringify(appJson, null, 2));
      log("✅ Updated app.json configuration", "success");
    } else {
      log("✅ app.json already configured", "success");
    }

    return true;
  } catch (error) {
    log(`❌ Error updating app.json: ${error.message}`, "error");
    return false;
  }
}

// Installation Functions
function installPods() {
  try {
    if (!fileExists(PODFILE_PATH)) {
      log("❌ No Podfile found, skipping pod install", "warning");
      return false;
    }

    log("🔄 Installing CocoaPods dependencies...", "info");

    // First try pod install
    const success = runCommand("cd ios && pod install --repo-update", {
      ignoreError: true,
    });

    if (!success) {
      log("🔄 Trying pod update...", "info");
      // If it fails, try pod update
      runCommand("cd ios && pod update IdensicMobileSDK", {
        ignoreError: true,
      });
      runCommand("cd ios && pod install", { ignoreError: true });
    }

    log("✅ CocoaPods installation completed", "success");
    return true;
  } catch (error) {
    log(`❌ Error installing pods: ${error.message}`, "error");
    return false;
  }
}

function cleanAndroidBuild() {
  try {
    if (!fileExists(ANDROID_BUILD_GRADLE_PATH)) {
      log("❌ Android project not found, skipping clean", "warning");
      return false;
    }

    log("🔄 Cleaning Android build...", "info");
    runCommand("cd android && ./gradlew clean", { ignoreError: true });
    log("✅ Android build cleaned", "success");
    return true;
  } catch (error) {
    log(`❌ Error cleaning Android build: ${error.message}`, "error");
    return false;
  }
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const verifyOnly = args.includes("--verify-only");

  log("🚀 Setting up Sumsub SDK configuration...", "info");
  log(`📦 Sumsub SDK version: ${SUMSUB_VERSION}`, "info");

  let allSuccess = true;

  // Update app.json first
  log("\n📋 Updating app.json configuration...", "info");
  const appJsonSuccess = updateAppJson();
  allSuccess = allSuccess && appJsonSuccess;

  // iOS Setup
  log("\n📱 Setting up iOS configuration...", "info");
  const iosSuccess = setupiOSPodfile() && setupiOSInfoPlist();
  enableiOSCapabilities(); // This is informational only
  allSuccess = allSuccess && iosSuccess;

  // Android Setup
  log("\n🤖 Setting up Android configuration...", "info");
  const androidBuildSuccess = setupAndroidBuildGradle();
  const androidRequirementsSuccess = checkAndroidRequirements();
  const androidModulesSuccess = setupAndroidModules();
  allSuccess =
    allSuccess &&
    androidBuildSuccess &&
    androidRequirementsSuccess &&
    androidModulesSuccess;

  if (!verifyOnly) {
    // Install dependencies
    if (iosSuccess) {
      log("\n📦 Installing iOS dependencies...", "info");
      installPods();
    }

    if (androidBuildSuccess) {
      log("\n📦 Cleaning Android build...", "info");
      cleanAndroidBuild();
    }
  }

  // Final summary
  log("\n" + "=".repeat(60), "info");
  if (allSuccess) {
    log("🎉 Sumsub SDK setup completed successfully!", "success");
    log("\n📱 iOS Manual Steps Required:", "warning");
    log("   1. Open ios/trustnexus.xcworkspace in Xcode", "warning");
    log(
      "   2. Enable 'Near Field Communication Tag Reading' capability",
      "warning"
    );
    log(
      "   3. Enable 'Background Modes' > 'Audio, AirPlay, and Picture in Picture'",
      "warning"
    );
    log("\n✅ You can now run:", "success");
    log("   npx expo run:ios", "info");
    log("   npx expo run:android", "info");
  } else {
    log("⚠️  Sumsub SDK setup completed with some warnings", "warning");
    log("📖 Check the messages above for details", "info");
  }
  log("=".repeat(60), "info");

  process.exit(allSuccess ? 0 : 1);
}

// Run the script
main();
