#!/usr/bin/env node

/**
 * Test script to verify authentication flow and notification cleanup
 * This script helps verify that the infinite loop issue has been resolved
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Authentication Flow and Notification Cleanup');
console.log('=' .repeat(60));

// Test 1: Check if authentication guards are properly implemented
function testAuthenticationGuards() {
  console.log('\n1. Testing Authentication Guards in Notification Hooks...');
  
  const notificationHooksPath = path.join(__dirname, '../services/notification/notification-hooks.ts');
  const interviewHooksPath = path.join(__dirname, '../services/notification/interview-notification-hooks.ts');
  
  try {
    const notificationHooksContent = fs.readFileSync(notificationHooksPath, 'utf8');
    const interviewHooksContent = fs.readFileSync(interviewHooksPath, 'utf8');
    
    // Check for authentication imports
    const hasAuthImport = notificationHooksContent.includes('useSession') && 
                         interviewHooksContent.includes('useSession');
    
    // Check for enabled property with authentication check
    const hasEnabledCheck = notificationHooksContent.includes('enabled: isAuthenticated') &&
                           interviewHooksContent.includes('shouldEnable');
    
    // Check for refetchInterval with authentication check
    const hasRefetchIntervalCheck = notificationHooksContent.includes('refetchInterval: isAuthenticated') &&
                                   interviewHooksContent.includes('refetchInterval: shouldEnable');
    
    console.log(`   ✅ Authentication imports: ${hasAuthImport ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Enabled property checks: ${hasEnabledCheck ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ RefetchInterval checks: ${hasRefetchIntervalCheck ? 'PASS' : 'FAIL'}`);
    
    return hasAuthImport && hasEnabledCheck && hasRefetchIntervalCheck;
  } catch (error) {
    console.log(`   ❌ Error reading files: ${error.message}`);
    return false;
  }
}

// Test 2: Check if notification provider respects authentication state
function testNotificationProvider() {
  console.log('\n2. Testing Notification Provider Auth State Dependencies...');
  
  const providerPath = path.join(__dirname, '../providers/notification/notification-provider.tsx');
  
  try {
    const providerContent = fs.readFileSync(providerPath, 'utf8');
    
    // Check for authentication-based polling enablement
    const hasAuthBasedPolling = providerContent.includes('isAuthenticated || !!userId');
    
    // Check for authentication state in useEffect dependencies
    const hasAuthInDependencies = providerContent.includes('isAuthenticated') &&
                                 providerContent.includes('(isAuthenticated || userId)');
    
    console.log(`   ✅ Authentication-based polling: ${hasAuthBasedPolling ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Auth state in dependencies: ${hasAuthInDependencies ? 'PASS' : 'FAIL'}`);
    
    return hasAuthBasedPolling && hasAuthInDependencies;
  } catch (error) {
    console.log(`   ❌ Error reading provider file: ${error.message}`);
    return false;
  }
}

// Test 3: Check if React Query cache clearing is implemented
function testReactQueryCacheClearing() {
  console.log('\n3. Testing React Query Cache Clearing on Logout...');
  
  const authProviderPath = path.join(__dirname, '../providers/auth/auth-provider.tsx');
  
  try {
    const authProviderContent = fs.readFileSync(authProviderPath, 'utf8');
    
    // Check for React Query import
    const hasQueryClientImport = authProviderContent.includes('useQueryClient');
    
    // Check for cache clearing in logout event handler
    const hasCacheClearingInLogout = authProviderContent.includes('queryClient.clear()') &&
                                    authProviderContent.includes('React Query cache cleared');
    
    // Check for cache clearing in manual signOut
    const hasCacheClearingInSignOut = authProviderContent.includes('queryClient.clear()') &&
                                     authProviderContent.includes('manual logout');
    
    console.log(`   ✅ QueryClient import: ${hasQueryClientImport ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Cache clearing in logout: ${hasCacheClearingInLogout ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Cache clearing in signOut: ${hasCacheClearingInSignOut ? 'PASS' : 'FAIL'}`);
    
    return hasQueryClientImport && hasCacheClearingInLogout && hasCacheClearingInSignOut;
  } catch (error) {
    console.log(`   ❌ Error reading auth provider file: ${error.message}`);
    return false;
  }
}

// Test 4: Check if SignalR connection cleanup is implemented
function testSignalRCleanup() {
  console.log('\n4. Testing SignalR Connection Cleanup on Auth State Change...');
  
  const signalRHookPath = path.join(__dirname, '../hooks/useSignalRNotifications.ts');
  
  try {
    const signalRHookContent = fs.readFileSync(signalRHookPath, 'utf8');
    
    // Check for authentication state usage
    const hasAuthStateUsage = signalRHookContent.includes('useSession') &&
                             signalRHookContent.includes('isAuthenticated');
    
    // Check for authentication-based connection logic
    const hasAuthBasedConnection = signalRHookContent.includes('!isAuthenticated') &&
                                  signalRHookContent.includes('stopConnection()');
    
    // Check for authentication in useEffect dependencies
    const hasAuthInEffectDeps = signalRHookContent.includes('isAuthenticated]');
    
    console.log(`   ✅ Authentication state usage: ${hasAuthStateUsage ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Auth-based connection logic: ${hasAuthBasedConnection ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Auth in effect dependencies: ${hasAuthInEffectDeps ? 'PASS' : 'FAIL'}`);
    
    return hasAuthStateUsage && hasAuthBasedConnection && hasAuthInEffectDeps;
  } catch (error) {
    console.log(`   ❌ Error reading SignalR hook file: ${error.message}`);
    return false;
  }
}

// Test 5: Check for potential infinite loop patterns
function testInfiniteLoopPrevention() {
  console.log('\n5. Testing Infinite Loop Prevention Patterns...');
  
  const files = [
    '../services/notification/notification-hooks.ts',
    '../services/notification/interview-notification-hooks.ts',
    '../providers/notification/notification-provider.tsx'
  ];
  
  let hasProperGuards = true;
  
  files.forEach(file => {
    try {
      const filePath = path.join(__dirname, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for authentication checks before API calls
      const hasAuthChecks = content.includes('isAuthenticated') || 
                           content.includes('!!token') ||
                           content.includes('enabled:');
      
      console.log(`   ✅ ${path.basename(file)}: ${hasAuthChecks ? 'PASS' : 'FAIL'}`);
      
      if (!hasAuthChecks) {
        hasProperGuards = false;
      }
    } catch (error) {
      console.log(`   ❌ Error reading ${file}: ${error.message}`);
      hasProperGuards = false;
    }
  });
  
  return hasProperGuards;
}

// Run all tests
function runAllTests() {
  console.log('Running comprehensive authentication and notification cleanup tests...\n');
  
  const test1 = testAuthenticationGuards();
  const test2 = testNotificationProvider();
  const test3 = testReactQueryCacheClearing();
  const test4 = testSignalRCleanup();
  const test5 = testInfiniteLoopPrevention();
  
  const allTestsPassed = test1 && test2 && test3 && test4 && test5;
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(60));
  console.log(`1. Authentication Guards: ${test1 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`2. Notification Provider: ${test2 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`3. React Query Cache Clearing: ${test3 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`4. SignalR Cleanup: ${test4 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`5. Infinite Loop Prevention: ${test5 ? '✅ PASS' : '❌ FAIL'}`);
  
  console.log('\n' + '=' .repeat(60));
  if (allTestsPassed) {
    console.log('🎉 ALL TESTS PASSED! The infinite loop issue should be resolved.');
    console.log('✅ Notification fetching will now properly stop on logout.');
    console.log('✅ Authentication state is properly respected across all components.');
  } else {
    console.log('⚠️  SOME TESTS FAILED! Please review the failing components.');
    console.log('❌ The infinite loop issue may still exist.');
  }
  console.log('=' .repeat(60));
  
  return allTestsPassed;
}

// Execute the tests
if (require.main === module) {
  const success = runAllTests();
  process.exit(success ? 0 : 1);
}

module.exports = {
  testAuthenticationGuards,
  testNotificationProvider,
  testReactQueryCacheClearing,
  testSignalRCleanup,
  testInfiniteLoopPrevention,
  runAllTests
};
