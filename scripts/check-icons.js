#!/usr/bin/env node

/**
 * Icon Verification Script for Trust Nexus Native
 * 
 * This script checks if all required icon files exist and have correct dimensions.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Trust Nexus Native - Icon Verification');
console.log('=========================================');

const assetsDir = path.join(__dirname, '../assets/images');

// Required icon files and their expected dimensions
const requiredIcons = {
  'small-logo.svg': 'SVG source file',
  'icon.png': '1024x1024 (Main app icon)',
  'adaptive-icon.png': '432x432 (Android adaptive)',
  'splash-icon.png': '512x512 (Splash screen)',
  'favicon.png': '256x256 (Web favicon)'
};

// Platform-specific icon locations
const platformIcons = {
  ios: {
    appIcon: 'ios/trustnexus/Images.xcassets/AppIcon.appiconset/<EMAIL>',
    splashIcon: 'ios/trustnexus/Images.xcassets/SplashScreenLogo.imageset/image.png'
  },
  android: {
    adaptiveIcon: 'android/app/src/main/res/mipmap-mdpi/ic_launcher_foreground.webp',
    splashIcon: 'android/app/src/main/res/drawable-mdpi/splashscreen_logo.png'
  }
};

let allGood = true;

console.log('\n📁 Checking source icon files...');
for (const [filename, description] of Object.entries(requiredIcons)) {
  const filePath = path.join(assetsDir, filename);
  
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${filename} - ${description}`);
  } else {
    console.log(`❌ ${filename} - MISSING! (${description})`);
    allGood = false;
  }
}

console.log('\n📱 Checking platform-specific icons...');

// Check iOS icons
console.log('\n🍎 iOS Icons:');
for (const [type, filePath] of Object.entries(platformIcons.ios)) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${type}: ${filePath}`);
  } else {
    console.log(`❌ ${type}: ${filePath} - MISSING!`);
    allGood = false;
  }
}

// Check Android icons
console.log('\n🤖 Android Icons:');
for (const [type, filePath] of Object.entries(platformIcons.android)) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${type}: ${filePath}`);
  } else {
    console.log(`❌ ${type}: ${filePath} - MISSING!`);
    allGood = false;
  }
}

console.log('\n📋 Configuration Check...');

// Check app.json configuration
const appJsonPath = path.join(__dirname, '../app.json');
if (fs.existsSync(appJsonPath)) {
  try {
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    const expo = appJson.expo;
    
    // Check main icon
    if (expo.icon === './assets/images/icon.png') {
      console.log('✅ Main icon configuration correct');
    } else {
      console.log(`❌ Main icon configuration incorrect: ${expo.icon}`);
      allGood = false;
    }
    
    // Check splash screen
    const splashPlugin = expo.plugins?.find(p => Array.isArray(p) && p[0] === 'expo-splash-screen');
    if (splashPlugin && splashPlugin[1]?.image === './assets/images/splash-icon.png') {
      console.log('✅ Splash screen configuration correct');
    } else {
      console.log('❌ Splash screen configuration incorrect');
      allGood = false;
    }
    
    // Check Android adaptive icon
    if (expo.android?.adaptiveIcon?.foregroundImage === './assets/images/adaptive-icon.png') {
      console.log('✅ Android adaptive icon configuration correct');
    } else {
      console.log('❌ Android adaptive icon configuration incorrect');
      allGood = false;
    }
    
  } catch (error) {
    console.log('❌ Error reading app.json:', error.message);
    allGood = false;
  }
} else {
  console.log('❌ app.json not found');
  allGood = false;
}

console.log('\n' + '='.repeat(50));

if (allGood) {
  console.log('🎉 All icons are properly configured!');
  console.log('\n📱 Ready to build:');
  console.log('   npx expo run:ios');
  console.log('   npx expo run:android');
} else {
  console.log('⚠️  Some issues found. Please fix them before building.');
  console.log('\n🔧 To fix issues:');
  console.log('   1. Run: npm run generate-icons');
  console.log('   2. Run: npm run prebuild:clean');
  console.log('   3. Run: npm run check-icons');
}

process.exit(allGood ? 0 : 1);
