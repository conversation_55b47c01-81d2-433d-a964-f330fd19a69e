#!/usr/bin/env node

/**
 * Icon Generation Script for Trust Nexus Native
 *
 * This script generates properly sized PNG icons from the SVG source
 * for iOS and Android platforms with correct dimensions.
 */

const fs = require("fs");
const path = require("path");
const sharp = require("sharp");

console.log("🎨 Trust Nexus Native - Icon Generation Script");
console.log("===============================================");

// Check if SVG source exists
const svgPath = path.join(__dirname, "../assets/images/small-logo.svg");
const assetsDir = path.join(__dirname, "../assets/images");

if (!fs.existsSync(svgPath)) {
  console.error("❌ SVG source file not found:", svgPath);
  process.exit(1);
}

console.log("✅ Found SVG source:", svgPath);

// Required icon sizes
const iconSizes = {
  // Main app icon (iOS with padding for better appearance)
  "icon.png": 1024,

  // Android adaptive icon (with proper padding)
  "adaptive-icon.png": 432,

  // Splash screen icon (smaller size for better appearance)
  "splash-icon.png": 300,

  // Favicon for web
  "favicon.png": 256,
};

// Special handling for adaptive icon with padding
const adaptiveIconConfig = {
  size: 432,
  contentSize: 216, // Reduced content size for more padding (432 * 0.5)
  padding: 108, // Increased padding (432 - 216) / 2
};

// iOS icon configuration with padding for better appearance
const iosIconConfig = {
  size: 1024,
  contentSize: 512, // 50% content, 50% padding for consistent look
  padding: 256, // (1024 - 512) / 2
};

console.log("\n📋 Required icon sizes:");
Object.entries(iconSizes).forEach(([filename, size]) => {
  console.log(`   ${filename}: ${size}x${size}px`);
});

async function generateIcons() {
  console.log("\n🔄 Generating PNG icons from SVG...");

  try {
    for (const [filename, size] of Object.entries(iconSizes)) {
      const outputPath = path.join(assetsDir, filename);

      console.log(`   Generating ${filename} (${size}x${size}px)...`);

      if (filename === "adaptive-icon.png") {
        // Special handling for Android adaptive icon with proper padding
        console.log(
          `     Adding padding for adaptive icon (content: ${adaptiveIconConfig.contentSize}x${adaptiveIconConfig.contentSize}px)`
        );

        await sharp(svgPath)
          .resize(
            adaptiveIconConfig.contentSize,
            adaptiveIconConfig.contentSize,
            {
              fit: "contain",
              background: { r: 255, g: 255, b: 255, alpha: 0 },
            }
          )
          .extend({
            top: adaptiveIconConfig.padding,
            bottom: adaptiveIconConfig.padding,
            left: adaptiveIconConfig.padding,
            right: adaptiveIconConfig.padding,
            background: { r: 255, g: 255, b: 255, alpha: 0 },
          })
          .png()
          .toFile(outputPath);
      } else if (filename === "icon.png") {
        // Special handling for iOS main icon with padding for better appearance
        console.log(
          `     Adding padding for iOS icon (content: ${iosIconConfig.contentSize}x${iosIconConfig.contentSize}px)`
        );

        await sharp(svgPath)
          .resize(iosIconConfig.contentSize, iosIconConfig.contentSize, {
            fit: "contain",
            background: { r: 255, g: 255, b: 255, alpha: 0 },
          })
          .extend({
            top: iosIconConfig.padding,
            bottom: iosIconConfig.padding,
            left: iosIconConfig.padding,
            right: iosIconConfig.padding,
            background: { r: 255, g: 255, b: 255, alpha: 0 },
          })
          .png()
          .toFile(outputPath);
      } else {
        // Standard icon generation
        await sharp(svgPath)
          .resize(size, size, {
            fit: "contain",
            background: { r: 255, g: 255, b: 255, alpha: 0 }, // Transparent background
          })
          .png()
          .toFile(outputPath);
      }

      console.log(`   ✅ Created: ${filename}`);
    }

    console.log("\n🎉 All icons generated successfully!");
    console.log("\n📱 Next steps:");
    console.log("1. Run: npm run prebuild:clean");
    console.log(
      "2. This will regenerate iOS and Android platform-specific icons"
    );
    console.log("3. Test the app to verify icons display correctly");
  } catch (error) {
    console.error("❌ Error generating icons:", error.message);
    process.exit(1);
  }
}

// Run the icon generation
generateIcons();
