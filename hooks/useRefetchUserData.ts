import { QUERY_KEYS } from "@/services/query-keys";
import { useQueryClient } from "@tanstack/react-query";

export const useRefetchUserData = () => {
  const queryClient = useQueryClient();

  // Utility Functions
  const refreshUserData = () => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.CURRENT_USER],
    });
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.COMPANY_BY_ID],
    });
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.ALL_COMPANIES],
    });
  };

  return { refreshUserData };
};
