import {
  handleKYBDeepLink as parseKYBDeepLink,
  showKYBCompletionAlert,
} from "@/services/sumsub/kyb-redirect-service";
import * as Linking from "expo-linking";
import { useEffect } from "react";

/**
 * Hook to handle KYB deep link returns
 * Should be used in the main app layout or root component
 */
export const useKYBDeepLink = () => {
  useEffect(() => {
    // Handle deep link when app is already open
    const subscription = Linking.addEventListener("url", (event) => {
      handleKYBDeepLink(event.url);
    });

    // Handle deep link when app is opened from closed state
    const checkInitialURL = async () => {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        handleKYBDeepLink(initialUrl);
      }
    };

    checkInitialURL();

    return () => subscription?.remove();
  }, []);

  const handleKYBDeepLink = (url: string) => {
    const result = parseKYBDeepLink(url);

    if (result) {
      console.log("🔗 KYB deep link received:", result);

      // Show completion alert
      showKYBCompletionAlert(result, () => {
        // Optional: refresh user data, navigate somewhere, etc.
        console.log("🔄 KYB completion handled");
      });
    }
  };

  return { handleKYBDeepLink };
};
