import { useState, useEffect, useCallback } from "react";
import signalRNotificationService from "@/services/notification/signalr-notification-service";
import { InterviewNotification } from "@/types/interview-notification";
import { API_URL } from "@/constants/api-constants";

import { useCurrentUser } from "@/services/user/user-hooks";
import { useSession } from "@/providers/auth/auth-provider";

export const useSignalRNotifications = (userId: string | null) => {
  const [notifications, setNotifications] = useState<InterviewNotification[]>(
    []
  );
  const [isConnected, setIsConnected] = useState(false);
  const [currentInterviewNotification, setCurrentInterviewNotification] =
    useState<InterviewNotification | null>(null);
  const [isInterviewModalOpen, setIsInterviewModalOpen] = useState(false);

  // Get user data to check interview completion status
  const { data: user } = useCurrentUser();

  // Get authentication state to properly handle disconnection on logout
  const { token } = useSession();
  const isAuthenticated = !!token;

  useEffect(() => {
    // Only connect if user is authenticated and has userId
    if (!userId || !API_URL || !isAuthenticated) {
      // If not authenticated, ensure connection is stopped
      if (!isAuthenticated) {
        signalRNotificationService.stopConnection();
        setIsConnected(false);
        console.log(
          "🔌 SignalR disconnected due to authentication state change"
        );
      }
      return;
    }

    const connectToSignalR = async () => {
      try {
        await signalRNotificationService.startConnection(API_URL!, userId);
        setIsConnected(true);
        console.log("🔗 SignalR connected for user:", userId);
      } catch (error) {
        console.error("❌ Failed to connect to SignalR:", error);
        setIsConnected(false);
      }
    };

    connectToSignalR();

    signalRNotificationService.onNotification((notification) => {
      // Add to notifications list
      setNotifications((prev) => [notification, ...prev]);

      // Check if interview is already completed
      const interviewCompleted = user?.userInfo?.interviewCompletedDate;
      if (interviewCompleted) {
        return;
      }

      // Show interview modal
      setCurrentInterviewNotification(notification);
      setIsInterviewModalOpen(true);
    });

    return () => {
      signalRNotificationService.stopConnection();
      signalRNotificationService.removeNotificationHandler();
      setIsConnected(false);
      console.log("🔌 SignalR cleanup on effect dependency change");
    };
  }, [userId, user?.userInfo?.interviewCompletedDate, isAuthenticated]);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const closeInterviewModal = useCallback(() => {
    setIsInterviewModalOpen(false);
    setCurrentInterviewNotification(null);
  }, []);

  const handleAcceptInterview = useCallback(async () => {
    console.log("✅ Interview accepted:", currentInterviewNotification);
    closeInterviewModal();
  }, [currentInterviewNotification, closeInterviewModal]);

  const handleRejectInterview = useCallback(async () => {
    console.log("❌ Interview rejected:", currentInterviewNotification);
    closeInterviewModal();
  }, [currentInterviewNotification, closeInterviewModal]);

  return {
    notifications,
    isConnected,
    clearNotifications,
    // Interview modal state
    currentInterviewNotification,
    isInterviewModalOpen,
    closeInterviewModal,
    handleAcceptInterview,
    handleRejectInterview,
  };
};
