const {
  withPodfile,
  withApp<PERSON>uildGradle,
  withProjectBuildGradle,
} = require("@expo/config-plugins");

/**
 * Expo Config Plugin for Sumsub SDK
 * Automatically configures iOS and Android for Sumsub integration
 */
const withSumsubSDK = (config) => {
  // Configure iOS Podfile
  config = withSumsubPodfile(config);

  // Configure Android build.gradle files
  config = withSumsubAndroidBuildGradle(config);
  config = withSumsubAndroidAppBuildGradle(config);

  return config;
};

/**
 * Configure iOS Podfile with Sumsub sources and dependencies
 */
const withSumsubPodfile = (config) => {
  return withPodfile(config, (config) => {
    const podfileContent = config.modResults.contents;

    // Check if Sumsub configuration is already added
    if (podfileContent.includes("SumSubstance/Specs.git")) {
      console.log("✅ Sumsub iOS configuration already exists");
      return config;
    }

    console.log("🔧 Adding Sumsub iOS configuration...");

    // Add Sumsub sources and ENV variables at the top
    const sumsubConfig = `source 'https://cdn.cocoapods.org/'
source 'https://github.com/SumSubstance/Specs.git'

# Enable MRTDReader (NFC) module
ENV['IDENSIC_WITH_MRTDREADER'] = 'true'

# Enable VideoIdent module
ENV['IDENSIC_WITH_VIDEOIDENT'] = 'true'

# Enable EID module
ENV['IDENSIC_WITH_EID'] = 'true'

`;

    // Add OpenSSL workaround in target section
    const openSSLWorkaround = `  
  # Ensure \`OpenSSL-Universal\` dependency to be included in Release builds
  if ENV['IDENSIC_WITH_MRTDREADER']
    pod 'OpenSSL-Universal', :configurations => ['Debug', 'Release']
  end
`;

    // Add Sumsub config at the beginning
    let modifiedContent = sumsubConfig + podfileContent;

    // Add OpenSSL workaround after use_expo_modules!
    modifiedContent = modifiedContent.replace(
      /use_expo_modules!/,
      `use_expo_modules!${openSSLWorkaround}`
    );

    config.modResults.contents = modifiedContent;
    console.log("✅ Sumsub iOS configuration added");

    return config;
  });
};

/**
 * Configure Android project build.gradle with Sumsub Maven repository
 */
const withSumsubAndroidBuildGradle = (config) => {
  return withProjectBuildGradle(config, (config) => {
    const buildGradleContent = config.modResults.contents;

    // Check if Maven repository is already added
    if (buildGradleContent.includes("maven.sumsub.com")) {
      console.log("✅ Sumsub Android Maven repository already exists");
      return config;
    }

    console.log("🔧 Adding Sumsub Android Maven repository...");

    // Add Maven repository in allprojects > repositories
    const sumsubMavenRepo = `        // Sumsub Maven repository - required for idensic-mobile-sdk
        maven { url "https://maven.sumsub.com/repository/maven-public/" }`;

    // Find allprojects > repositories section and add Sumsub repo
    const repositoriesRegex =
      /(allprojects\s*\{\s*repositories\s*\{[^}]*)(}\s*})/;

    if (repositoriesRegex.test(buildGradleContent)) {
      const modifiedContent = buildGradleContent.replace(
        repositoriesRegex,
        `$1${sumsubMavenRepo}\n    $2`
      );

      config.modResults.contents = modifiedContent;
      console.log("✅ Sumsub Android Maven repository added");
    } else {
      console.warn(
        "⚠️  Could not find allprojects > repositories section in build.gradle"
      );
    }

    return config;
  });
};

/**
 * Configure Android app build.gradle with additional settings if needed
 */
const withSumsubAndroidAppBuildGradle = (config) => {
  return withAppBuildGradle(config, (config) => {
    const buildGradleContent = config.modResults.contents;

    // Check if minSdkVersion is appropriate (Sumsub requires API 21+)
    const minSdkRegex = /minSdkVersion\s+(\d+)/;
    const match = buildGradleContent.match(minSdkRegex);

    if (match) {
      const currentMinSdk = parseInt(match[1]);
      if (currentMinSdk < 21) {
        console.warn(
          `⚠️  Current minSdkVersion (${currentMinSdk}) is below Sumsub requirement (21+)`
        );
        console.warn(
          "   Consider updating minSdkVersion in your app configuration"
        );
      } else {
        console.log(
          `✅ minSdkVersion (${currentMinSdk}) meets Sumsub requirements`
        );
      }
    }

    return config;
  });
};

/**
 * Validation function to check if all required configurations are present
 */
const validateSumsubConfig = (config) => {
  const errors = [];
  const warnings = [];

  // Check iOS permissions
  const iosPermissions = config.ios?.infoPlist;
  const requiredIOSPermissions = [
    "NSCameraUsageDescription",
    "NSMicrophoneUsageDescription",
    "NSPhotoLibraryUsageDescription",
    "NSLocationWhenInUseUsageDescription",
    "NFCReaderUsageDescription",
  ];

  requiredIOSPermissions.forEach((permission) => {
    if (!iosPermissions?.[permission]) {
      warnings.push(`Missing iOS permission: ${permission}`);
    }
  });

  // Check NFC identifiers
  const nfcIdentifiers =
    iosPermissions?.[
      "com.apple.developer.nfc.readersession.iso7816.select-identifiers"
    ];
  if (
    !nfcIdentifiers ||
    !Array.isArray(nfcIdentifiers) ||
    nfcIdentifiers.length === 0
  ) {
    warnings.push("Missing NFC identifiers for iOS");
  }

  // Check Android permissions (these should be auto-added by the SDK)
  const androidPermissions = config.android?.permissions;
  const requiredAndroidPermissions = [
    "android.permission.CAMERA",
    "android.permission.RECORD_AUDIO",
    "android.permission.ACCESS_FINE_LOCATION",
  ];

  // Note: Android permissions are usually auto-added by the SDK, so we just log info
  console.log("ℹ️  Android permissions will be auto-added by Sumsub SDK");

  // Log validation results
  if (warnings.length > 0) {
    console.warn("⚠️  Sumsub configuration warnings:");
    warnings.forEach((warning) => console.warn(`   - ${warning}`));
  }

  if (errors.length > 0) {
    console.error("❌ Sumsub configuration errors:");
    errors.forEach((error) => console.error(`   - ${error}`));
    throw new Error("Sumsub configuration validation failed");
  }

  if (warnings.length === 0 && errors.length === 0) {
    console.log("✅ Sumsub configuration validation passed");
  }
};

// Main plugin export
module.exports = (config) => {
  console.log("🚀 Configuring Sumsub SDK...");

  // Apply all Sumsub configurations
  const configuredConfig = withSumsubSDK(config);

  // Validate configuration
  try {
    validateSumsubConfig(configuredConfig);
  } catch (error) {
    console.error("❌ Sumsub plugin validation failed:", error.message);
    // Don't throw here to allow build to continue, but log the error
  }

  console.log("✅ Sumsub SDK configuration completed");
  return configuredConfig;
};
