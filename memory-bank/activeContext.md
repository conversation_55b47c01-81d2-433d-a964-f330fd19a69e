# Active Context

## Current Focus

1. Implementing native StyleSheet system across components
2. Implementing Sumsub SDK integration
3. Enhancing UI components with refined styling
4. Building document management components and screens
5. Developing company management components and home screen
6. Implementing company details and consent management

### Navigation Improvements

We are currently focused on enhancing the navigation system with the following key areas:

1. **Stack Navigation**

   - Proper gesture handling for welcome screen
   - Fixed back navigation issues
   - Configured stack animation and presentation mode

2. **Tab Navigation**

   - Optimized custom tab bar implementation
   - Fixed navigation state preservation
   - Improved tab switching performance

3. **Navigation State Management**
   - Proper handling of navigation history
   - Modal state management and cleanup
   - Tab state preservation during navigation

## Recent Changes

### Home Screen Implementation

1. Index.tsx (Dashboard Home)

   - Replaced PageWrapper with more flexible structure
   - Integrated UserHeader for profile information
   - Implemented FlashList with header component for optimized performance
   - Used shared page styles through pageStyle utilities
   - Added company list with proper filtering
   - Incorporated AppHeader with title and navigation options
   - Implemented pull-to-refresh functionality
   - Added empty state handling for search results

2. Company Components
   - Created CompanyItem component with clean styling
   - Implemented tier badges in upper right corner
   - Added document sharing count information
   - Implemented status visualization (Approved, Pending, Rejected)
   - Created Details and Revoke action buttons
   - Applied consistent styling with border and shadow effects
   - Used COLORS.background for consistent theming
   - Added primaryLight border for subtle emphasis

### Document Components Implementation

1. DocumentItem

   - Created reusable document list item component
   - Implemented status-based color indicators (Updated, Expired, Expires Soon)
   - Added document type and file information display
   - Implemented action buttons (options menu)
   - Applied consistent styling with shadow and border effects

2. FormSearch

   - Built custom search input component
   - Matched design specifications with proper border radius (40)
   - Used light gray background (#F5F5F5)
   - Implemented search icon positioning
   - Added proper padding and height (40 vertical scale)
   - Ensured proper hit areas for better touch interaction

3. MyDocuments Screen
   - Implemented document listing screen
   - Added UserHeader component for profile information
   - Integrated FormSearch for document filtering
   - Used FlashList for optimized document rendering
   - Added pull-to-refresh functionality
   - Implemented empty state handling
   - Applied proper spacing and margins

### Form Components Enhancement

1. FormInput

   - Implemented native StyleSheet
   - Added iOS optimizations
   - Improved type safety
   - Enhanced keyboard handling

2. FormPassword

   - Matched FormInput styling
   - Added iOS-specific improvements
   - Enhanced visibility toggle
   - Improved error handling

3. ImageSelectionCard
   - Implemented native styling
   - Improved selection handling
   - Enhanced visual feedback

### Development Environment

1. Build System

   - Migrated to Expo Development Build
   - Configured native module support
   - Updated build scripts

2. SDK Integration

   - Added Sumsub dependencies
   - Configured platform-specific requirements
   - Prepared SDK initialization
   - Implemented Sumsub service for token retrieval
   - Created SDK initialization module
   - Set up tier progression system

### iOS Optimizations

1. Input Session Handling

   - Added proper focus/blur management
   - Implemented session ID fixes
   - Enhanced keyboard behavior

2. Form Improvements
   - Added KeyboardAvoidingView
   - Implemented proper scroll handling
   - Enhanced touch handling

### UI Component Enhancements

1. AppButton

   - Improved styling for all button variants
   - Enhanced outline variant with shadow effects
   - Fixed text visibility and styling issues
   - Added consistent button text base styling
   - Optimized large button sizing

2. Welcome Flow

   - Implemented multi-step welcome screens
   - Added step indicators with active/inactive states
   - Fixed navigation logic between steps
   - Added proper Next/Skip controls with appropriate styling
   - Optimized layout and responsiveness

3. Navigation Logic
   - Enhanced step transitions
   - Improved back button handling
   - Fixed routing to appropriate screens

### Company Details Implementation

1. Company Details Page

   - Created responsive layout with ScrollView for content overflow
   - Implemented large centered logo display (180x120)
   - Added floating tier badge above logo
   - Centered company information with proper spacing
   - Implemented dynamic action button based on consent state
   - Added proper padding and margins for visual hierarchy

2. Modal Components

   - Created TierUpgradeModal for tier upgrade flow
   - Implemented OnboardingConfirmModal for consent management
   - Added RevokeConsentModal with warning styling
   - Used consistent modal styling across components
   - Implemented loading states for async operations

3. CompanyActionButton Component

   - Extracted button logic into separate component
   - Handles three states: Upgrade, Onboard, and Revoke Consent
   - Manages modal visibility states
   - Implements proper loading states during API calls

4. Consent Management
   - Implemented createUserCompanyConsent mutation
   - Added revokeUserCompanyConsent functionality
   - Proper error handling and loading states
   - User-friendly confirmation flows
   - Clear visual feedback for actions

## Active Decisions

### Layout Architecture

1. Page Structure:

   - Moving from PageWrapper to more flexible layout systems
   - Using FlashList ListHeaderComponent for better performance
   - Standardizing header components across screens
   - Implementing shared page styles for consistency

2. List Components:
   - Using FlashList for optimal performance
   - Consistent item styling with shadows and borders
   - Implementing pull-to-refresh pattern across all lists

### Company Management

1. Company Display:

   - Clean card-based layout with consistent spacing
   - Visual tier indicators in the upper right corner
   - Status indicators with semantic colors
   - Action buttons aligned with branding
   - Placeholder logos with initial letters when images unavailable

2. Company Interactions:
   - Details button for viewing company information
   - Optional revoke functionality for permission management
   - Search filtering capabilities
   - Document sharing relationship tracking

### Build System

1. Using Development Build for:

   - Native module support (Sumsub)
   - Better performance
   - Platform-specific features

2. SDK Integration Strategy

   - Token management implementation complete
   - Handling verification flow
   - Managing SDK lifecycle
   - Tier progression based on user type (KYC/KYB)

### Styling System

1. Native StyleSheet Implementation:

   - Performance optimization
   - Type-safe styles
   - Platform-specific styling

2. Using scale utilities for:

   - Consistent sizing
   - Responsive layouts
   - Platform-specific adjustments

3. Shadow Effects:
   - Enhanced shadow implementation for "floating" UI elements
   - Cross-platform shadow consistency
   - Custom box-shadow implementation

### Component Architecture

1. Base components with:

   - Strong typing
   - iOS optimizations
   - Consistent styling

2. Specialized components:
   - Extending base functionality
   - Maintaining consistency
   - Platform-specific enhancements

### Document Management

1. List Optimization:

   - Using FlashList for better performance with long lists
   - Implementing proper estimatedItemSize for optimization
   - Adding refresh control for data updates

2. Search Implementation:

   - Custom FormSearch component with design-matching styling
   - Client-side filtering for fast response
   - Proper spacing and margins for visual consistency

3. Status Visualization:
   - Color-coded status indicators (green, red, orange)
   - Clean document item layout with consistent spacing
   - Options menu for document actions

### Navigation Structure

1. **Navigation Structure**

   - Using nested Stack and Tabs navigation
   - Welcome screen with gesture support
   - Custom tab bar implementation

2. **Gesture Handling**

   - Enabled for welcome screen
   - Disabled for tab navigation to prevent conflicts
   - Horizontal swipe back gesture

3. **Tab Bar Design**
   - Custom implementation with active/inactive states
   - Visual indicators for active tab
   - Performance-optimized animations

## Next Steps

1. Complete native styling implementation
2. Test Sumsub SDK integration
3. Test verification flow
4. Document integration patterns
5. Enhance error handling
6. Implement document upload functionality
7. Add document detail view screen
8. Implement document sharing features
9. Complete company detail view
10. Implement company permission management
11. Connect company and document relationships

12. **Deep Linking**

- Implement comprehensive deep linking support
- Handle authentication state preservation
- Add testing suite for deep links

13. **Transition Animations**

- Add custom animation curves
- Optimize performance
- Improve gesture response

14. **Performance Optimization**

- Implement route preloading
- Optimize memory management
- Add state cleanup mechanisms

## Known Issues

1. iOS text input session warnings

   - Implementing proper focus/blur handling
   - Adding session management
   - Optimizing keyboard interaction

2. Style consistency

   - Ensuring uniform appearance
   - Maintaining responsive behavior
   - Platform-specific adjustments

3. Integration Requirements

   - SDK version compatibility
   - Platform-specific setup
   - Token management implementation

4. Document Management
   - Need to implement actual document upload functionality
   - Document sharing features not yet implemented
   - Document detail view screen pending
