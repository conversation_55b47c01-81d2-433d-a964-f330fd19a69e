# Technical Context

## Development Environment

- Platform: React Native with Expo Development Build
- Language: TypeScript
- OS Support: iOS (primary focus), Android
- Build Type: Development Build (required for native modules)

## Key Dependencies

1. React Native

   - Using Development Build for native module support
   - Native StyleSheet system
   - Platform-specific optimizations

2. Form Management

   - React Hook Form for form state
   - Zod for validation

3. UI Components

   - Custom form components
   - Expo Vector Icons for iconography

4. Identity Verification
   - Sumsub React Native Module v1.34.1
   - Native SDK integration
   - Secure document verification
   - MRTD Reader for NFC chip reading
   - VideoIdent for video identification
   - eID card verification support

## Sumsub Integration

### Setup Requirements

1. Android

   - Kotlin version 1.7.10 or higher
   - Add Sumsub Maven repository
   - Configure ProGuard rules
   - MRTDReader enabled by default
   - VideoIdent module configuration:
     - Enable in build.gradle
     - Version: 1.34.1
     - Required for video identification flow

2. iOS
   - Disable Bitcode in build settings
   - Required Permissions in Info.plist:
     - Camera Usage (NSCameraUsageDescription)
     - Microphone Usage (NSMicrophoneUsageDescription)
     - Photo Library Usage (NSPhotoLibraryUsageDescription)
     - Location Usage (NSLocationWhenInUseUsageDescription)
     - Face ID Usage (NSFaceIDUsageDescription)
     - NFC Reader Usage (NFCReaderUsageDescription)
   - NFC Capabilities:
     - Enable MRTDReader in Podfile
     - Add NFC capability in device requirements
     - Configure ISO-7816 select identifiers:
       - A0000002471001 (eID)
       - E80704007F00070302 (eID)
   - Dependencies:
     - OpenSSL-Universal (required for Debug and Release)
   - Module Configuration:
     - VideoIdent enabled in Podfile
     - MRTDReader enabled in Podfile
     - EID module enabled in Podfile
   - Support for portrait orientation only

### Implementation Details

The Sumsub integration consists of two main components:

1. `sumsub-service.ts` - Backend communication service that:

   - Retrieves the current user information
   - Determines the next verification tier based on user type (KYC/KYB) and current tier
   - Fetches a Sumsub access token from the API
   - Manages error handling and response normalization

2. `launchSNSMobileSDK.ts` - SDK initialization module that:
   - Initializes the Sumsub mobile SDK with the access token
   - Configures token expiration handling with automatic refresh
   - Sets up event callbacks for status changes and logging
   - Handles the SDK launch process
   - Manages SDK result and error handling
   - Invalidates relevant queries on status changes
   - Provides type-safe error handling and result processing

### Status Management

The SDK implements a comprehensive status management system:

1. Status Types:

   ```typescript
   type SumsubStatus =
     | "Ready"
     | "Initial"
     | "Loading"
     | "Failed"
     | "Completed"
     | "ActionCompleted";
   ```

2. Query Invalidation:

   - User data on verification completion
   - Tier information updates
   - Applicant status changes
   - Action-specific updates

3. Event Handling:
   ```typescript
   interface StatusChangeEvent {
     prevStatus: SumsubStatus;
     newStatus: SumsubStatus;
     actionId?: string;
     applicantId?: string;
   }
   ```

### Error Handling

The SDK implements robust error handling:

1. Error Types:

   ```typescript
   interface SDKError {
     code: number;
     description: string;
     correlationId?: string;
   }
   ```

2. Error Processing:
   - Type-safe error normalization
   - Correlation ID tracking
   - Structured error reporting
   - User-friendly error messages

### Launch Options

The SDK supports configurable launch options:

```typescript
interface LaunchOptions {
  onStatusChange?: OnStatusChangeCallback;
  onComplete?: (result: SDKResult) => void;
  onError?: (error: SDKError) => void;
  debug?: boolean;
  locale?: string;
}
```

### API Integration

```typescript
// API Routes
const API_ROUTES = {
  ACCOUNTS: "/Accounts",
  SUMSUB_ACCESS_TOKEN: "/Sumsub/get-tier-sdk-userid-level-token",
};

// Access Token Retrieval
export const getSumsubAccessToken = async (): Promise<string> => {
  // Gets current user info (user_id, registrationtype, state)
  // Determines next verification tier based on user type and current tier
  // Fetches access token from API with userId and levelName
  // Returns normalized token from response
};

// SDK Initialization
export let launchSNSMobileSDK = async () => {
  // Initializes SDK with access token and refresh handler
  // Configures event handlers for status changes and logging
  // Launches SDK and handles results or errors
};
```

### Tier Progression System

The application implements a tier-based verification system:

1. KYC Flow (Individual Users):

   - TIER0 → TIER1 → TIER2 → TIER3

2. KYB Flow (Business Users):
   - TIER0 → KYB_TIER1 → KYB_TIER2 → KYB_TIER3

### Type Definitions

```typescript
// Sumsub SDK Interface
interface SNSMobileSDK {
  init: (
    accessToken: string,
    tokenExpirationHandler: () => Promise<string>
  ) => SNSMobileSDKBuilder;
}

// Response Types
export interface SumsubSuccessResponse {
  isSuccess: true;
  data: string;
  errors: SumsubError[];
}

export interface SumsubError {
  code: number;
  message: string;
  source: string;
  severity: number;
  customMessage: string;
}

// Tier Levels
export enum SUMSUB_LEVEL {
  TIER0 = "tier0",
  TIER1 = "tier1",
  TIER2 = "tier2",
  TIER3 = "tier3",
  KYB_TIER1 = "kybtier1",
  KYB_TIER2 = "kybtier2",
  KYB_TIER3 = "kybtier3",
}
```

## Styling System

### Native StyleSheet Implementation

- Using React Native's StyleSheet.create
- Platform-specific styles when needed
- Theme-based design system
- Enhanced shadow effects with cross-platform compatibility
- Custom box-shadow implementation

### Style Constants

```typescript
// Colors
COLORS = {
  background: string
  primary: string
  mediumGray: string
  error: string
  // ... other colors
}

// Scaling Utilities
scale(number): number
verticalScale(number): number
```

### Button Styling System

The application uses a comprehensive button styling system with multiple variants and sizes:

1. Button Variants:

   - default: Primary color background with white text
   - outline: White background with shadow effects and primary color text
   - secondary: White background with subtle shadow and primary text
   - destructive: Error color background with white text
   - ghost: Transparent background with primary text
   - link: Transparent with underlined text
   - icon: Icon-only button styling

2. Button Sizes:

   - default: Standard size
   - sm: Small buttons
   - md: Medium sized buttons
   - lg: Large buttons with increased width and padding
   - icon: Square icon-only sizing
   - text: No minimum size, for text-only buttons

3. Shadow Implementation:
   ```typescript
   // Enhanced shadow for floating effect
   shadowColor: "rgba(0, 0, 0, 0.15)",
   boxShadow: "0px 6px 8.3px 0px rgba(28, 63, 60, 0.2)",
   shadowOffset: { width: 0, height: 4 },
   shadowOpacity: 0.8,
   shadowRadius: 8,
   elevation: 8,
   ```

## Component Architecture

### Form Components

1. FormInput

   - Base text input component
   - Label, error handling
   - iOS optimizations

2. FormPassword

   - Extends FormInput functionality
   - Toggle password visibility
   - Custom eye icon
   - iOS-specific configurations:
     - textContentType="none" to disable automatic password suggestions
     - autoComplete="off" for better control
     - Custom password visibility toggle

3. ImageSelectionCard
   - Selection UI component
   - Image display
   - Selection state management

## Technical Constraints

1. iOS Input Handling

   - Session ID management
   - Focus/blur optimization
   - Keyboard behavior
   - Password field optimizations:
     - Disabled automatic strong password suggestions
     - Custom password management
     - Enhanced security controls

2. Type Safety

   - Strict prop typing
   - Interface definitions
   - Component ref handling

3. Performance

   - Memoization where needed
   - Proper event handling
   - Optimized re-renders

4. Native Module Requirements
   - Development build required
   - Platform-specific configurations
   - SDK version compatibility
   - NFC capability requirements
   - OpenSSL dependency management
   - Video identification support
   - eID verification support

## Modal System

### Base Modal Implementation

The application uses a consistent modal system with the following features:

1. Core Components:

   - Transparent overlay with fade animation
   - Centered content container
   - Standard padding and border radius
   - Maximum width constraint
   - iOS-safe area handling

2. Modal Variants:
   - Standard: White background with shadow
   - Warning: Error-tinted border for destructive actions
   - Action: Primary-colored buttons for main actions
   - Confirmation: Secondary actions for cancellation

### Modal Styling

```typescript
// Base Modal Styles
const modalStyles = {
  overlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: scale(16),
  },
  content: {
    maxWidth: scale(400),
    borderRadius: scale(24),
    padding: scale(24),
  },
  warning: {
    borderColor: COLORS.error + "20",
    borderWidth: 1,
  },
};
```

## Consent Management System

### Core Components

1. Consent Types:

   ```typescript
   interface UserCompanyConsent {
     userId: string;
     companyId: string;
     applicant_id: string;
     created_at: string;
     updated_at: string;
   }
   ```

2. Mutation Hooks:

   ```typescript
   const useCreateUserCompanyConsent = () => {
     // Handles consent creation
   };

   const useRevokeUserCompanyConsent = () => {
     // Handles consent revocation
   };
   ```

3. State Management:
   ```typescript
   interface ConsentState {
     hasConsent: boolean;
     canUpgrade: boolean;
   }
   ```

### Implementation Details

1. Action Button Logic:

   - UPGRADE: Available when user can upgrade tier
   - ONBOARD: Available when no consent exists
   - REVOKE: Available when consent exists

2. Modal Flow:

   - Upgrade: Shows tier upgrade information
   - Onboarding: Confirms consent provision
   - Revoke: Warns about consent removal

3. API Integration:
   - Creates consent records
   - Manages consent revocation
   - Handles loading states
   - Provides error feedback

### Event Handling

The SDK implements a comprehensive event handling system:

1. Event Types:

   ```typescript
   type EventType =
     | "ApplicantLoaded" // When an applicant is loaded
     | "StepInitiated" // When a verification step starts
     | "StepCompleted" // When a step is completed or cancelled
     | "Analytics"; // Analytics events
   ```

2. Event Structure:

   ```typescript
   interface SumsubEvent {
     eventType: EventType;
     payload: {
       applicantId?: string; // For ApplicantLoaded
       idDocSetType?: string; // For StepInitiated/Completed
       isCancelled?: boolean; // For StepCompleted
       eventName?: string; // For Analytics
       eventPayload?: any; // For Analytics
     };
   }
   ```

3. Event Processing:

   - Automatic logging in debug mode
   - Step completion tracking
   - Query invalidation on relevant events
   - Analytics event tracking
   - Custom event handler support

4. Launch Options:
   ```typescript
   interface LaunchOptions {
     onStatusChange?: OnStatusChangeCallback;
     onComplete?: (result: SDKResult) => void;
     onError?: (error: SDKError) => void;
     onEvent?: (event: SumsubEvent) => void;
     debug?: boolean;
     locale?: string;
   }
   ```

## Navigation Implementation

### Stack Navigation

The app uses Expo Router for navigation with a nested structure:

1. **Root Layout (`app/_layout.tsx`)**

   - Providers setup
   - Safe area handling
   - Status bar configuration

2. **App Layout (`app/(app)/_layout.tsx`)**

   ```typescript
   <Stack
     screenOptions={{
       headerShown: false,
       gestureEnabled: true,
       gestureDirection: "horizontal",
       animation: "slide_from_right",
       presentation: "card",
     }}
   >
     <Stack.Screen name="welcome" />
     <Stack.Screen name="(tabs)" />
   </Stack>
   ```

3. **Tab Layout (`app/(app)/(tabs)/_layout.tsx`)**
   ```typescript
   <Tabs
     screenOptions={{
       tabBarActiveTintColor: COLORS.primary,
       tabBarInactiveTintColor: COLORS.mediumGray,
       headerShown: false,
     }}
     tabBar={CustomTabBar}
   />
   ```

### Custom Tab Bar

1. **Component Structure**

   ```typescript
   interface CustomTabBarProps {
     state: any;
     descriptors: any;
     navigation: any;
     iconSize: number;
   }
   ```

2. **Styling System**

   ```typescript
   const styles = StyleSheet.create({
     tabBarContainer: {
       position: "absolute",
       bottom: 0,
       borderTopLeftRadius: 30,
       elevation: 8,
     },
     indicator: {
       width: scale(7),
       height: scale(7),
       borderRadius: scale(5),
     },
   });
   ```

3. **Icon System**
   - Using `expo-image` for icons
   - Tint color based on active state
   - Consistent sizing with scale utility

### Navigation State Management

1. **Authentication State**

   ```typescript
   const { token, isLoading } = useSession();
   if (!token) return <Redirect href="/sign-in" />;
   ```

2. **Tab State**

   - Preserved during navigation
   - Handled by Expo Router
   - Custom state management when needed

3. **Modal State**
   - Managed at component level
   - Proper cleanup on dismiss
   - Animation handling

### Gesture System

1. **Stack Gestures**

   - Enabled for welcome screen
   - Horizontal swipe back
   - Animation configuration

2. **Tab Gestures**
   - Disabled to prevent conflicts
   - Custom touch handling
   - Performance optimization
