# Project Brief: Trust Nexus

## Project Overview

Trust Nexus is a secure and user-friendly mobile application for identity verification and authentication services. Built with React Native and Expo, it provides a robust platform for user registration, authentication, and identity verification through integration with the Sumsub SDK.

## Core Requirements

### Technical Stack

- React Native with Expo Development Build
- TypeScript for type safety
- Native StyleSheet system for styling
- React Hook Form with Zod validation
- Sumsub SDK for identity verification

### Key Features

1. Authentication System

   - Sign in functionality
   - Registration flow (KYC/KYB)
   - Password management
   - Form validation

2. UI Components

   - Custom form inputs with validation
   - Password fields with visibility toggle
   - Image selection cards
   - Responsive layouts

3. Identity Verification
   - Sumsub SDK integration
   - Document verification
   - Secure data handling
   - Real-time verification status

### Quality Standards

1. Type Safety

   - TypeScript throughout
   - Strict type checking
   - Interface definitions

2. Performance

   - Optimized rendering
   - Efficient styling
   - Memory management

3. User Experience
   - Smooth form handling
   - Keyboard optimization
   - Error feedback
   - Loading states

## Current Focus

1. Native styling implementation
2. iOS input optimizations
3. Sumsub SDK integration

## Goals

1. Create a secure and user-friendly authentication system
2. Implement efficient and type-safe styling
3. Optimize iOS input handling
4. Maintain consistent styling across platforms
5. Integrate Sumsub verification seamlessly
