# System Patterns

## Component Patterns

### Form Input Components

1. Base Pattern

```typescript
interface BaseFormProps extends TextInputProps {
  label?: string;
  error?: string;
  name: string;
  containerStyle?: StyleProp<ViewStyle>;
}

const FormComponent = forwardRef<TextInput, Props>((props, ref) => {
  // iOS optimizations
  const handleFocus = useCallback(
    (e) => {
      if (Platform.OS === "ios") {
        e.target?.focus();
      }
      props.onFocus?.(e);
    },
    [props.onFocus]
  );

  return (
    <View style={[styles.container, props.containerStyle]}>
      {/* Label */}
      {/* Input */}
      {/* Error */}
    </View>
  );
});
```

2. Style Pattern

```typescript
const styles = StyleSheet.create({
  container: {
    width: "100%",
    gap: verticalScale(8),
    marginBottom: verticalScale(24),
  },
  input: {
    height: verticalScale(56),
    paddingHorizontal: scale(24),
    borderWidth: 1,
    borderRadius: scale(28),
  },
});
```

## Button Styling Patterns

1. Base Button Variants

```typescript
type ButtonVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "ghost"
  | "link"
  | "icon";

type ButtonSize = "default" | "sm" | "md" | "lg" | "icon" | "text";
```

2. Button Style Implementation

```typescript
// Button component structure
const AppButton = (props: AppButtonProps) => {
  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonVariantStyle(variant),
        getButtonSizeStyle(size),
        buttonStyle,
      ]}
    >
      <Text
        style={[
          styles.text,
          getTextVariantStyle(variant),
          styles.buttonTextBase,
          textStyle,
        ]}
      >
        {isLoading ? <ActivityIndicator color="#FFFFFF" /> : text}
      </Text>
    </TouchableOpacity>
  );
};

// Consistent base styling
const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: scale(28),
    minWidth: scale(159),
    minHeight: verticalScale(56),
  },
  buttonTextBase: {
    color: "#FFFFFF",
    fontWeight: "700",
  },
});
```

3. Special Button Variants

```typescript
// Shadow-based outline button (no border)
buttonOutline: {
  backgroundColor: "#FFFFFF",
  borderWidth: 0,
  shadowColor: "rgba(0, 0, 0, 0.15)",
  boxShadow: "0px 6px 8.3px 0px rgba(28, 63, 60, 0.2)",
  shadowOffset: {
    width: 0,
    height: 4,
  },
  shadowOpacity: 0.8,
  shadowRadius: 8,
  elevation: 8,
},

// Text styling for primary color text
textOutline: {
  color: COLORS.primary,
  fontWeight: "700",
},
```

## Form Management Pattern

1. Form Setup

```typescript
interface FormData {
  // Form fields
}

const form = useForm<FormData>({
  resolver: zodResolver(schema),
});
```

2. Validation Pattern

```typescript
const schema = z.object({
  field: z.string().min(1, "Required"),
});
```

## iOS Optimization Patterns

1. Input Session Management

```typescript
// Focus/Blur handling
const handleFocus = useCallback((e) => {
  if (Platform.OS === "ios") {
    e.target?.focus();
  }
}, []);

// Input configuration
<TextInput
  autoCorrect={false}
  spellCheck={false}
  keyboardAppearance="light"
  enablesReturnKeyAutomatically
  rejectResponderTermination={false}
/>;
```

2. Keyboard Management

```typescript
<KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"}>
  <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
    <ScrollView keyboardShouldPersistTaps="handled">
      {/* Form content */}
    </ScrollView>
  </TouchableWithoutFeedback>
</KeyboardAvoidingView>
```

## Navigation Patterns

### Company Details Navigation

1. Route Structure Pattern

```typescript
// Main app routes structure
/(app)/
  ├── (tabs)/           // Tab-based navigation
  │   ├── (home)/       // Home tab content
  │   ├── my-companies  // My Companies tab
  │   └── profile       // Profile tab
  └── company/[id]      // Company details (outside tabs)
```

2. Source-Aware Navigation Pattern

```typescript
// Navigation with source tracking
const navigateToCompany = (companyId: number, source: string) => {
  router.push(`/(app)/company/${companyId}?from=${source}`);
};

// Usage examples
// From marketplace
router.push(`/(app)/company/${company_id}?from=marketplace`);

// From my companies
router.push(`/(app)/company/${companyId}?from=my-companies`);
```

3. Custom Back Navigation Pattern

```typescript
// Company details back navigation
const CompanyDetails = () => {
  const { from } = useLocalSearchParams<{ from?: string }>();

  const handleBackPress = () => {
    if (from === "my-companies") {
      router.push("/(app)/(tabs)/my-companies");
    } else {
      router.push("/(app)/(tabs)/(home)");
    }
  };

  return <AppHeader title="Company Details" onBackPress={handleBackPress} />;
};
```

4. Route Configuration Pattern

```typescript
// App layout configuration
<Stack>
  <Stack.Screen
    name="(tabs)"
    options={{
      gestureEnabled: false, // Disable gesture for tabs
    }}
  />
  <Stack.Screen
    name="company/[id]"
    options={{
      gestureEnabled: true,
      gestureDirection: "horizontal",
      animation: "slide_from_right",
      presentation: "card",
    }}
  />
</Stack>

// Tab layout configuration (hide from tab bar)
<Tabs.Screen
  name="company/[id]"
  options={{
    href: null, // Hide from tab bar
  }}
/>
```

5. Navigation Best Practices

```typescript
// Always include source tracking for proper back navigation
const handleNavigation = (id: number) => {
  router.push(`/(app)/company/${id}?from=${currentPage}`);
};

// Use custom back handlers for complex navigation flows
const handleBackPress = () => {
  // Custom logic based on navigation source
  if (shouldGoToSpecificPage) {
    router.push('/specific-page');
  } else {
    router.back(); // Default behavior
  }
};

// Configure proper animations and gestures
options={{
  gestureEnabled: true,
  gestureDirection: "horizontal",
  animation: "slide_from_right",
  presentation: "card",
}}
```

## Style Management Patterns

1. Theme Constants

```typescript
const COLORS = {
  background: "#FFFFFF",
  primary: "#000000",
  // ...
};
```

2. Responsive Scaling

```typescript
const scale = (size: number) => {
  // Scale based on screen width
};

const verticalScale = (size: number) => {
  // Scale based on screen height
};
```

## Component Composition Pattern

1. Base Components

   - AppText
   - AppButton
   - FormInput

2. Specialized Components
   - FormPassword (extends FormInput)
   - ImageSelectionCard (specialized selection)

## Architecture

## UI Component Patterns

### List Components

1. FlashList Implementation

   - Using ShopifyFlashList for optimal performance
   - Consistent estimatedItemSize settings
   - Pull-to-refresh pattern with RefreshControl
   - Empty state handling with styled components
   - Search functionality integrated in header
   - ListHeaderComponent for fixed headers

2. List Item Patterns
   - Card-based design with consistent styling
   - Shadow and border treatments
   - Status indicators with semantic colors
   - Action buttons with proper hit areas
   - Information hierarchy with primary/secondary text

### Layout Patterns

1. Screen Layout

   - Moving from PageWrapper to flexible container components
   - Using shared pageStyle utilities
   - AppHeader for consistent navigation
   - UserHeader for profile information
   - Search components for filtering
   - Content containers with proper padding/margin

2. Card Components
   - Consistent border radius (scale(12))
   - Shadow implementation with box-shadow
   - Border styling with COLORS.primaryLight
   - COLORS.background for consistency
   - Proper spacing with scale/verticalScale

## Modal Patterns

1. Base Modal Structure

```typescript
interface BaseModalProps {
  isVisible: boolean;
  onClose: () => void;
  isLoading?: boolean;
}

const BaseModal = ({ isVisible, onClose, isLoading }: BaseModalProps) => {
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContent}>{/* Content */}</View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: scale(16),
  },
  modalContent: {
    backgroundColor: COLORS.background,
    borderRadius: scale(24),
    padding: scale(24),
    width: "100%",
    maxWidth: scale(400),
  },
});
```

2. Warning Modal Pattern

```typescript
// For destructive actions like consent revocation
const styles = StyleSheet.create({
  modalContent: {
    // ... base styles
    borderWidth: 1,
    borderColor: COLORS.error + "20", // 20% opacity
  },
});
```

## Consent Management Pattern

1. Consent State Types

```typescript
type ConsentState = {
  hasConsent: boolean;
  canUpgrade: boolean;
};

type ConsentAction = "UPGRADE" | "ONBOARD" | "REVOKE";
```

2. Action Button Pattern

```typescript
const CompanyActionButton = ({
  consentState,
  onUpgrade,
  onOnboard,
  onRevoke,
}: CompanyActionButtonProps) => {
  if (consentState.canUpgrade) {
    return (
      <AppButton
        text="UPGRADE"
        variant="outline"
        size="lg"
        onPress={onUpgrade}
      />
    );
  }

  if (consentState.hasConsent) {
    return (
      <AppButton
        text="REVOKE CONSENT"
        variant="destructive"
        size="lg"
        onPress={onRevoke}
      />
    );
  }

  return (
    <AppButton text="ONBOARD" variant="outline" size="lg" onPress={onOnboard} />
  );
};
```

3. Consent Mutation Pattern

```typescript
const useConsentMutation = () => {
  const { mutate: createConsent, isPending: isCreatePending } =
    useCreateUserCompanyConsent();

  const { mutate: revokeConsent, isPending: isRevokePending } =
    useRevokeUserCompanyConsent();

  return {
    createConsent,
    revokeConsent,
    isLoading: isCreatePending || isRevokePending,
  };
};
```

## SDK Integration Patterns

### Sumsub SDK Pattern

1. SDK Initialization

```typescript
interface LaunchOptions {
  onStatusChange?: (event: StatusChangeEvent) => void;
  onComplete?: (result: SDKResult) => void;
  onError?: (error: SDKError) => void;
  debug?: boolean;
  locale?: string;
}

const launchSDK = async (options: LaunchOptions) => {
  const sdk = SNSMobileSDK.init(accessToken, tokenExpirationHandler)
    .withHandlers({
      onStatusChanged: handleStatusChange,
      onLog: handleLog,
    })
    .withDebug(options.debug)
    .withLocale(options.locale)
    .build();

  return sdk.launch();
};
```

2. Status Management

```typescript
type SumsubStatus =
  | "Ready"
  | "Initial"
  | "Loading"
  | "Failed"
  | "Completed"
  | "ActionCompleted";

const handleStatusChange = (event: StatusChangeEvent) => {
  // Invalidate queries on status changes
  if (event.newStatus === "Completed") {
    queryClient.invalidateQueries(["user"]);
    queryClient.invalidateQueries(["userTier"]);
  }
};
```

3. Error Handling

```typescript
interface SDKError {
  code: number;
  description: string;
  correlationId?: string;
}

try {
  const result = await sdk.launch();
  handleSuccess(result);
} catch (err: unknown) {
  const error = normalizeError(err);
  handleError(error);
}
```

4. Result Processing

```typescript
interface SDKResult {
  applicantId: string;
  reviewId: string;
  reviewStatus?: "completed" | "pending" | "rejected";
  rejectLabels?: string[];
}

const handleSuccess = (result: SDKResult) => {
  // Process verification result
  // Update application state
  // Notify user
};
```

## Navigation Architecture

### Navigation Flow

```mermaid
flowchart TD
    Root[Root Layout] --> Auth{Authentication}
    Auth -->|Authenticated| App[App Layout]
    Auth -->|Unauthenticated| SignIn[Sign In]

    App --> Welcome[Welcome Screen]
    App --> Tabs[Tabs Layout]

    Tabs --> Home[Home Tab]
    Tabs --> Documents[Documents Tab]
    Tabs --> Companies[Companies Tab]
    Tabs --> Profile[Profile Tab]
```

### Stack Navigation Pattern

1. **Root Stack**

   - Handles authentication flow
   - Manages global state providers
   - Controls status bar appearance

2. **App Stack**

   - Manages welcome and tabs screens
   - Controls gesture navigation
   - Handles screen transitions

3. **Tab Stack**
   - Custom tab bar implementation
   - Tab state preservation
   - Screen-specific configurations

### Navigation Rules

1. **Authentication Flow**

   - Check token on app start
   - Redirect to sign in if no token
   - Preserve navigation state after auth

2. **Welcome Flow**

   - Show on first authenticated access
   - Enable gesture navigation
   - One-time display pattern

3. **Tab Navigation**
   - Preserve tab state during navigation
   - Custom tab bar with animations
   - Tab-specific gesture handling

### Custom Tab Bar Pattern

1. **Component Structure**

   ```typescript
   const CustomTabBar = ({ state, descriptors, navigation }) => {
     return (
       <View style={styles.container}>
         {state.routes.map((route, index) => (
           <TabItem
             key={route.key}
             route={route}
             descriptor={descriptors[route.key]}
             isFocused={state.index === index}
             onPress={() => navigation.navigate(route.name)}
           />
         ))}
       </View>
     );
   };
   ```

2. **Tab Item Pattern**

   ```typescript
   const TabItem = ({ route, descriptor, isFocused, onPress }) => {
     return (
       <TouchableOpacity
         onPress={onPress}
         style={[styles.tab, isFocused && styles.activeTab]}
       >
         <TabIcon name={route.name} active={isFocused} />
         <TabLabel {...descriptor.options} focused={isFocused} />
       </TouchableOpacity>
     );
   };
   ```

3. **State Management Pattern**

   ```typescript
   const useTabNavigation = () => {
     const navigation = useNavigation();
     const route = useRoute();

     const navigateToTab = useCallback(
       (tabName: string) => {
         navigation.navigate(tabName);
       },
       [navigation]
     );

     return {
       currentTab: route.name,
       navigateToTab,
     };
   };
   ```

### Navigation State Pattern

1. **Authentication State**

   ```typescript
   const useAuthNavigation = () => {
     const { token } = useSession();
     const navigation = useNavigation();

     useEffect(() => {
       if (!token) {
         navigation.navigate("sign-in");
       }
     }, [token]);
   };
   ```

2. **Tab State**

   ```typescript
   const useTabState = () => {
     const [previousTab, setPreviousTab] = useState(null);
     const currentTab = useRoute().name;

     useEffect(() => {
       setPreviousTab(currentTab);
     }, [currentTab]);

     return { previousTab, currentTab };
   };
   ```

3. **Modal State**

   ```typescript
   const useModalState = () => {
     const [isVisible, setIsVisible] = useState(false);
     const navigation = useNavigation();

     useEffect(() => {
       const unsubscribe = navigation.addListener("blur", () => {
         setIsVisible(false);
       });

       return unsubscribe;
     }, [navigation]);

     return { isVisible, setIsVisible };
   };
   ```
