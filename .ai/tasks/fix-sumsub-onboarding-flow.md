# Task: Fix Sumsub Onboarding Flow Issues

**Created**: 2025-07-09
**Status**: In Progress

## Issues Identified:

1. **Consent popup showing on liveness check failure**: Line 90 in onboarding-confirm-modal.tsx sets `setIsVisible(true)` in catch block
2. **Missing error state handling**: Only handles "Approved" status, ignores failure states like "Failed", "FinallyRejected", "TemporarilyDeclined"
3. **No user error feedback**: Users don't get proper error messages when liveness check fails

## Sub-tasks:

- [x] 1. Analyze current Sumsub integration and identify issues
- [x] 2. Review Sumsub status types and error handling patterns from other components
- [x] 3. Add state management for error handling in OnboardingConfirmModal
- [x] 4. Implement proper status change handler with success/failure logic
- [x] 5. Remove fallback consent popup trigger in catch block
- [x] 6. Add error notification/popup for failed liveness checks
- [ ] 7. Test the fixed flow to ensure proper behavior
- [x] 8. Update project-info.md with the fix details

## Implementation Plan:

### Status Handler Logic:

- **Success states**: "Approved", "ActionCompleted" → Show consent popup
- **Failure states**: "Failed", "FinallyRejected", "TemporarilyDeclined" → Show error message
- **Other states**: "Pending", "Incomplete" → Continue waiting
- **Catch block**: Remove `setIsVisible(true)`, show error message instead

### Error Handling:

- Add error state management
- Create error popup/notification component
- Provide clear error messages to users
- Prevent proceeding to onboarding on failure

## Files to Modify:

- `components/modals/onboarding-confirm-modal.tsx` (main fix)
- Possibly create error notification component if needed
