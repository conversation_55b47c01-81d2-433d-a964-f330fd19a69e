# VerifyMe

A secure and user-friendly mobile application for identity verification and authentication services. Built with React Native and Expo.

## Quick Start

1. Install dependencies:

   ```bash
   npm install
   ```

2. Create Development Build:

   ```bash
   npx expo prebuild
   ```

   This is required for Sumsub SDK integration.

3. Run on iOS:

   ```bash
   npx expo run:ios
   ```

   Run on Android:

   ```bash
   npx expo run:android
   ```

## Development

This project uses:

- React Native with Expo Development Build
- TypeScript for type safety
- React Native StyleSheet for styling
- Sumsub SDK for identity verification
- File-based routing

### Sumsub Integration

- Uses Sumsub React Native Module v1.34.1
- Requires Kotlin 1.7.10+ for Android
- iOS requires disabled bitcode
- See [techContext.md](memory-bank/techContext.md) for setup details

For detailed documentation about:

- Project overview and goals → [projectbrief.md](memory-bank/projectbrief.md)
- Technical setup → [techContext.md](memory-bank/techContext.md)
- Development patterns → [systemPatterns.md](memory-bank/systemPatterns.md)
- Current status → [activeContext.md](memory-bank/activeContext.md)

## Testing

You can run the app on:

- iOS Simulator (Development Build)
- Android Emulator (Development Build)
- Physical device (Development Build)

## Contributing

Please review our development patterns in [systemPatterns.md](memory-bank/systemPatterns.md) before contributing.

## Documentation

For comprehensive documentation, see the [memory-bank](memory-bank/) directory.
