// Marketplace category icons
export const MARKETPLACE_ICONS = {
  FINANCIAL_SERVICES: require("@/assets/icons/marketplace/financial-service.png"),
  BANKING: require("@/assets/icons/marketplace/banking.png"),
  ELECTRONIC_MONEY: require("@/assets/icons/marketplace/electronic-money.png"),
  GAMING: require("@/assets/icons/marketplace/gaming.png"),
  TRANSPORT: require("@/assets/icons/marketplace/transport.png"),
  TELECOMMUNICATIONS: require("@/assets/icons/marketplace/telemumunications.png"),
  RETAIL_E_COMMERCE: require("@/assets/icons/marketplace/e-com.png"),
  CRYPTO_ASSETS: require("@/assets/icons/marketplace/cripto.png"),
  INSURANCE: require("@/assets/icons/marketplace/insurance.png"),
  LAW_FIRMS: require("@/assets/icons/marketplace/law.png"),
  TRADING: require("@/assets/icons/marketplace/trading.png"),
  ENERGY_UTILITIES: require("@/assets/icons/marketplace/energy.png"),
  TRAVEL: require("@/assets/icons/marketplace/travel.png"),
  HEALTH_WELLNESS: require("@/assets/icons/marketplace/health.png"),
  EDUCATION: require("@/assets/icons/marketplace/education.png"),
  REAL_ESTATE: require("@/assets/icons/marketplace/real-estate.png"),
};

// Marketplace categories data
export interface MarketplaceCategory {
  id: string;
  title: string;
  icon: any;
  description?: string;
}

export const MARKETPLACE_CATEGORIES: MarketplaceCategory[] = [
  {
    id: "financial-services",
    title: "Financial\nServices",
    icon: MARKETPLACE_ICONS.FINANCIAL_SERVICES,
    description: "Automated KYC & KYB for Financial Services",
  },
  {
    id: "banking",
    title: "Banking",
    icon: MARKETPLACE_ICONS.BANKING,
    description: "Automated KYC and KYB for smooth and secure banking",
  },
  {
    id: "electronic-money",
    title: "Electronic\nMoney",
    icon: MARKETPLACE_ICONS.ELECTRONIC_MONEY,
    description: "Automated KYC and KYB for smooth and secure transactions",
  },
  {
    id: "gaming",
    title: "Gaming",
    icon: MARKETPLACE_ICONS.GAMING,
    description: "Participant Verification for Gaming",
  },
  {
    id: "telecommunications",
    title: "Telecommunications",
    icon: MARKETPLACE_ICONS.TELECOMMUNICATIONS,
    description: "KYC & KYB for telecommunication services",
  },
  {
    id: "retail-e-commerce",
    title: "Retail & e-Commerce",
    icon: MARKETPLACE_ICONS.RETAIL_E_COMMERCE,
    description: "Shopper Security",
  },
  {
    id: "crypto-assets",
    title: "Crypto Assets",
    icon: MARKETPLACE_ICONS.CRYPTO_ASSETS,
    description: "Secure Identity Verification to Protect Digital Assets",
  },
  {
    id: "insurance",
    title: "Insurance",
    icon: MARKETPLACE_ICONS.INSURANCE,
    description: "Seamlessly onboard and be onboarded with insurance services",
  },
  {
    id: "law-firms",
    title: "Law\nFirms",
    icon: MARKETPLACE_ICONS.LAW_FIRMS,
    description: "Seamlessly onboard and be onboarded with legal services",
  },
  {
    id: "trading",
    title: "Trading",
    icon: MARKETPLACE_ICONS.TRADING,
    description: "International trust with secure, verified trading",
  },
  {
    id: "transport",
    title: "Transport",
    icon: MARKETPLACE_ICONS.TRANSPORT,
    description: "Driver and passenger verification",
  },
  {
    id: "energy-utilities",
    title: "Energy &\nUtilities",
    icon: MARKETPLACE_ICONS.ENERGY_UTILITIES,
    description: "Customer onboarding verification",
  },
  {
    id: "travel",
    title: "Travel",
    icon: MARKETPLACE_ICONS.TRAVEL,
    description: "Traveler identity verification",
  },
  {
    id: "health-wellness",
    title: "Health &\nWellness",
    icon: MARKETPLACE_ICONS.HEALTH_WELLNESS,
    description: "Patient verification and compliance",
  },
  {
    id: "education",
    title: "Education",
    icon: MARKETPLACE_ICONS.EDUCATION,
    description: "Student and staff verification",
  },
  {
    id: "real-estate",
    title: "Real\nEstate",
    icon: MARKETPLACE_ICONS.REAL_ESTATE,
    description: "Property transaction verification",
  },
];
