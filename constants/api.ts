import { getStorageItemAsync, setStorageItemAsync } from "@/utils/storage";
import {
  API_ROUTES,
  API_URL,
  AUTH_TOKEN_KEY,
  USER_ID_KEY,
} from "./api-constants";

// Event system for token refresh notifications
type TokenRefreshListener = (newToken: string) => void;
const tokenRefreshListeners: TokenRefreshListener[] = [];

// Event system for logout notifications
type LogoutListener = () => void;
const logoutListeners: LogoutListener[] = [];

// Simple force logout function to avoid circular dependency
const forceLogout = async (): Promise<void> => {
  try {
    console.log("🚪 Forcing logout and clearing auth data");
    await setStorageItemAsync(AUTH_TOKEN_KEY, null);
    await setStorageItemAsync(USER_ID_KEY, null);
    console.log("✅ Auth data cleared");

    // Notify all logout listeners
    notifyLogout();
  } catch (error) {
    console.error("Error during force logout:", error);
  }
};

// Export the force logout function
export const triggerLogout = forceLogout;

export const onTokenRefresh = (listener: TokenRefreshListener) => {
  tokenRefreshListeners.push(listener);

  // Return unsubscribe function
  return () => {
    const index = tokenRefreshListeners.indexOf(listener);
    if (index > -1) {
      tokenRefreshListeners.splice(index, 1);
    }
  };
};

export const onLogout = (listener: LogoutListener) => {
  logoutListeners.push(listener);

  // Return unsubscribe function
  return () => {
    const index = logoutListeners.indexOf(listener);
    if (index > -1) {
      logoutListeners.splice(index, 1);
    }
  };
};

const notifyTokenRefresh = (newToken: string) => {
  tokenRefreshListeners.forEach((listener) => {
    try {
      listener(newToken);
    } catch (error) {
      console.error("Error in token refresh listener:", error);
    }
  });
};

const notifyLogout = () => {
  logoutListeners.forEach((listener) => {
    try {
      listener();
    } catch (error) {
      console.error("Error in logout listener:", error);
    }
  });
};

// Re-export constants from api-constants
export {
  API_ROUTES,
  API_URL,
  AUTH_TOKEN_KEY,
  USER_ID_KEY,
} from "./api-constants";

export const generateHeaders = (
  method: RequestInit["method"],
  body?: RequestInit["body"],
  token?: string | null,
  additionalHeaders?: HeadersInit
): RequestInit => {
  return {
    method,
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...(additionalHeaders || {}),
    },
    body,
  };
};

/**
 * Simple API client for making HTTP requests with automatic token refresh
 */
export const api = {
  /**
   * Make a GET request with automatic token refresh
   */
  get: async <T>(endpoint: string, customHeaders?: HeadersInit): Promise<T> => {
    const url = `${API_URL}${endpoint}`;
    const token = await getStorageItemAsync(AUTH_TOKEN_KEY);
    const options = generateHeaders("GET", undefined, token, customHeaders);
    return fetchWithRetry<T>(url, options);
  },

  /**
   * Make a POST request with JSON body and automatic token refresh
   */
  post: async <T>(
    endpoint: string,
    data?: any,
    customHeaders?: HeadersInit
  ): Promise<T> => {
    const url = `${API_URL}${endpoint}`;
    console.log("🔑 URL:", url);

    const body = data ? JSON.stringify(data) : undefined;
    const token = await getStorageItemAsync(AUTH_TOKEN_KEY);
    const options = generateHeaders("POST", body, token, customHeaders);
    return fetchWithRetry<T>(url, options);
  },

  /**
   * Make a PUT request with JSON body and automatic token refresh
   */
  put: async <T>(
    endpoint: string,
    data?: any,
    customHeaders?: HeadersInit
  ): Promise<T> => {
    const url = `${API_URL}${endpoint}`;
    const body = data ? JSON.stringify(data) : undefined;
    const token = await getStorageItemAsync(AUTH_TOKEN_KEY);
    const options = generateHeaders("PUT", body, token, customHeaders);
    return fetchWithRetry<T>(url, options);
  },

  /**
   * Make a DELETE request with automatic token refresh
   */
  delete: async <T>(
    endpoint: string,
    customHeaders?: HeadersInit
  ): Promise<T> => {
    const url = `${API_URL}${endpoint}`;
    const token = await getStorageItemAsync(AUTH_TOKEN_KEY);
    const options = generateHeaders("DELETE", undefined, token, customHeaders);
    return fetchWithRetry<T>(url, options);
  },

  /**
   * Make a PATCH request with JSON body and automatic token refresh
   */
  patch: async <T>(
    endpoint: string,
    data?: any,
    customHeaders?: HeadersInit
  ): Promise<T> => {
    const url = `${API_URL}${endpoint}`;
    const body = data ? JSON.stringify(data) : undefined;
    const token = await getStorageItemAsync(AUTH_TOKEN_KEY);
    const options = generateHeaders("PATCH", body, token, customHeaders);
    return fetchWithRetry<T>(url, options);
  },
};

/**
 * Enhanced fetch function that handles token refresh only when needed (on 401)
 */
async function fetchWithRetry<T>(
  url: string,
  options: RequestInit,
  isRetry = false
): Promise<T> {
  try {
    // Add timeout to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // Handle successful responses
    if (response.ok) {
      // For No Content responses
      if (response.status === 204) {
        return {} as T;
      }
      return await response.json();
    }

    // Handle 401 - try token refresh only once
    if (response.status === 401 && !isRetry) {
      console.log("🔄 Got 401, attempting token refresh...");

      const refreshed = await refreshTokenAndRetry();

      if (refreshed.success) {
        // Update the Authorization header with new token
        const newHeaders = { ...options.headers } as Record<string, string>;
        newHeaders["Authorization"] = `Bearer ${refreshed.token}`;

        // Retry the request with new token
        const newOptions = { ...options, headers: newHeaders };
        console.log("🔄 Retrying request with refreshed token");
        return fetchWithRetry<T>(url, newOptions, true); // Mark as retry
      } else {
        // Refresh failed, force logout
        console.log("❌ Token refresh failed, forcing logout");
        await forceLogout();
        throw new Error("Session expired. Please login again.");
      }
    } else if (response.status === 401) {
      // 401 after retry
      console.log("❌ Authentication failed after retry, forcing logout");
      await forceLogout();
      throw new Error("Unauthorized. Please login again.");
    } else if (response.status === 403) {
      throw new Error("You don't have permission to access this resource.");
    } else if (response.status === 404) {
      throw new Error("The requested resource was not found.");
    } else if (response.status >= 500) {
      throw new Error("Server error. Please try again later.");
    }

    // Try to get the error message from the response
    try {
      const errorData = await response.json();
      console.log("❌ API Error:", errorData);

      // Handle backend error structure: { errors: [{ message: "..." }], isSuccess: false }
      if (
        errorData.errors &&
        Array.isArray(errorData.errors) &&
        errorData.errors.length > 0
      ) {
        const firstError = errorData.errors[0];
        const errorMessage =
          firstError.customMessage ||
          firstError.message ||
          `Error: ${response.status}`;
        throw new Error(errorMessage);
      }

      // Fallback to direct message property or generic error
      throw new Error(errorData.message || `Error: ${response.status}`);
    } catch (e) {
      // If JSON parsing fails or error is already thrown, handle appropriately
      if (
        e instanceof Error &&
        e.message !== `Request failed with status: ${response.status}`
      ) {
        throw e; // Re-throw the specific error message we extracted
      }
      throw new Error(`Request failed with status: ${response.status}`);
    }
  } catch (error: any) {
    if (error.name === "AbortError") {
      throw new Error("Request timed out. Please try again.");
    }
    throw error;
  }
}

/**
 * Refresh token and return the new token
 */
async function refreshTokenAndRetry(): Promise<{
  success: boolean;
  token?: string;
}> {
  try {
    const currentToken = await getStorageItemAsync(AUTH_TOKEN_KEY);
    const userId = await getStorageItemAsync(USER_ID_KEY);

    if (!currentToken || !userId) {
      return { success: false };
    }

    const response = await fetch(`${API_URL}${API_ROUTES.REFRESH}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${currentToken}`,
      },
      body: JSON.stringify({ user_id: userId }),
    });

    if (!response.ok) {
      return { success: false };
    }

    const data = await response.json();

    if (data?.isSuccess && data?.data?.token) {
      const newToken = data.data.token.replace(/^Bearer\s+/, "");

      // Store the new token
      const { setStorageItemAsync } = await import("@/utils/storage");
      await setStorageItemAsync(AUTH_TOKEN_KEY, newToken);

      notifyTokenRefresh(newToken);

      return { success: true, token: newToken };
    }

    return { success: false };
  } catch (error) {
    console.error("Token refresh error:", error);
    return { success: false };
  }
}

/**
 * Helper to build URL query parameters
 */
export function buildQueryParams(params: Record<string, any>): string {
  const validParams = Object.entries(params).filter(
    ([_, value]) => value !== undefined && value !== null && value !== ""
  );

  if (validParams.length === 0) return "";

  const queryString = validParams
    .map(([key, value]) => {
      if (Array.isArray(value)) {
        return value
          .map(
            (item) => `${encodeURIComponent(key)}=${encodeURIComponent(item)}`
          )
          .join("&");
      }
      return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
    })
    .join("&");

  return `?${queryString}`;
}
