import { FormInput } from "@/components/form/form-input";
import { FormPassword } from "@/components/form/form-password";
import { FormWrapper } from "@/components/form/form-wrapper";
import AppHeader from "@/components/layout/app-header";
import { AppButton } from "@/components/ui/app-button";
import { AppText, AppTextWithLink } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { registrationSchema } from "@/lib/validation/auth-schema";
import { useSession } from "@/providers/auth/auth-provider";
import { RegistrationTypeOption } from "@/types/auth";
import {
  getKeyboardAvoidingBehavior,
  getKeyboardVerticalOffset,
  isVerySmallDevice,
} from "@/utils/platform-utils";
import { verticalScale } from "@/utils/styling-scale";
import { zodResolver } from "@hookform/resolvers/zod";
import { router } from "expo-router";
import React, { useCallback } from "react";
import { Controller, useForm } from "react-hook-form";
import {
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { z } from "zod";

type RegistrationFormData = z.infer<typeof registrationSchema>;

export default function RegistrationKYB() {
  const { signUp, isLoading, error, clearError } = useSession();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
    defaultValues: {
      registrationType: RegistrationTypeOption.KYB,
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = (data: RegistrationFormData) => signUp(data);

  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  return (
    <KeyboardAvoidingView
      behavior={getKeyboardAvoidingBehavior()}
      keyboardVerticalOffset={getKeyboardVerticalOffset()}
      style={styles.container}
    >
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <View style={styles.container}>
          {/* Header */}
          <AppHeader title="Company Registration" />

          {/* Form section */}
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={[
              styles.scrollViewContent,
              isVerySmallDevice && styles.scrollViewContentSmall,
            ]}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <FormWrapper enableFlexWrap>
              <Controller
                control={control}
                name="firstName"
                render={({ field: { onChange, onBlur, value } }) => (
                  <FormInput
                    label="Company name"
                    name="firstName"
                    placeholder="Company name"
                    onChangeText={onChange}
                    onBlur={onBlur}
                    value={value}
                    error={errors.firstName?.message}
                    returnKeyType="next"
                    blurOnSubmit={false}
                    enablesReturnKeyAutomatically
                  />
                )}
              />

              <Controller
                control={control}
                name="lastName"
                render={({ field: { onChange, onBlur, value } }) => (
                  <FormInput
                    label="Contact person"
                    name="lastName"
                    placeholder="Contact person"
                    onChangeText={onChange}
                    onBlur={onBlur}
                    value={value}
                    error={errors.lastName?.message}
                    returnKeyType="next"
                    blurOnSubmit={false}
                    enablesReturnKeyAutomatically
                  />
                )}
              />

              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, onBlur, value } }) => (
                  <FormInput
                    label="Email address"
                    name="email"
                    placeholder="Email address"
                    onChangeText={onChange}
                    onBlur={() => {
                      // Trim whitespace when user finishes editing
                      const trimmedValue = value?.trim() || "";
                      if (trimmedValue !== value) {
                        onChange(trimmedValue);
                      }
                      onBlur();
                    }}
                    value={value}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                    error={errors.email?.message}
                    returnKeyType="next"
                    blurOnSubmit={false}
                    enablesReturnKeyAutomatically
                  />
                )}
              />

              <Controller
                control={control}
                name="password"
                render={({ field: { onChange, onBlur, value } }) => (
                  <FormPassword
                    label="Password"
                    name="password"
                    placeholder="Password"
                    onChangeText={onChange}
                    onBlur={onBlur}
                    value={value}
                    error={errors.password?.message}
                    returnKeyType="next"
                    blurOnSubmit={false}
                    enablesReturnKeyAutomatically
                  />
                )}
              />

              <Controller
                control={control}
                name="confirmPassword"
                render={({ field: { onChange, onBlur, value } }) => (
                  <FormPassword
                    label="Confirm Password"
                    name="confirmPassword"
                    placeholder="Confirm password"
                    onChangeText={onChange}
                    onBlur={onBlur}
                    value={value}
                    error={errors.confirmPassword?.message}
                    returnKeyType="done"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    enablesReturnKeyAutomatically
                  />
                )}
              />

              {/* Register button */}
              <AppButton
                onPress={handleSubmit(onSubmit)}
                text="REGISTER"
                disabled={isLoading}
                isLoading={isLoading}
              />

              {error && (
                <AppText variant="destructive" size="sm">
                  {error}
                </AppText>
              )}
            </FormWrapper>
            {/* Bottom text with link */}
            <View
              style={[
                styles.bottomContainer,
                isVerySmallDevice && styles.bottomContainerSmall,
              ]}
            >
              <AppTextWithLink
                textBefore="Already have an account?"
                linkText="Log in"
                onLinkPress={() => {
                  clearError(); // Clear any registration errors before navigating
                  router.push("/sign-in");
                }}
                variant="primary"
                linkVariant="link"
                weight="normal"
                size="lg"
              />
            </View>
          </ScrollView>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: verticalScale(24),
  },
  scrollViewContentSmall: {
    paddingBottom: verticalScale(12),
  },
  bottomContainer: {
    alignItems: "center",
    marginTop: verticalScale(8),
  },
  bottomContainerSmall: {
    marginTop: verticalScale(4),
  },
});
