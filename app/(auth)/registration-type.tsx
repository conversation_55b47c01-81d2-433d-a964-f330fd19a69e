import { ImageSelectionCard } from "@/components/form/image-selection-card";
import Logo from "@/components/logo";
import { AppButton } from "@/components/ui/app-button";
import { AppText, AppTextWithLink } from "@/components/ui/app-text";
import { ICONS } from "@/constants/icons";
import { COLORS } from "@/constants/theme";
import { RegistrationTypeOption } from "@/types/auth";
import {
  getKeyboardAvoidingBehavior,
  getKeyboardVerticalOffset,
  isVerySmallDevice,
} from "@/utils/platform-utils";
import { scale, verticalScale } from "@/utils/styling-scale";
import { router } from "expo-router";
import React, { useCallback, useState } from "react";
import {
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from "react-native";

export default function RegistrationType() {
  const [selectedType, setSelectedType] = useState<RegistrationTypeOption>(
    RegistrationTypeOption.KYC
  );

  const handleContinue = () =>
    router.push(
      selectedType === RegistrationTypeOption.KYC
        ? "/registration-kyc"
        : "/registration-kyb"
    );

  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  return (
    <KeyboardAvoidingView
      behavior={getKeyboardAvoidingBehavior()}
      keyboardVerticalOffset={getKeyboardVerticalOffset()}
      style={styles.container}
    >
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <View style={styles.container}>
          {/* Logo section */}
          <View style={styles.logoContainer}>
            <Logo />
          </View>

          {/* Content section */}
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollViewContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <View style={styles.contentContainer}>
              <AppText
                variant="primary"
                weight="semibold"
                size="xl"
                style={styles.titleText}
              >
                You are registering as:
              </AppText>

              <View style={styles.cardsContainer}>
                {/* Me option */}
                <ImageSelectionCard
                  title="Individual"
                  imageSource={ICONS.KYC_ICON}
                  isSelected={selectedType === RegistrationTypeOption.KYC}
                  onPress={() => setSelectedType(RegistrationTypeOption.KYC)}
                />

                {/* Business option */}
                <ImageSelectionCard
                  title="Business"
                  imageSource={ICONS.KYC_COMPANY_ICON}
                  isSelected={selectedType === RegistrationTypeOption.KYB}
                  onPress={() => setSelectedType(RegistrationTypeOption.KYB)}
                />
              </View>

              {/* Button */}
              <View style={styles.buttonContainer}>
                <AppButton text="NEXT" onPress={handleContinue} />
              </View>
            </View>
            {/* Bottom text with link */}
            <View
              style={[
                styles.bottomContainer,
                isVerySmallDevice && styles.bottomContainerSmall,
              ]}
            >
              <AppTextWithLink
                textBefore="Already have an account?"
                linkText="Log in"
                onLinkPress={() => router.push("/sign-in")}
                variant="primary"
                linkVariant="link"
                weight="normal"
                size="lg"
              />
            </View>
          </ScrollView>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: verticalScale(40),
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingHorizontal: scale(24),
  },
  contentContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: scale(208),
    alignSelf: "center",
  },
  titleText: {
    textAlign: "center",
    marginBottom: verticalScale(40),
  },
  cardsContainer: {
    gap: verticalScale(40),
    marginBottom: verticalScale(20),
    alignItems: "center",
  },
  buttonContainer: {
    width: "100%",
  },
  bottomContainer: {
    alignItems: "center",
    marginTop: verticalScale(20),
    marginBottom: verticalScale(20),
  },
  bottomContainerSmall: {
    marginTop: verticalScale(12),
    marginBottom: verticalScale(16),
  },
});
