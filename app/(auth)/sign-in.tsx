import { FormCheckbox } from "@/components/form/form-checkbox";
import { FormInput } from "@/components/form/form-input";
import { FormPassword } from "@/components/form/form-password";
import { FormWrapper } from "@/components/form/form-wrapper";
import Logo from "@/components/logo";
import { AppButton } from "@/components/ui/app-button";
import { AppText, AppTextWithLink } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { loginSchema } from "@/lib/validation/auth-schema";
import { useSession } from "@/providers/auth/auth-provider";
import {
  getKeyboardAvoidingBehavior,
  getKeyboardVerticalOffset,
  isVerySmallDevice,
} from "@/utils/platform-utils";
import { scale, verticalScale } from "@/utils/styling-scale";
import { zodResolver } from "@hookform/resolvers/zod";
import { router } from "expo-router";
import React, { useCallback, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import {
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";

interface LoginFormData {
  email: string;
  password: string;
}

export default function SignIn() {
  const { signIn, isLoading, error, clearError } = useSession();
  const [rememberMe, setRememberMe] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormData) => signIn(data);

  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  return (
    <KeyboardAvoidingView
      behavior={getKeyboardAvoidingBehavior()}
      keyboardVerticalOffset={getKeyboardVerticalOffset()}
      style={styles.container}
    >
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <View style={styles.container}>
          {/* Logo section */}
          <View style={styles.logoContainer}>
            <Logo />
          </View>

          {/* Form section */}
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={[
              styles.scrollViewContent,
              isVerySmallDevice && styles.scrollViewContentSmall,
            ]}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <FormWrapper>
              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, onBlur, value } }) => (
                  <FormInput
                    label="Email address"
                    name="email"
                    placeholder="Email address"
                    onChangeText={onChange}
                    onBlur={() => {
                      // Trim whitespace when user finishes editing
                      const trimmedValue = value?.trim() || "";
                      if (trimmedValue !== value) {
                        onChange(trimmedValue);
                      }
                      onBlur();
                    }}
                    value={value}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                    error={errors.email?.message}
                    returnKeyType="next"
                    enablesReturnKeyAutomatically
                  />
                )}
              />

              <Controller
                control={control}
                name="password"
                render={({ field: { onChange, onBlur, value } }) => (
                  <FormPassword
                    label="Password"
                    name="password"
                    placeholder="Password"
                    onChangeText={onChange}
                    onBlur={onBlur}
                    value={value}
                    error={errors.password?.message}
                    returnKeyType="done"
                    onSubmitEditing={handleSubmit(onSubmit)}
                    enablesReturnKeyAutomatically
                  />
                )}
              />

              {/* Remember me and Forgot password row */}
              <View style={[styles.rememberForgotContainer]}>
                <View>
                  <FormCheckbox
                    label="Remember me"
                    checked={rememberMe}
                    onToggle={() => setRememberMe(!rememberMe)}
                    style={[
                      styles.checkboxStyle,
                      isVerySmallDevice && styles.checkboxStyleSmall,
                    ]}
                  />
                </View>

                <View
                  style={isVerySmallDevice ? styles.centeredWrapper : undefined}
                >
                  <TouchableOpacity
                    style={[
                      styles.forgotPasswordTouchable,
                      isVerySmallDevice && styles.forgotPasswordTouchableSmall,
                    ]}
                  >
                    <AppText variant="link" style={styles.forgotPasswordText}>
                      Forgot password?
                    </AppText>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Login button */}
              <AppButton
                onPress={handleSubmit(onSubmit)}
                text="LOGIN"
                variant="default"
                disabled={isLoading}
                isLoading={isLoading}
              />

              {error && (
                <AppText variant="destructive" size="sm">
                  {error}
                </AppText>
              )}
            </FormWrapper>
            {/* Bottom text with link */}
            <View
              style={[
                styles.bottomContainer,
                isVerySmallDevice && styles.bottomContainerSmall,
              ]}
            >
              <AppTextWithLink
                textBefore="Don't have an account?"
                linkText="Register"
                onLinkPress={() => {
                  clearError(); // Clear any login errors before navigating
                  router.push("/registration-type");
                }}
                variant="primary"
                linkVariant="link"
                weight="normal"
                size="lg"
              />
            </View>
          </ScrollView>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: verticalScale(40),
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: verticalScale(24),
  },
  scrollViewContentSmall: {
    paddingBottom: verticalScale(16),
  },
  rememberForgotContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: verticalScale(24),
    marginTop: verticalScale(4),
    width: "100%",
    flexWrap: "wrap",
    gap: scale(12),
  },
  rememberForgotContainerSmall: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    width: "100%",
    gap: verticalScale(12),
    // borderWidth: 1, // Debug - uncomment to see container bounds
    // borderColor: 'red',
  },
  checkboxStyle: {
    flexShrink: 0,
  },
  checkboxStyleSmall: {
    alignSelf: "center",
    justifyContent: "center",
  },
  forgotPasswordTouchable: {
    paddingVertical: verticalScale(8),
    paddingHorizontal: scale(8),
    minHeight: verticalScale(44),
    justifyContent: "center",
    alignItems: "center",
    flexShrink: 0,
  },
  forgotPasswordTouchableSmall: {
    alignSelf: "center",
    justifyContent: "center",
    alignItems: "center",
    width: "auto",
  },
  forgotPasswordText: {
    color: COLORS.primary,
    textAlign: "center",
  },
  bottomContainer: {
    alignItems: "center",
    marginBottom: verticalScale(40),
    paddingHorizontal: scale(16),
  },
  bottomContainerSmall: {
    marginBottom: verticalScale(24),
    paddingHorizontal: scale(12),
  },
  centeredWrapper: {
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
  },
});
