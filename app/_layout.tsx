import { useFonts } from "expo-font";
import { Slot } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";

import { ErrorBoundary } from "@/components/ui/error-boundary";
import { COLORS } from "@/constants/theme";
import { useKYBDeepLink } from "@/hooks/useKYBDeepLink";
import Providers from "@/providers/providers";
import { StyleSheet } from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  // Handle KYB deep link returns
  useKYBDeepLink();

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }
  return (
    <ErrorBoundary>
      <Providers>
        <SafeAreaProvider>
          <SafeAreaView style={styles.container}>
            <Slot screenOptions={{ headerShown: false }} />
          </SafeAreaView>
        </SafeAreaProvider>
        <StatusBar style="dark" />
      </Providers>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
});
