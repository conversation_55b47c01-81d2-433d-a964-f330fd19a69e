import { DocumentImageViewer } from "@/components/documents/document-image-viewer";
import DocumentItem from "@/components/documents/document-item";
import { PDFPreviewModal } from "@/components/documents/pdf-preview-modal";
import { FormSearch } from "@/components/form/form-search";
import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import UserHeader from "@/components/layout/user-header";
import EmptyState from "@/components/ui/empty-state";
import { COLORS } from "@/constants/theme";
import { useCurrentUser, useUserDocuments } from "@/services/user/user-hooks";
import { Document } from "@/types/document";
import { verticalScale } from "@/utils/styling-scale";
import { FlashList } from "@shopify/flash-list";
import { router } from "expo-router";
import { useMemo, useState } from "react";
import { ActivityIndicator, RefreshControl, View } from "react-native";

export default function MyDocuments() {
  const { data: currentUser, isPending: isUserPending } = useCurrentUser();

  const {
    data: documents,
    refetch,
    isLoading,
  } = useUserDocuments(currentUser?.applicant_id);

  const handleRefresh = () => {
    refetch();
  };

  const [searchQuery, setSearchQuery] = useState("");

  // Image viewer state
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedDocumentIndex, setSelectedDocumentIndex] = useState(0);

  // PDF viewer state
  const [pdfViewerVisible, setPdfViewerVisible] = useState(false);
  const [selectedPDFDocument, setSelectedPDFDocument] =
    useState<Document | null>(null);

  const filteredDocuments = useMemo(() => {
    return documents?.data?.filter((doc) =>
      doc.fileName.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [documents, searchQuery]);

  const refreshing = isUserPending || isLoading;

  // Handle image press to open viewer
  const handleImagePress = (document: Document) => {
    const docIndex =
      filteredDocuments?.findIndex(
        (doc) => doc.fileName === document.fileName
      ) || 0;
    setSelectedDocumentIndex(docIndex);
    setImageViewerVisible(true);
  };

  // Handle close image viewer
  const handleCloseImageViewer = () => {
    setImageViewerVisible(false);
  };

  // Handle PDF press to open PDF viewer
  const handlePDFPress = (document: Document) => {
    setSelectedPDFDocument(document);
    setPdfViewerVisible(true);
  };

  // Handle close PDF viewer
  const handleClosePDFViewer = () => {
    setPdfViewerVisible(false);
    setSelectedPDFDocument(null);
  };

  // Navigation handlers
  const handleNotificationPress = () => {
    console.log("🔔 Notification bell pressed from my documents");
    router.push("/(app)/(tabs)/(home)/notifications");
  };

  if (refreshing) {
    return (
      <ActivityIndicator
        size="large"
        color={COLORS.primary}
        style={{
          paddingVertical: verticalScale(20),
        }}
      />
    );
  }

  return (
    <View style={pageStyle.container}>
      <FlashList
        data={filteredDocuments}
        contentContainerStyle={pageStyle.listContainer}
        ListHeaderComponent={
          <View>
            <AppHeader title="My Documents" grayColor hideBackButton />

            <UserHeader onPressNotification={handleNotificationPress} />

            <FormSearch
              placeholder="Search documents"
              value={searchQuery}
              onChangeText={setSearchQuery}
              onClear={() => setSearchQuery("")}
              containerStyle={pageStyle.searchContainer}
            />
          </View>
        }
        renderItem={({ item }) => (
          <DocumentItem
            id={item.idDocDefCountry ?? ""}
            name={item.fileName}
            fileType={item.idDocDefIdDocType ?? ""}
            thumbnailUrl={item.fileS3PathThumbnail}
            document={item}
            onImagePress={() => handleImagePress(item)}
            onPDFPress={() => handlePDFPress(item)}
          />
        )}
        estimatedItemSize={2}
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
          />
        }
        ListEmptyComponent={<EmptyState text="No documents found" />}
      />

      {/* Image Viewer Modal */}
      {filteredDocuments && (
        <DocumentImageViewer
          visible={imageViewerVisible}
          onRequestClose={handleCloseImageViewer}
          documents={filteredDocuments}
          initialIndex={selectedDocumentIndex}
        />
      )}

      {/* PDF Preview Modal */}
      <PDFPreviewModal
        isVisible={pdfViewerVisible}
        onClose={handleClosePDFViewer}
        document={selectedPDFDocument}
      />
    </View>
  );
}
