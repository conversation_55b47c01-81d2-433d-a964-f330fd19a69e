import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import { NotificationList } from "@/components/notifications/notification-list";
import { useNotificationContext } from "@/providers/notification/notification-provider";
import { NotificationType } from "@/types/notification";
import { router } from "expo-router";
import React from "react";
import { Alert, View } from "react-native";

export default function NotificationsScreen() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    isLoading,
  } = useNotificationContext();

  const handleNotificationPress = (notification: any) => {
    // Mark as read when pressed
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Handle navigation based on notification type
    switch (notification.type) {
      case NotificationType.DOCUMENT_APPROVED:
      case NotificationType.DOCUMENT_REJECTED:
      case NotificationType.DOCUMENT_PENDING:
        // Navigate to documents tab
        router.push("/(app)/(tabs)/my-documents");
        break;

      case NotificationType.COMPANY_CONSENT:
        // Navigate to companies tab
        router.push("/(app)/my-companies");
        break;

      case NotificationType.TIER_UPGRADE:
        // Navigate to profile tab where tier info is shown
        router.push("/(app)/(tabs)/profile");
        break;

      case NotificationType.INTERVIEW:
        // Show alert for interview notifications
        Alert.alert(
          notification.title,
          notification.message +
            "\n\nNote: Interview requests are handled automatically via popup modals.",
          [{ text: "OK" }]
        );
        break;

      case NotificationType.SYSTEM:
      case NotificationType.WARNING:
      default:
        // Show alert with full message for system notifications
        Alert.alert(notification.title, notification.message, [{ text: "OK" }]);
        break;
    }
  };

  const handleRefresh = () => {
    // Refresh notifications
    // The useNotifications hook will automatically refetch data
  };

  const handleMarkAllAsRead = () => {
    if (unreadCount === 0) return;

    Alert.alert(
      "Mark All as Read",
      `Mark all ${unreadCount} notifications as read?`,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Mark All",
          onPress: () => {
            markAllAsRead();
          },
        },
      ]
    );
  };

  const handleClearAll = () => {
    if (notifications.length === 0) return;

    Alert.alert(
      "Clear All Notifications",
      `Delete all ${notifications.length} notifications? This action cannot be undone.`,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Clear All",
          style: "destructive",
          onPress: () => {
            clearAllNotifications();
          },
        },
      ]
    );
  };

  return (
    <View style={pageStyle.container}>
      <AppHeader
        title={`Notifications ${unreadCount > 0 ? `(${unreadCount})` : ""}`}
        onBackPress={() => router.back()}
      />
      <NotificationList
        notifications={notifications}
        isLoading={isLoading}
        unreadCount={unreadCount}
        onRefresh={handleRefresh}
        onNotificationPress={handleNotificationPress}
        onMarkAsRead={markAsRead}
        onMarkAllAsRead={handleMarkAllAsRead}
        onDeleteNotification={removeNotification}
        onClearAll={handleClearAll}
      />
    </View>
  );
}
