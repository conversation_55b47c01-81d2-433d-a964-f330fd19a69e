import CompanyItem from "@/components/companies/company-item";
import MarketplaceCategories from "@/components/companies/marketplace-categories";
import { FormSearch } from "@/components/form/form-search";
import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import UserHeader from "@/components/layout/user-header";
import { AppText } from "@/components/ui/app-text";
import EmptyState from "@/components/ui/empty-state";
import {
  MARKETPLACE_CATEGORIES,
  MarketplaceCategory,
} from "@/constants/marketplace-icons";
import { COLORS } from "@/constants/theme";
import { useCompanies } from "@/services/company/company-hooks";
import { useCurrentUser } from "@/services/user/user-hooks";
import { RegistrationTypeOption } from "@/types/auth";
import { scale, verticalScale } from "@/utils/styling-scale";
import Ionicons from "@expo/vector-icons/Ionicons";
import { FlashList } from "@shopify/flash-list";
import { router } from "expo-router";
import { useMemo, useRef, useState, useCallback } from "react";
import {
  ActivityIndicator,
  Alert,
  Pressable,
  RefreshControl,
  StyleSheet,
  View,
} from "react-native";

// Map category IDs to company category values
const CATEGORY_MAPPING: Record<string, string[]> = {
  "financial-services": ["financial-services", "financial"],
  banking: ["banking"],
  "electronic-money": ["electronic-money", "fintech", "payments"],
  gaming: ["gaming", "entertainment"],
  transport: ["transport", "transportation", "logistics"],
  telecommunications: ["telecommunications", "telecom"],
  "retail-e-commerce": ["retail", "e-commerce", "ecommerce"],
  "crypto-assets": ["crypto", "cryptocurrency", "blockchain"],
  insurance: ["insurance"],
  "law-firms": ["legal", "law", "law-firms"],
  trading: ["trading", "investment"],
  "energy-utilities": ["energy", "utilities", "power"],
  travel: ["travel", "tourism", "hospitality"],
  "health-wellness": ["health", "wellness", "healthcare", "medical"],
  education: ["education", "learning", "academic"],
  "real-estate": ["real-estate", "property", "realty"],
};

export default function HomeScreen() {
  const { data: companies, isLoading, refetch, isFetching } = useCompanies();
  const { data: user, isLoading: userLoading } = useCurrentUser();
  const flashListRef = useRef<FlashList<any>>(null);
  const headerRef = useRef<View>(null);
  const activeFiltersRef = useRef<View>(null);

  const handleRefresh = () => {
    refetch();
  };

  // Scroll to companies section with smooth animation
  const scrollToCompanies = useCallback(() => {
    if (flashListRef.current) {
      // Use setTimeout to ensure state updates are processed first
      setTimeout(() => {
        try {
          // Simple scroll to end where companies are located
          flashListRef.current?.scrollToIndex({
            index: 0,
            animated: true,
            viewOffset: 100,
            viewPosition: 0.5,
          });
        } catch (error) {
          console.error("Scroll error:", error);
        }
      }, 200); // Small delay to ensure filtering is complete
    }
  }, []);

  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const filteredCompanies = useMemo(() => {
    let filtered = companies?.result || [];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter((company) =>
        company.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filters (multiple categories with OR logic)
    if (selectedCategories.length > 0) {
      filtered = filtered.filter((company) => {
        return selectedCategories.some((selectedCategory) => {
          const categoryValues = CATEGORY_MAPPING[selectedCategory] || [
            selectedCategory,
          ];
          return categoryValues.some((cat) =>
            company.category?.toLowerCase().includes(cat.toLowerCase())
          );
        });
      });
    }

    return filtered;
  }, [companies, searchQuery, selectedCategories]);

  const handleCategoryPress = (category: MarketplaceCategory) => {
    try {
      setSelectedCategories((prev) => {
        const newCategories = prev.includes(category.id)
          ? prev.filter((c) => c !== category.id) // Remove category if already selected
          : [...prev, category.id]; // Add category if not selected

        // Scroll to companies section when a category is selected/deselected
        // This ensures users can immediately see the filtered results
        scrollToCompanies();

        return newCategories;
      });
    } catch (error) {
      console.error("Category selection error:", error);
      Alert.alert("Error", "Failed to select category");
    }
  };

  const handleClearFilters = () => {
    setSelectedCategories([]);
    setSearchQuery("");
    // Scroll to companies section to show all companies after clearing filters
    scrollToCompanies();
  };

  const refreshing = isFetching || isLoading || userLoading;

  if (refreshing) {
    return (
      <ActivityIndicator
        size="large"
        color={COLORS.primary}
        style={{
          paddingVertical: verticalScale(20),
        }}
      />
    );
  }

  return (
    <View style={pageStyle.container}>
      <FlashList
        ref={flashListRef}
        contentContainerStyle={pageStyle.listContainer}
        data={filteredCompanies}
        ListHeaderComponent={
          <View style={[styles.customHeaderContainer]}>
            <View ref={headerRef}>
              <AppHeader title="Marketplace" grayColor hideBackButton />
              <UserHeader />

              <FormSearch
                placeholder="Search"
                value={searchQuery}
                onChangeText={setSearchQuery}
                onClear={handleClearFilters}
                containerStyle={[
                  pageStyle.searchContainer,
                  selectedCategories.length > 0 &&
                    styles.searchContainerWithFilters,
                ]}
              />

              {/* Active Filters */}
            </View>
            <View>
              <View style={styles.categoriesSection}>
                {/* Marketplace Categories Section */}
                <View style={styles.categoriesHeader}>
                  <AppText variant="primary" size="base" weight="semibold">
                    Browse by Category
                  </AppText>
                </View>
                <MarketplaceCategories onCategoryPress={handleCategoryPress} />
              </View>

              {/* Active Filters */}
              <View ref={activeFiltersRef}>
                <ActiveFilters
                  selectedCategories={selectedCategories}
                  setSelectedCategories={setSelectedCategories}
                  handleClearFilters={handleClearFilters}
                  onFilterChange={scrollToCompanies}
                />
              </View>

              {/* Companies Section Header */}
              <View>
                {filteredCompanies && filteredCompanies.length > 0 && (
                  <View style={styles.companiesHeader}>
                    <AppText variant="primary" size="base" weight="semibold">
                      {selectedCategories.length > 0
                        ? "Filtered Companies"
                        : "All Companies"}
                    </AppText>
                    <AppText variant="secondary" size="sm" weight="medium">
                      {filteredCompanies.length} companies{" "}
                      {selectedCategories.length > 0 ? "found" : "available"}
                    </AppText>
                  </View>
                )}
              </View>
            </View>
          </View>
        }
        renderItem={({ item }) => (
          <CompanyItem
            {...item}
            userType={user?.registrationtype as RegistrationTypeOption}
            userTier={user?.tierAsNumber || 0}
          />
        )}
        estimatedItemSize={50}
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
          />
        }
        ListEmptyComponent={<EmptyState text="No companies found" />}
      />
    </View>
  );
}

// Active Filters Component
interface ActiveFiltersProps {
  selectedCategories: string[];
  setSelectedCategories: React.Dispatch<React.SetStateAction<string[]>>;
  handleClearFilters: () => void;
  onFilterChange?: () => void;
}

const ActiveFilters = ({
  selectedCategories,
  setSelectedCategories,
  handleClearFilters,
  onFilterChange,
}: ActiveFiltersProps) => {
  if (selectedCategories.length === 0) return null;

  return (
    <View style={styles.activeFilters}>
      <View style={styles.filterRow}>
        <View style={styles.filterTags}>
          {selectedCategories.map((category) => (
            <Pressable
              key={category}
              style={styles.filterTag}
              onPress={() => {
                setSelectedCategories((prev: string[]) =>
                  prev.filter((c: string) => c !== category)
                );
                // Trigger scroll to companies when individual filter is removed
                onFilterChange?.();
              }}
            >
              <AppText
                variant="primary"
                size="xs"
                weight="medium"
                style={styles.filterTagText}
              >
                {MARKETPLACE_CATEGORIES.find(
                  (cat) => cat.id === category
                )?.title.replace("\n", " ") || category}
              </AppText>
              <Ionicons
                name="close"
                size={scale(12)}
                color={COLORS.primary}
                style={styles.closeIcon}
              />
            </Pressable>
          ))}
        </View>
        <Pressable onPress={handleClearFilters} style={styles.clearAllButton}>
          <Ionicons
            name="trash-outline"
            size={scale(16)}
            color={COLORS.primary}
          />
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  companiesHeader: {
    paddingHorizontal: scale(16),
    paddingBottom: verticalScale(4),
    marginBottom: verticalScale(5),
  },

  categoriesHeader: {
    paddingHorizontal: scale(16),
    paddingBottom: verticalScale(8),
  },

  categoriesSection: {
    marginBottom: verticalScale(8),
  },

  activeFilters: {
    paddingHorizontal: scale(10),
    paddingTop: verticalScale(2),
    paddingBottom: verticalScale(4),
    borderBottomWidth: 1,
    borderBottomColor: COLORS.grayLight,
    marginBottom: verticalScale(4),
  },

  filterRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },

  filterTags: {
    flexDirection: "row",
    flexWrap: "wrap",
    flex: 1,
  },

  filterTag: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: verticalScale(4),
    paddingHorizontal: scale(8),
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: scale(12),
    marginRight: scale(8),
    marginBottom: verticalScale(6),
  },

  filterTagText: {
    marginRight: scale(4),
  },

  closeIcon: {
    marginLeft: scale(2),
  },

  clearAllButton: {
    padding: scale(6),
    borderRadius: scale(8),
  },

  searchContainerWithFilters: {
    marginBottom: 0,
  },

  customHeaderContainer: {
    gap: verticalScale(2),
  },
});
