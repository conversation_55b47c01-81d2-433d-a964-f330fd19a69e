import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import UserHeader from "@/components/layout/user-header";
import { SignOutConfirmationModal } from "@/components/modals/sign-out-confirmation-modal";
import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { useSession } from "@/providers/auth/auth-provider";
import { useCurrentUser } from "@/services/user/user-hooks";
import { RegistrationTypeOption } from "@/types/auth";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import { Pressable, ScrollView, StyleSheet, View } from "react-native";

interface SectionItemProps {
  title: string;
  hasArrow?: boolean;
  isExpanded?: boolean;
  onPress?: () => void;
  children?: React.ReactNode;
  showBottomBorder?: boolean;
}

const SectionItem = ({
  title,
  hasArrow = true,
  isExpanded = false,
  onPress,
  children,
  showBottomBorder = true,
}: SectionItemProps) => (
  <View style={styles.itemContainer}>
    <Pressable style={styles.sectionItem} onPress={onPress}>
      <View style={styles.sectionContent}>
        <AppText
          variant="primary"
          size="sm"
          weight={children ? "semibold" : "normal"}
          style={styles.sectionTitle}
        >
          {title}
        </AppText>
        {children && <View style={styles.subItemsContainer}>{children}</View>}
      </View>
      {hasArrow && (
        <Ionicons
          name={isExpanded ? "chevron-down-outline" : "chevron-forward-outline"}
          size={18}
          color={COLORS.secondary}
        />
      )}
    </Pressable>
    {showBottomBorder && <View style={styles.bottomBorder} />}
  </View>
);

interface SubItemProps {
  title: string;
  onPress?: () => void;
}

const SubItem = ({ title, onPress }: SubItemProps) => (
  <Pressable onPress={onPress} style={styles.subItemPressable}>
    <AppText variant="secondary" size="sm" style={styles.subItemText}>
      {title}
    </AppText>
  </Pressable>
);

export default function Profile() {
  const { signOut } = useSession();
  const { data: user } = useCurrentUser();
  const [isProfileExpanded, setIsProfileExpanded] = useState(true); // Start expanded as in Figma

  const [isSignOutModalVisible, setIsSignOutModalVisible] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  // Determine if user is KYB (corporate) or KYC (individual)
  const isKYBUser = user?.registrationtype === RegistrationTypeOption.KYB;

  const handleNotificationPress = () => {
    router.push("/(app)/(tabs)/(home)/notifications");
  };

  const handleSignOutPress = () => {
    setIsSignOutModalVisible(true);
  };

  const handleSignOutConfirm = async () => {
    try {
      setIsSigningOut(true);
      signOut();
    } catch (error) {
      console.error("Sign out error:", error);
    } finally {
      setIsSigningOut(false);
      setIsSignOutModalVisible(false);
    }
  };

  const handleSignOutCancel = () => {
    setIsSignOutModalVisible(false);
  };

  const headerTitle = isKYBUser ? "Company Profile" : "My Profile";

  return (
    <View style={pageStyle.container}>
      <View style={pageStyle.listContainer}>
        {/* Header */}
        <AppHeader title={headerTitle} grayColor hideBackButton />
        <UserHeader onPressNotification={handleNotificationPress} />

        {/* Content List */}
        <View>
          {/* Profile Section - Expandable */}
          <SectionItem
            title="Profile"
            hasArrow={true}
            isExpanded={isProfileExpanded}
            onPress={() => setIsProfileExpanded(!isProfileExpanded)}
            showBottomBorder={true}
          >
            {isProfileExpanded && (
              <>
                <SubItem
                  title={isKYBUser ? "Company Profile" : "Personal information"}
                  onPress={() => router.push("/(app)/personal-information")}
                />
                <SubItem
                  title="Sign In method"
                  onPress={() => router.push("/(app)/sign-in-methods")}
                />
              </>
            )}
          </SectionItem>

          {/* My Documents */}
          <SectionItem
            title="My Documents"
            onPress={() => router.push("/(app)/(tabs)/my-documents")}
            showBottomBorder={true}
          />

          {/* My Companies */}
          <SectionItem
            title="My Companies"
            onPress={() => router.push("/(app)/my-companies")}
            showBottomBorder={true}
          />
        </View>

        {/* Hidden Logout Button */}
        <View style={styles.logoutSection}>
          <AppButton
            text="Sign Out"
            variant="outline"
            size="sm"
            onPress={handleSignOutPress}
            buttonStyle={styles.logoutButton}
            textStyle={styles.logoutButtonText}
          />
        </View>

        {/* Sign Out Confirmation Modal */}
        <SignOutConfirmationModal
          isVisible={isSignOutModalVisible}
          onConfirm={handleSignOutConfirm}
          onCancel={handleSignOutCancel}
          isLoading={isSigningOut}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: verticalScale(100),
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: scale(24),
    paddingVertical: verticalScale(16),
    backgroundColor: COLORS.background,
  },
  headerButton: {
    padding: scale(4),
  },
  headerTitle: {
    color: COLORS.gray,
  },
  userInfoSection: {
    paddingHorizontal: scale(17),
    paddingVertical: verticalScale(0),
    backgroundColor: COLORS.background,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: verticalScale(12),
  },
  avatarContainer: {
    marginRight: scale(12),
  },
  avatar: {
    width: scale(41),
    height: scale(41),
    borderRadius: scale(21),
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.mediumGray,
    alignItems: "center",
    justifyContent: "center",
  },
  userDetails: {
    flex: 1,
  },
  userEmail: {
    marginTop: verticalScale(2),
  },
  searchContainer: {
    paddingHorizontal: scale(27),
    paddingVertical: verticalScale(16),
    backgroundColor: COLORS.background,
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "rgba(136, 142, 146, 0.1)",
    borderWidth: 1,
    borderColor: COLORS.primaryLight,
    borderRadius: scale(25),
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(12),
  },
  searchPlaceholder: {
    flex: 1,
  },
  listContainer: {
    paddingHorizontal: scale(20),
    paddingTop: verticalScale(0),
    backgroundColor: COLORS.background,
  },
  itemContainer: {
    backgroundColor: COLORS.background,
  },
  sectionItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    paddingVertical: verticalScale(12),
    backgroundColor: COLORS.background,
  },
  sectionContent: {
    flex: 1,
  },
  sectionTitle: {
    marginBottom: verticalScale(0),
  },
  subItemsContainer: {
    marginTop: verticalScale(14),
    gap: verticalScale(14),
  },
  subItemText: {
    color: "rgba(0, 0, 0, 0.5)",
    lineHeight: 16,
  },
  subItemPressable: {
    paddingVertical: verticalScale(2),
  },
  bottomBorder: {
    height: 1,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
    marginVertical: verticalScale(0),
  },
  logoutSection: {
    paddingHorizontal: scale(16),
    paddingTop: verticalScale(40),
    alignItems: "center",
    backgroundColor: COLORS.background,
  },
  logoutButton: {
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    borderColor: "rgba(239, 68, 68, 0.2)",
    minWidth: scale(120),
  },
  logoutButtonText: {
    color: COLORS.error,
  },
});
