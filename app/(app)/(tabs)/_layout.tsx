import { ICONS } from "@/constants/icons";
import { COLORS } from "@/constants/theme";
import { TabIndicator } from "@/components/ui/tab-indicator";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Image } from "expo-image";
import { Tabs } from "expo-router";
import { StyleSheet, TouchableOpacity, View } from "react-native";

const ICON_SIZE = scale(22);

export default function TabsLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: COLORS.primary,
        tabBarInactiveTintColor: COLORS.mediumGray,
        headerShown: false,
        tabBarLabelStyle: {
          display: "none",
        },
      }}
      tabBar={(props) => <CustomTabBar {...props} iconSize={ICON_SIZE} />}
    >
      <Tabs.Screen
        name="(home)"
        options={{
          title: "Home",
        }}
      />
      <Tabs.Screen
        name="my-documents"
        options={{
          title: "Documents",
        }}
      />

      <Tabs.Screen
        name="profile"
        options={{
          title: "Profile",
        }}
      />
    </Tabs>
  );
}

function CustomTabBar({ state, descriptors, navigation, iconSize }: any) {
  return (
    <View style={[styles.tabBarContainer]}>
      <View style={styles.tabBar}>
        {state.routes.map((route: any, index: number) => {
          const { options } = descriptors[route.key];
          const isFocused = state.index === index;

          let iconName: keyof typeof ICONS;
          if (route.name === "(home)") {
            iconName = "HOME_ICON";
          } else if (route.name === "my-documents") {
            iconName = "DOCUMENT_ICON";
          } else if (route.name === "my-companies") {
            iconName = "TASK_ICON";
          } else {
            iconName = "PROFILE_ICON";
          }

          const onPress = () => {
            const event = navigation.emit({
              type: "tabPress",
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          return (
            <TouchableOpacity
              key={route.key}
              activeOpacity={0.8}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              onPress={onPress}
              style={[styles.tabButton]}
            >
              <View style={styles.tabItemContainer}>
                <View style={styles.iconContainer}>
                  <Image
                    source={ICONS[iconName]}
                    style={styles.tabIcon}
                    contentFit="contain"
                    tintColor={isFocused ? "#1B3142" : "#89929B"}
                  />
                </View>
                <View style={{ marginTop: verticalScale(8) }}>
                  <TabIndicator isActive={isFocused} />
                </View>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  tabBarContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingTop: verticalScale(15),
    backgroundColor: "#FFFFFF",
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabBar: {
    flexDirection: "row",
    justifyContent: "center",
    gap: verticalScale(60),
    alignItems: "center",
    paddingTop: 5,
    paddingBottom: 3,
  },
  tabButton: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 3,
  },
  tabItemContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  iconContainer: {
    width: ICON_SIZE + scale(8), // Add padding around icon
    height: ICON_SIZE + scale(8),
    borderRadius: (ICON_SIZE + scale(8)) / 2, // Perfect circle
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  tabIcon: {
    width: ICON_SIZE,
    height: ICON_SIZE,
  },
});
