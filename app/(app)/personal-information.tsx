import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { useCurrentUser } from "@/services/user/user-hooks";
import { useGetDocumentsByTier } from "@/services/user/documents-by-tier-hooks";
import { RegistrationTypeOption } from "@/types/auth";
import { TieredDocument } from "@/types/document";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import { ActivityIndicator, ScrollView, StyleSheet, View } from "react-native";

interface InfoRowProps {
  label: string;
  value: string;
  showBottomBorder?: boolean;
}

const InfoRow = ({ label, value, showBottomBorder = true }: InfoRowProps) => (
  <View style={styles.infoRowContainer}>
    <View style={styles.infoRow}>
      <AppText variant="primary" size="sm" weight="medium" style={styles.label}>
        {label}
      </AppText>
      <AppText variant="primary" size="sm" style={styles.value}>
        {value}
      </AppText>
    </View>
    {showBottomBorder && <View style={styles.rowBorder} />}
  </View>
);

const SuitabilityBadge = () => (
  <View style={styles.badgeContainer}>
    <View style={styles.badge}>
      <View style={styles.badgeIndicator} />
      <AppText variant="success" size="sm" weight="medium">
        Appropriate
      </AppText>
    </View>
  </View>
);

interface VerificationDetailsProps {
  documents: TieredDocument[];
}

const VerificationDetails = ({ documents }: VerificationDetailsProps) => {
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date
        .toLocaleDateString("en-US", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        })
        .replace(/\//g, ".");
    } catch {
      return "Not available";
    }
  };

  if (!documents || documents.length === 0) {
    return (
      <View style={styles.verificationContainer}>
        <AppText variant="secondary" size="sm">
          No verification details available
        </AppText>
      </View>
    );
  }

  return (
    <View style={styles.verificationContainer}>
      <AppText
        variant="primary"
        size="sm"
        weight="semibold"
        style={styles.verificationTitle}
      >
        Verification Details
      </AppText>

      {documents.map((doc, index) => (
        <View key={index} style={styles.verificationItem}>
          <View style={styles.verificationHeader}>
            <AppText variant="primary" size="sm" weight="medium">
              {doc.idDocDefIdDocType || "Document"}
            </AppText>
            <View style={styles.statusBadge}>
              <View style={styles.statusIndicator} />
              <AppText variant="success" size="xs" weight="medium">
                VERIFIED
              </AppText>
            </View>
          </View>

          <View style={styles.verificationMeta}>
            <AppText variant="secondary" size="xs">
              Country: {doc.idDocDefCountry || "Not specified"}
            </AppText>
            <AppText variant="secondary" size="xs">
              Created: {formatDate(doc.creationDate)}
            </AppText>
            <AppText variant="secondary" size="xs">
              Updated: {formatDate(doc.modificationDate)}
            </AppText>
          </View>
        </View>
      ))}
    </View>
  );
};

export default function PersonalInformation() {
  const { data: user, isLoading, error } = useCurrentUser();
  const { data: tieredDocuments } = useGetDocumentsByTier(user?.applicant_id, {
    enabled: !!user?.applicant_id,
  });

  const [timezone, setTimezone] = useState<string>("");
  const isKYBUser = user?.registrationtype === RegistrationTypeOption.KYB;

  useEffect(() => {
    // Get user's timezone - same logic as web app
    try {
      const tz = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const offset = new Date().getTimezoneOffset();
      const hours = Math.abs(Math.floor(offset / 60));
      const minutes = Math.abs(offset % 60);
      const sign = offset < 0 ? "+" : "-";

      // Format: "America/New_York (GMT-4)"
      const formattedTz = `${tz} (GMT${sign}${hours}${
        minutes > 0 ? `:${minutes.toString().padStart(2, "0")}` : ""
      })`;
      setTimezone(formattedTz);
    } catch (error) {
      console.error("Error getting timezone:", error);
      setTimezone("Not available");
    }
  }, []);

  const handleBackPress = () => {
    router.back();
  };

  const headerTitle = isKYBUser ? "Company Profile" : "Personal Information";

  // Error state
  if (error) {
    return (
      <View style={pageStyle.container}>
        <AppHeader title={headerTitle} onBackPress={handleBackPress} />
        <View style={styles.errorContainer}>
          <AppText variant="primary" size="lg" weight="semibold">
            Oops! Something went wrong
          </AppText>
          <AppText variant="secondary" size="sm" style={styles.errorText}>
            We couldn&apos;t load your personal information. Please try again.
          </AppText>
        </View>
      </View>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <View style={pageStyle.container}>
        <AppHeader title={headerTitle} onBackPress={handleBackPress} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <AppText variant="secondary" size="sm" style={styles.loadingText}>
            Loading your information...
          </AppText>
        </View>
      </View>
    );
  }

  const isCompanyRegistration = user?.registrationtype === "company";
  const isTier3User = user?.state === "tier3" || user?.state === "kybtier3";

  // Get Tier 3 documents for verification details
  const tier3Documents =
    tieredDocuments?.data?.tiers?.find(
      (tier) => tier.state === "tier3" || tier.state === "kybtier3"
    )?.files || [];

  return (
    <View style={[pageStyle.container, { paddingBottom: 0 }]}>
      <AppHeader title={headerTitle} onBackPress={handleBackPress} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Personal Information Section */}
        <View style={styles.section}>
          {/* User Profile Card */}
          <View style={styles.profileCard}>
            <View style={styles.profileInfo}>
              <View style={styles.avatarContainer}>
                <View style={styles.avatar}>
                  <Ionicons
                    name="person-outline"
                    size={scale(24)}
                    color={COLORS.primary}
                  />
                </View>
              </View>
              <View style={styles.userDetails}>
                <AppText variant="primary" size="base" weight="semibold">
                  {isCompanyRegistration
                    ? user?.userCompany?.companyName ||
                      user?.name ||
                      "Company Name"
                    : user?.name || "User Name"}
                </AppText>
                <AppText variant="secondary" size="sm" style={styles.userEmail}>
                  {isCompanyRegistration
                    ? user?.userCompany?.email || user?.email || "Not provided"
                    : user?.email || "Not provided"}
                </AppText>
                <AppText
                  variant="secondary"
                  size="xs"
                  style={styles.signupDate}
                >
                  Signup date:{" "}
                  {user?.signup_date
                    ? new Date(user.signup_date)
                        .toLocaleDateString("en-US", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                        .replace(/\//g, ".")
                    : "Not available"}
                </AppText>
              </View>
            </View>
          </View>

          {/* Information Details */}
          <View style={styles.detailsContainer}>
            {/* Company Name - Show for company registration */}
            {isCompanyRegistration && (
              <InfoRow
                label="Company"
                value={
                  user?.userCompany?.companyName || user?.name || "Not provided"
                }
              />
            )}

            {/* Contact - Different sources for company vs individual */}
            <InfoRow
              label="Contact"
              value={
                isCompanyRegistration
                  ? user?.userCompany?.phoneNumber || "Not provided"
                  : user?.userIndividual?.phoneNumber || "Not provided"
              }
            />

            {/* Country - Different sources for company vs individual */}
            <InfoRow
              label={isCompanyRegistration ? "Country" : "Country of Residence"}
              value={
                isCompanyRegistration
                  ? user?.userCompany?.country || "Not provided"
                  : user?.userIndividual?.country || "Not provided"
              }
            />

            {/* Address - Different sources and labels for company vs individual */}
            <InfoRow
              label={isCompanyRegistration ? "Legal Address" : "Address"}
              value={
                isCompanyRegistration
                  ? user?.userCompany?.legalAddress || "Not provided"
                  : user?.userIndividual?.address || "Not provided"
              }
            />

            {/* TIN - Different sources for company vs individual */}
            <InfoRow
              label="TIN"
              value={
                isCompanyRegistration
                  ? user?.userCompany?.taxId || "Not provided"
                  : user?.userIndividual?.tin || "Not provided"
              }
            />

            {/* Tax Residency - Only for individual users */}
            {!isCompanyRegistration && (
              <InfoRow
                label="Tax Residency"
                value={
                  user?.userIndividual?.taxResidenceCountry || "Not provided"
                }
              />
            )}

            {/* Company-specific fields */}
            {isCompanyRegistration && (
              <>
                <InfoRow
                  label="Registration Number"
                  value={
                    user?.userCompany?.registrationNumber || "Not provided"
                  }
                />
                <InfoRow
                  label="Registration Location"
                  value={
                    user?.userCompany?.registrationLocation || "Not provided"
                  }
                />
                <InfoRow
                  label="Website"
                  value={user?.userCompany?.website || "Not provided"}
                />
                <InfoRow
                  label="Postal Address"
                  value={user?.userCompany?.postalAddress || "Not provided"}
                />
                <InfoRow
                  label="Incorporation Date"
                  value={
                    user?.userCompany?.incorporationDate
                      ? new Date(user.userCompany.incorporationDate)
                          .toLocaleDateString("en-US", {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric",
                          })
                          .replace(/\//g, ".")
                      : "Not provided"
                  }
                />
              </>
            )}

            {/* Individual-specific fields */}
            {!isCompanyRegistration && (
              <InfoRow
                label="Birth Date"
                value={
                  user?.userIndividual?.birthDate
                    ? new Date(user.userIndividual.birthDate)
                        .toLocaleDateString("en-US", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                        .replace(/\//g, ".")
                    : "Not provided"
                }
              />
            )}

            {/* Tier Level Display */}
            <InfoRow
              label="Tier Level"
              value={
                user?.state === "tier3" || user?.state === "kybtier3"
                  ? "Pro"
                  : user?.state === "tier2" || user?.state === "kybtier2"
                  ? "Standard"
                  : user?.state === "tier1" || user?.state === "kybtier1"
                  ? "Basic"
                  : "Basic"
              }
            />

            <InfoRow
              label="Time zone"
              value={timezone || "Loading..."}
              showBottomBorder={!isTier3User}
            />

            {/* Enhanced Tier 3 Verification - Show only for tier3 or kybtier3 users */}
            {isTier3User && (
              <>
                <View style={styles.infoRowContainer}>
                  <View style={styles.infoRow}>
                    <AppText
                      variant="primary"
                      size="sm"
                      weight="medium"
                      style={styles.label}
                    >
                      {isKYBUser
                        ? "Company Profile Questionnaire"
                        : "Suitability test"}
                    </AppText>
                    <SuitabilityBadge />
                  </View>
                </View>

                {/* Detailed Verification Information */}
                <VerificationDetails documents={tier3Documents} />
              </>
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: verticalScale(20),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: verticalScale(16),
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: scale(32),
    backgroundColor: COLORS.background,
  },
  errorText: {
    textAlign: "center",
    marginTop: verticalScale(8),
  },
  section: {
    backgroundColor: COLORS.background,
    marginHorizontal: scale(5),
    marginTop: verticalScale(5),
    borderRadius: scale(12),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    padding: scale(20),
  },
  profileCard: {
    paddingBottom: verticalScale(10),
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatarContainer: {
    marginRight: scale(16),
  },
  avatar: {
    width: scale(48),
    height: scale(48),
    borderRadius: scale(24),
    backgroundColor: COLORS.background,
    borderWidth: 2,
    borderColor: COLORS.primary,
    alignItems: "center",
    justifyContent: "center",
  },
  userDetails: {
    flex: 1,
  },
  userEmail: {
    marginTop: verticalScale(4),
  },
  signupDate: {
    marginTop: verticalScale(2),
  },
  detailsContainer: {
    gap: verticalScale(0),
  },
  infoRowContainer: {
    backgroundColor: COLORS.background,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: verticalScale(16),
    minHeight: verticalScale(48),
  },
  label: {
    minWidth: scale(100),
    marginRight: scale(16),
  },
  value: {
    flex: 1,
    textAlign: "right",
  },
  rowBorder: {
    height: 1,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  badgeContainer: {
    alignItems: "flex-end",
  },
  badge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.success + "1F", // 12% opacity
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(4),
    borderRadius: scale(16),
  },
  badgeIndicator: {
    width: scale(8),
    height: scale(8),
    borderRadius: scale(4),
    backgroundColor: COLORS.success,
    marginRight: scale(8),
  },
  verificationContainer: {
    marginTop: verticalScale(16),
    paddingTop: verticalScale(16),
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  verificationTitle: {
    marginBottom: verticalScale(12),
  },
  verificationItem: {
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    borderRadius: scale(8),
    padding: scale(12),
    marginBottom: verticalScale(8),
  },
  verificationHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: verticalScale(8),
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.success + "1F", // 12% opacity
    paddingHorizontal: scale(8),
    paddingVertical: verticalScale(2),
    borderRadius: scale(12),
  },
  statusIndicator: {
    width: scale(6),
    height: scale(6),
    borderRadius: scale(3),
    backgroundColor: COLORS.success,
    marginRight: scale(4),
  },
  verificationMeta: {
    gap: verticalScale(2),
  },
});
