import { COLORS } from "@/constants/theme";
import { useSession } from "@/providers/auth/auth-provider";
import { Redirect, Stack } from "expo-router";
import { ActivityIndicator, View } from "react-native";

export default function AppLayout() {
  const { token, isLoading } = useSession();

  // You can keep the splash screen open, or render a loading screen like we do here.
  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  // Only require authentication within the (app) group's layout as users
  // need to be able to access the (auth) group and sign in again.
  if (!token) {
    // On web, static rendering will stop here as the user is not authenticated
    // in the headless Node process that the pages are rendered in.
    return <Redirect href="/sign-in" />;
  }

  // This layout can be deferred because it's not the root layout.
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        gestureDirection: "horizontal",
        animation: "slide_from_right",
        presentation: "card",
      }}
    >
      <Stack.Screen
        name="welcome"
        options={{
          gestureEnabled: true,
          gestureDirection: "horizontal",
        }}
      />
      <Stack.Screen
        name="(tabs)"
        options={{
          gestureEnabled: false, // Disable gesture for tabs
        }}
      />
      <Stack.Screen
        name="company/[id]"
        options={{
          gestureEnabled: true,
          gestureDirection: "horizontal",
          animation: "slide_from_right",
          presentation: "card",
        }}
      />
    </Stack>
  );
}
