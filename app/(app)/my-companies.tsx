import { ConsentCompanyItem } from "@/components/companies/consent-company-item";
import { FormSearch } from "@/components/form/form-search";
import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import UserHeader from "@/components/layout/user-header";
import {
  RevokeConsentModal,
  useRevokeConsentStore,
} from "@/components/modals/revoke-consent-modal";
import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import EmptyState from "@/components/ui/empty-state";

import { COLORS } from "@/constants/theme";
import { useUserCompaniesConsents } from "@/services/company/company-hooks";
import { useCurrentUser } from "@/services/user/user-hooks";
import { useGetDocumentsByTier } from "@/services/user/documents-by-tier-hooks";
import { Company } from "@/types/company";
import { Document } from "@/types/document";
import { scale, verticalScale } from "@/utils/styling-scale";
import { FlashList } from "@shopify/flash-list";
import { useRouter } from "expo-router";
import { useMemo, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  RefreshControl,
  StyleSheet,
  View,
} from "react-native";

// Helper function to get documents for a company based on tier (matching web implementation)
const getDocumentsForCompany = (
  companyTier: number,
  tieredDocuments: any,
  userProfile: any
) => {
  if (!tieredDocuments?.data?.tiers) return [];
  const tierPrefix =
    userProfile?.registrationtype === "company" ? "kybtier" : "tier";

  // Extract tier number from enum value (e.g., "tier1" -> 1, "kybtier2" -> 2)
  const tierKey = `${tierPrefix}${companyTier}`;
  const tierData = tieredDocuments.data.tiers.find(
    (tier: any) => tier.state === tierKey
  );

  return tierData?.files || [];
};

interface ConsentCompanyWithDocuments extends Company {
  isExpanded?: boolean;
  documentsForTier?: Document[];
}

export default function MyCompanies() {
  const router = useRouter();
  const {
    data: user,
    isLoading: userLoading,
    error: userError,
  } = useCurrentUser();
  const {
    data: companies,
    isLoading,
    refetch: refetchCompanies,
    isFetching,
    error: companiesError,
  } = useUserCompaniesConsents(user?.user_id ?? "");

  // Use the same tiered documents approach as consent history
  const {
    data: tieredDocuments,
    refetch: refetchDocuments,
    isLoading: isLoadingDocuments,
  } = useGetDocumentsByTier(user?.applicant_id || "");

  // FlashList ref for auto-scroll
  const flashListRef = useRef<FlashList<ConsentCompanyWithDocuments>>(null);

  const { setIsVisible } = useRevokeConsentStore();

  const [searchQuery, setSearchQuery] = useState("");
  const [expandedCompanies, setExpandedCompanies] = useState<Set<number>>(
    new Set()
  );

  // Revoke consent modal state
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);

  const handleRefresh = () => {
    try {
      refetchCompanies();
      refetchDocuments();
    } catch (error) {
      console.error("Error refreshing companies:", error);
      Alert.alert("Error", "Failed to refresh companies");
    }
  };

  const filteredCompanies = useMemo(() => {
    try {
      if (!companies || !Array.isArray(companies) || !tieredDocuments)
        return [];

      return companies
        .filter((company) =>
          company?.name?.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .map((company: Company) => {
          const tier =
            company.required_individual_tier ||
            company.required_corporate_tier ||
            1;
          // Use the same logic as consent history to get documents by tier
          const documentsForTier = getDocumentsForCompany(
            tier,
            tieredDocuments,
            user
          );

          return {
            ...company,
            isExpanded: expandedCompanies.has(company.company_id),
            documentsForTier,
          } as ConsentCompanyWithDocuments;
        });
    } catch (error) {
      console.error("Error filtering companies:", error);
      return [];
    }
  }, [companies, tieredDocuments, searchQuery, expandedCompanies, user]);

  const handleDetails = (companyId: number) => {
    try {
      router.push(`/(app)/company/${companyId}?from=my-companies`);
    } catch (error) {
      console.error("Navigation error:", error);
      Alert.alert("Error", "Failed to navigate to company details");
    }
  };

  // Handle expand/collapse company details with auto-scroll
  const handleToggleExpand = (companyId: number) => {
    const newExpanded = new Set(expandedCompanies);
    const wasExpanded = newExpanded.has(companyId);

    if (wasExpanded) {
      newExpanded.delete(companyId);
    } else {
      newExpanded.add(companyId);

      // Auto-scroll to the expanded item after a short delay
      setTimeout(() => {
        const itemIndex = filteredCompanies.findIndex(
          (company) => company.company_id === companyId
        );
        if (itemIndex !== -1 && flashListRef.current) {
          flashListRef.current.scrollToIndex({
            index: itemIndex,
            animated: true,
            viewPosition: 0.3, // Position item at 30% from top of visible area
          });
        }
      }, 100);
    }

    setExpandedCompanies(newExpanded);
  };

  // Handle revoke consent
  const handleRevokePress = (company: Company) => {
    setSelectedCompany(company);
    setIsVisible(true);
  };

  const handleNavigateToMarketplace = () => {
    try {
      router.push("/(app)/(tabs)/(home)");
    } catch (error) {
      console.error("Navigation error:", error);
      Alert.alert("Error", "Failed to navigate to marketplace");
    }
  };

  const refreshing =
    isFetching || isLoading || userLoading || isLoadingDocuments;

  // Error state
  if (userError || companiesError) {
    return (
      <View style={pageStyle.container}>
        <AppHeader title="My Companies" grayColor hideBackButton />
        <View style={styles.errorContainer}>
          <AppText variant="primary" size="lg" weight="semibold">
            Oops! Something went wrong
          </AppText>
          <AppText variant="secondary" size="sm" style={styles.errorText}>
            We couldn&apos;t load your companies. Please try again.
          </AppText>
          <AppButton
            text="Try Again"
            variant="default"
            size="sm"
            onPress={handleRefresh}
            buttonStyle={styles.retryButton}
          />
        </View>
      </View>
    );
  }

  // Loading state
  if (refreshing && (!filteredCompanies || filteredCompanies.length === 0)) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <AppText variant="secondary" size="sm" style={styles.loadingText}>
          Loading your companies...
        </AppText>
      </View>
    );
  }

  return (
    <View style={pageStyle.container}>
      <FlashList
        contentContainerStyle={pageStyle.listContainer}
        data={filteredCompanies || []}
        ListHeaderComponent={
          <View style={styles.headerContainer}>
            <AppHeader
              title="My Companies"
              grayColor
              onBackPress={() => router.push("/profile")}
            />
            <UserHeader />

            <FormSearch
              placeholder="Search your companies"
              value={searchQuery}
              onChangeText={setSearchQuery}
              onClear={() => setSearchQuery("")}
              containerStyle={styles.searchContainer}
            />

            {/* Companies Count */}
            {filteredCompanies && filteredCompanies.length > 0 && (
              <View style={styles.countContainer}>
                <AppText variant="secondary" size="sm" weight="medium">
                  {filteredCompanies.length} Connected{" "}
                  {filteredCompanies.length === 1 ? "Company" : "Companies"}
                </AppText>
              </View>
            )}
          </View>
        }
        renderItem={({ item }) => {
          if (!item) return null;
          return (
            <ConsentCompanyItem
              company={item}
              documentCount={item.documentsForTier?.length || 0}
              isExpanded={item.isExpanded}
              documentsForTier={item.documentsForTier || []}
              onRevokePress={() => handleRevokePress(item)}
              onDetailsPress={() => handleToggleExpand(item.company_id)}
              onPress={() => handleDetails(item.company_id)}
            />
          );
        }}
        estimatedItemSize={100}
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <EmptyState text="No companies connected yet" />
            <AppButton
              text="Browse Marketplace"
              variant="default"
              size="md"
              onPress={handleNavigateToMarketplace}
              buttonStyle={styles.browseButton}
            />
          </View>
        }
      />
      {/* Revoke Consent Modal */}
      <RevokeConsentModal
        companyId={selectedCompany?.company_id ?? 0}
        companyName={selectedCompany?.name ?? ""}
        showButton={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: verticalScale(16),
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: scale(32),
    backgroundColor: COLORS.background,
  },
  errorText: {
    textAlign: "center",
    marginTop: verticalScale(8),
    marginBottom: verticalScale(24),
  },
  retryButton: {
    paddingHorizontal: scale(32),
  },
  headerContainer: {
    paddingBottom: verticalScale(16),
    paddingHorizontal: scale(10),
  },
  searchContainer: {
    marginTop: verticalScale(16),
    marginHorizontal: scale(16),
  },
  countContainer: {
    paddingHorizontal: scale(16),
    paddingTop: verticalScale(16),
    paddingBottom: verticalScale(8),
  },
  emptyContainer: {
    paddingTop: verticalScale(50),
    alignItems: "center",
  },
  browseButton: {
    marginTop: verticalScale(20),
    paddingHorizontal: scale(32),
  },
});
