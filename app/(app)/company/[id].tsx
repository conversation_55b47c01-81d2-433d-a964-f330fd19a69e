import TierBadge from "@/components/companies/tier-badge";
import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import { OnboardingConfirmModal } from "@/components/modals/onboarding-confirm-modal";
import { RevokeConsentModal } from "@/components/modals/revoke-consent-modal";
import { UpgradeSumSubModal } from "@/components/modals/upgrade-sumsub-popup-modal";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import {
  useCompanyByID,
  useUserCompaniesConsents,
} from "@/services/company/company-hooks";
import { getCompanyTier } from "@/services/sumsub/sumsub-service";
import { useCurrentUser } from "@/services/user/user-hooks";
import { RegistrationTypeOption } from "@/types/auth";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Image } from "expo-image";
import { useLocalSearchParams, useRouter } from "expo-router";
import React from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";

export default function CompanyDetails() {
  const { id, from } = useLocalSearchParams();
  const router = useRouter();
  const { data: company, isLoading } = useCompanyByID(id as string);
  const { data: user, isLoading: isUserLoading } = useCurrentUser();

  const {
    data: userCompaniesConsents,
    isLoading: isUserCompaniesConsentsLoading,
  } = useUserCompaniesConsents(user?.user_id ?? "");

  // Handle back navigation based on where user came from
  const handleBackPress = () => {
    if (from === "my-companies") {
      router.push("/(app)/my-companies");
    } else {
      // Default to marketplace (home)
      router.push("/(app)/(tabs)/(home)");
    }
  };

  if (!company || !user) return null;

  const { companyTierNumber, companyTierSumsubLevel } = getCompanyTier({
    company,
    userRegistrationType: user?.registrationtype as RegistrationTypeOption,
  });

  let needToUpgrade = user.state !== companyTierSumsubLevel;

  if (companyTierNumber) {
    if (user?.tierAsNumber === 0) {
      needToUpgrade = true;
    } else {
      needToUpgrade = (user?.tierAsNumber ?? 1) < companyTierNumber;
    }
  }

  const hasGivenConsent = userCompaniesConsents?.some(
    (consent) => consent.company_id === company?.company_id
  );

  if (isLoading || isUserLoading || isUserCompaniesConsentsLoading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  return (
    <View style={pageStyle.container}>
      <View style={styles.container}>
        <ScrollView
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
        >
          <AppHeader title="Company details" onBackPress={handleBackPress} />
          <View style={styles.header}>
            <View style={styles.companyInfo}>
              <View style={styles.coverPhotoContainer}>
                <Image
                  source={{ uri: company?.s3ImageUrl || company?.s3LogoUrl }}
                  style={styles.coverPhoto}
                  contentFit="fill"
                  transition={200}
                />
              </View>
            </View>
            {/* title and badge */}
            <View style={styles.titleContainer}>
              <AppText variant="primary" size="2xl" weight="bold">
                {company?.name}
              </AppText>
              <TierBadge tier={companyTierNumber} />
            </View>
            {company?.description && (
              <Text style={styles.description}>{company.description}</Text>
            )}
          </View>
        </ScrollView>

        <View style={styles.actions}>
          {needToUpgrade && (
            <UpgradeSumSubModal
              companyName={company?.name ?? ""}
              companyLogo={company?.s3ImageUrl || company.s3LogoUrl}
              companyTierNumber={companyTierNumber}
              companyTierSumsubLevel={companyTierSumsubLevel}
            />
          )}

          {!needToUpgrade && !hasGivenConsent && company && (
            <OnboardingConfirmModal
              companyName={company.name}
              companyTierNumber={companyTierNumber}
              companyId={company.company_id}
            />
          )}
          {!needToUpgrade && hasGivenConsent && company && (
            <RevokeConsentModal
              companyName={company.name}
              companyId={company.company_id}
            />
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingHorizontal: scale(5),
  },
  header: {
    paddingTop: verticalScale(10),
  },
  companyInfo: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    position: "relative",
    marginBottom: verticalScale(12),
  },
  coverPhotoContainer: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: scale(5),
    overflow: "hidden",
  },
  coverPhoto: {
    width: "100%",
    height: "100%",
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: verticalScale(4),
  },

  description: {
    fontSize: scale(14),
    color: "#4B5563",
    lineHeight: scale(20),
  },

  actions: {
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(16),
    backgroundColor: COLORS.background,
    gap: scale(16),
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
  },

  buttonText: {
    fontSize: scale(14),
    fontWeight: "500",
    color: "#1B3142",
  },
  revokeButtonText: {
    fontSize: scale(14),
    fontWeight: "500",
    color: "#F44336",
  },

  statusText: {
    fontSize: scale(14),
    fontWeight: "500",
    color: "#059669",
  },

  upgradeButton: {
    width: "100%",
  },
  revokeButton: {
    width: "100%",
  },
});
