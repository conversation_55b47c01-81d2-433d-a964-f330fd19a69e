import AppHeader from "@/components/layout/app-header";
import { pageStyle } from "@/components/layout/page-style";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { useCurrentUser } from "@/services/user/user-hooks";
import { scale, verticalScale } from "@/utils/styling-scale";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { ChangePasswordModal } from "@/components/modals/change-password-modal";

interface SignInMethodCardProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  value: string;
  onPress?: () => void;
  disabled?: boolean;
}

const SignInMethodCard = ({
  icon,
  title,
  value,
  onPress,
  disabled = false,
}: SignInMethodCardProps) => (
  <TouchableOpacity
    style={[styles.methodCard, disabled && styles.methodCardDisabled]}
    onPress={onPress}
    disabled={disabled}
    activeOpacity={0.7}
  >
    <View style={styles.methodIconContainer}>
      <Ionicons name={icon} size={scale(20)} color={COLORS.primary} />
    </View>
    <View style={styles.methodContent}>
      <AppText
        variant="secondary"
        size="xs"
        weight="medium"
        style={styles.methodLabel}
      >
        {title}
      </AppText>
      <AppText
        variant="primary"
        size="sm"
        weight="semibold"
        style={styles.methodValue}
      >
        {value}
      </AppText>
    </View>
    {!disabled && (
      <View style={styles.methodArrow}>
        <Ionicons
          name="chevron-forward"
          size={scale(20)}
          color={COLORS.mediumGray}
        />
      </View>
    )}
  </TouchableOpacity>
);

export default function SignInMethods() {
  const { data: user, isLoading, error } = useCurrentUser();
  const [isChangePasswordModalVisible, setIsChangePasswordModalVisible] =
    useState(false);

  const handleBackPress = () => {
    router.back();
  };

  const handleChangePassword = () => {
    setIsChangePasswordModalVisible(true);
  };

  const handleChangeEmail = () => {
    // TODO: Implement change email functionality
    console.log("Change email functionality not yet implemented");
  };

  // Error state
  if (error) {
    return (
      <View style={pageStyle.container}>
        <AppHeader title="Sign-in Methods" onBackPress={handleBackPress} />
        <View style={styles.errorContainer}>
          <Ionicons
            name="alert-circle-outline"
            size={scale(48)}
            color={COLORS.error}
          />
          <AppText variant="destructive" size="base" style={styles.errorText}>
            Failed to load user information
          </AppText>
        </View>
      </View>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <View style={pageStyle.container}>
        <AppHeader title="Sign-in Methods" onBackPress={handleBackPress} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <AppText variant="secondary" size="sm" style={styles.loadingText}>
            Loading sign-in methods...
          </AppText>
        </View>
      </View>
    );
  }

  return (
    <View style={pageStyle.container}>
      <AppHeader title="Sign-in Methods" onBackPress={handleBackPress} />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Sign-in Methods Section */}
        <View style={styles.section}>
          <View style={styles.methodsContainer}>
            {/* Email Card */}
            <SignInMethodCard
              icon="mail-outline"
              title="Email"
              value={user?.email || "Not provided"}
              onPress={handleChangeEmail}
              disabled={true} // Disable for now until change email is implemented
            />

            {/* Password Card */}
            <SignInMethodCard
              icon="key-outline"
              title="Password"
              value="••••••••••"
              onPress={handleChangePassword}
            />
          </View>

          {/* Security Note */}
          <View style={styles.securityNote}>
            <Ionicons
              name="shield-checkmark-outline"
              size={scale(20)}
              color={COLORS.primary}
              style={styles.securityIcon}
            />
            <View style={styles.securityTextContainer}>
              <AppText
                variant="primary"
                size="sm"
                weight="medium"
                style={styles.securityTitle}
              >
                Keep your account secure
              </AppText>
              <AppText
                variant="secondary"
                size="xs"
                style={styles.securityDescription}
              >
                Use a strong password with at least 8 characters, including
                uppercase, lowercase, numbers, and symbols.
              </AppText>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Change Password Modal */}
      <ChangePasswordModal
        isVisible={isChangePasswordModalVisible}
        onClose={() => setIsChangePasswordModalVisible(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: verticalScale(100),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: verticalScale(16),
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: scale(32),
    backgroundColor: COLORS.background,
  },
  errorText: {
    textAlign: "center",
    marginTop: verticalScale(16),
  },
  section: {
    marginHorizontal: scale(5),
    marginTop: verticalScale(16),
    shadowColor: "#000",

    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    marginBottom: verticalScale(8),
  },
  sectionDescription: {
    marginBottom: verticalScale(24),
  },
  methodsContainer: {
    gap: verticalScale(12),
    marginBottom: verticalScale(24),
  },
  methodCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.background,
    borderRadius: scale(12),
    borderWidth: 1,
    borderColor: COLORS.primaryLight,
    padding: scale(16),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  methodCardDisabled: {
    opacity: 0.6,
  },
  methodIconContainer: {
    width: scale(40),
    height: scale(40),
    borderRadius: scale(20),
    backgroundColor: "#C8E2CE",
    alignItems: "center",
    justifyContent: "center",
    marginRight: scale(16),
  },
  methodContent: {
    flex: 1,
  },
  methodLabel: {
    marginBottom: verticalScale(2),
  },
  methodValue: {
    color: COLORS.primary,
  },
  methodArrow: {
    marginLeft: scale(8),
  },
  securityNote: {
    flexDirection: "row",
    backgroundColor: "#F8FAFC",
    borderRadius: scale(8),
    padding: scale(16),
    borderLeftWidth: scale(4),
    borderLeftColor: COLORS.primary,
  },
  securityIcon: {
    marginRight: scale(12),
    marginTop: verticalScale(2),
  },
  securityTextContainer: {
    flex: 1,
  },
  securityTitle: {
    marginBottom: verticalScale(4),
  },
  securityDescription: {
    lineHeight: scale(16),
  },
});
