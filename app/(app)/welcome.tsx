import AppHeader from "@/components/layout/app-header";
import Logo from "@/components/logo";
import { AppButton } from "@/components/ui/app-button";
import { AppText } from "@/components/ui/app-text";
import { COLORS } from "@/constants/theme";
import { useSession } from "@/providers/auth/auth-provider";
import { QUERY_KEYS } from "@/services/query-keys";
import { openKYBInBrowser } from "@/services/sumsub/kyb-redirect-service";
import { launchSNSMobileSDK } from "@/services/sumsub/launchSNSMobileSDK";
import { getSumsubAccessToken } from "@/services/sumsub/sumsub-service";
import { useCurrentUser } from "@/services/user/user-hooks";
import { RegistrationTypeOption, SUMSUB_LEVEL } from "@/types/auth";
import { scale, verticalScale } from "@/utils/styling-scale";
import { useQueryClient } from "@tanstack/react-query";
import { router } from "expo-router";
import { useState } from "react";
import { ActivityIndicator, StyleSheet, View } from "react-native";

type WelcomeStep = {
  step: number;
  title: string;
  description: string;
  buttonText: string;
  showSkip?: boolean;
};

const KYC_STEPS: WelcomeStep[] = [
  {
    step: 1,
    title: "Welcome to",
    description:
      "We know - KYC checks are long, tiresome, and repeatedly unpleasant.\n\nWith TrustNexus, this has changed. We've made the process simple, easy to understand, one that doesn't take hours of the night or two boring security checks!",
    buttonText: "NEXT",
  },
  {
    step: 2,
    title: "Welcome to",
    description:
      "We enable you to seamlessly jump into existing platforms built around us. Businesses, governments, agencies, and all other institutions don't need to reinvent the wheel by verifying you repeatedly. They can rely on the highest tier you have already secured.",
    buttonText: "NEXT",
  },
  {
    step: 3,
    title: "Welcome to",
    description:
      "From sign-up to registration of your profile with us to your specific verification in whichever way suits you the best, we're here to help organizations identify and verify people like you.",
    buttonText: "START KYC",
    showSkip: true,
  },
];

const KYB_STEPS: WelcomeStep[] = [
  {
    step: 1,
    title: "Welcome to",
    description:
      "We know - KYB checks are long, tiresome, and repeatedly unpleasant.\n\nWith TrustNexus, this has changed. We've made the process simple, easy to understand, one that doesn't take hours of the night or two boring security checks!",
    buttonText: "NEXT",
  },
  {
    step: 2,
    title: "Welcome to",
    description:
      "We enable you to seamlessly jump into existing platforms built around us. Businesses, governments, agencies, and all other institutions don't need to reinvent the wheel by verifying you repeatedly. They can rely on the highest tier you have already secured.",
    buttonText: "NEXT",
  },
  {
    step: 3,
    title: "Welcome to",
    description:
      "From sign-up to registration of your profile with us to your specific verification in whichever way suits you the best, we're here to help organizations identify and verify businesses like yours.",
    buttonText: "START KYB",
    showSkip: true,
  },
];

export default function Welcome() {
  const { data: currentUser, isPending: isUserPending } = useCurrentUser();
  const { clearJustSignedUp } = useSession();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const queryClient = useQueryClient();

  const isKYB = currentUser?.registrationtype === RegistrationTypeOption.KYB;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const userTier = currentUser?.state;

  const steps =
    currentUser?.registrationtype === RegistrationTypeOption.KYB
      ? KYB_STEPS
      : KYC_STEPS;

  const totalSteps = steps.length;
  const currentStep = steps[currentStepIndex];

  // Handle KYC completion and navigation
  const handleKYCStatusChange = (event: any) => {
    console.log("🔄 KYC Status Change:", event);

    // Invalidate user queries to refresh user data
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.CURRENT_USER],
    });

    // Check if KYC verification is completed or pending
    if (event.newStatus === "Pending" || event.newStatus === "Approved") {
      console.log("✅ KYC Tier 1 completed, navigating to marketplace");

      // Clear the justSignedUp state so user won't return to welcome screen
      clearJustSignedUp();

      // Navigate to marketplace after successful KYC completion
      setTimeout(() => {
        router.replace("/(app)/(tabs)/(home)");
      }, 1000); // Small delay to ensure user data is refreshed
    } else if (event.newStatus === "ActionCompleted") {
      console.log("🎯 KYC Action completed, checking final status");

      // Clear the justSignedUp state so user won't return to welcome screen
      clearJustSignedUp();

      // For ActionCompleted, also invalidate queries and navigate
      setTimeout(() => {
        router.replace("/(app)/(tabs)/(home)");
      }, 1000);
    }
  };

  const handleNext = async () => {
    if (currentStepIndex < totalSteps - 1) {
      // Move to the next step if we're not at the last step
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      // On the last step, launch KYC verification
      try {
        if (isKYB) {
          await openKYBInBrowser();
          return;
        }
        console.log("🚀 Starting KYC verification process...");
        const tokenData = await getSumsubAccessToken({
          tierEnum: SUMSUB_LEVEL.TIER1,
        });

        await launchSNSMobileSDK({
          sumSubData: {
            token: tokenData.token,
            userEmail: currentUser?.email ?? "",
          },
          statusChangeHandler: handleKYCStatusChange,
          tier: SUMSUB_LEVEL.TIER1,
        });
      } catch (error) {
        console.error("❌ Error launching KYC verification:", error);
        // On error, still navigate to marketplace as fallback
        router.replace("/(app)/(tabs)/(home)");
      }
    }
  };
  const handleBack = () => {
    if (currentStepIndex > 0) {
      // Go back to previous step if not on first step
      setCurrentStepIndex(currentStepIndex - 1);
    } else {
      // On first step, go back to previous tab
      router.back();
    }
  };

  const handleSkip = () => {
    // Clear the justSignedUp state when user skips KYC
    clearJustSignedUp();
    router.push("/(app)/(tabs)/(home)");
  };

  if (isUserPending) {
    return <ActivityIndicator size="large" color={COLORS.primary} />;
  }

  return (
    <View style={styles.container}>
      <AppHeader
        title={`Step ${currentStep.step}/${totalSteps}`}
        onBackPress={handleBack}
      />

      <View style={styles.stepIndicator}>
        {steps.map((_, index) => (
          <View
            key={index}
            style={[
              styles.stepLine,
              index <= currentStepIndex
                ? styles.activeStepLine
                : styles.inactiveStepLine,
            ]}
          />
        ))}
      </View>

      <View style={styles.content}>
        <AppText variant="primary" weight="bold" size="3xl">
          {currentStep.title}
        </AppText>
        <Logo withTitle />

        <AppText variant="primary" style={styles.description}>
          {currentStep.description}
        </AppText>

        <View style={styles.buttonContainer}>
          <AppButton
            text={currentStep.buttonText}
            variant="default"
            onPress={handleNext}
          />

          {currentStep.showSkip && (
            <AppButton text="SKIP" onPress={handleSkip} variant="outline" />
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  stepIndicator: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: verticalScale(5),
    marginBottom: verticalScale(15),
    paddingHorizontal: scale(40),
  },
  stepLine: {
    height: verticalScale(2),
    flex: 1,
    marginHorizontal: scale(4),
  },
  activeStepLine: {
    backgroundColor: COLORS.primary,
  },
  inactiveStepLine: {
    backgroundColor: COLORS.mediumGray + "40", // Adding 40% opacity
  },
  content: {
    flex: 1,
    paddingHorizontal: scale(30),
    alignItems: "center",
    paddingTop: verticalScale(20),
    justifyContent: "space-between",
    paddingBottom: verticalScale(40),
  },
  titleContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: verticalScale(30),
  },
  title: {
    marginRight: scale(10),
  },
  logo: {
    width: scale(40),
    height: scale(40),
  },
  description: {
    textAlign: "center",
    marginBottom: verticalScale(40),
    lineHeight: verticalScale(24),
  },
  buttonContainer: {
    alignItems: "center",
    marginTop: verticalScale(20),
    gap: verticalScale(12),
  },
  skipButton: {
    marginTop: verticalScale(16),
    minHeight: verticalScale(48),
    borderRadius: scale(30),
  },
  skipButtonText: {
    fontSize: scale(16),
    color: COLORS.primary,
  },
  loginContainer: {
    alignItems: "center",
    marginTop: verticalScale(20),
  },
});
