import Logo from "@/components/logo";
import { AppButton } from "@/components/ui/app-button";
import { Stack, router } from "expo-router";
import React from "react";
import { StyleSheet, View } from "react-native";

export default function NotFoundScreen() {
  const handleGoHome = () => {
    router.replace("/(app)/(tabs)/(home)");
  };

  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />
      <View style={styles.container}>
        <Logo />
        <AppButton
          text="Go to Home"
          onPress={handleGoHome}
          buttonStyle={styles.button}
        />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  button: {
    marginTop: 40,
    minWidth: 200,
  },
});
