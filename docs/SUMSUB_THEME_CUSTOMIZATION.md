# Sumsub SDK Theme Customization

This document explains how the Sumsub Mobile SDK has been customized to match the TrustNexus app's visual design and branding.

## 🎨 Overview

The Sumsub SDK theme customization ensures visual consistency between the TrustNexus app and the identity verification flows. The theme applies your brand colors, typography, spacing, and component styling throughout the entire Sumsub verification process.

## 📁 Files Structure

```
services/sumsub/
├── sumsub-theme.ts          # Main theme configuration
├── launchSNSMobileSDK.ts    # Updated SDK integration with theme
└── sumsub-service.ts        # Token management (unchanged)

www/
├── assets.js                # Asset bundling configuration
└── fonts/
    └── SpaceMono-Regular.ttf # Font asset for SDK
```

## 🎯 Theme Configuration

### Colors Applied

The theme maps TrustNexus colors to Sumsub SDK elements:

| TrustNexus Color | Hex Code  | Sumsub Usage                             |
| ---------------- | --------- | ---------------------------------------- |
| Primary          | `#1C3F3C` | Primary buttons, navigation, alerts      |
| Primary Light    | `#C8E2CE` | Pressed states, hover effects            |
| Background       | `#FFFFFF` | Main backgrounds, button text            |
| Secondary        | `#151312` | Strong text content, camera background   |
| Gray             | `#60636B` | Neutral text content                     |
| Medium Gray      | `#888E92` | Weak text, disabled states               |
| Light Gray       | `#E3E3E3` | Field backgrounds, borders, separators   |
| Success          | `#2AA952` | Success state backgrounds (with opacity) |
| Warning          | `#FF9500` | Warning state backgrounds (with opacity) |
| Error            | `#FF0000` | Error state backgrounds (with opacity)   |

### Typography

- **Font Family**: SpaceMono (consistent with app)
- **Responsive Sizing**: Uses app's `scale()` function
- **Font Sizes**:
  - Headline1: 24pt
  - Headline2: 20pt
  - Subtitle1: 18pt
  - Subtitle2: 16pt
  - Body: 14pt
  - Caption: 12pt

### Layout & Spacing

- **Button Height**: 48pt (matches app forms)
- **Corner Radius**: 8pt (consistent with app cards)
- **Horizontal Margins**: 16pt (matches app layout)
- **Border Width**: 1pt (consistent styling)

## 🔧 Implementation Details

### Theme Creation

The theme is created in `services/sumsub/sumsub-theme.ts`:

```typescript
export const createSumsubTheme = () => {
  return {
    universal: {
      fonts: {
        /* Font configuration */
      },
      colors: {
        /* Color mapping */
      },
      metrics: {
        /* Layout specifications */
      },
    },
    ios: {
      /* iOS-specific settings */
    },
    android: {
      /* Android-specific settings */
    },
  };
};
```

### SDK Integration

The theme is applied in `launchSNSMobileSDK.ts`:

```typescript
const theme = createSumsubTheme();
const snsMobileSDK = SNSMobileSDK.init(token, handler)
  .withTheme(theme) // Apply custom theme
  .build();
```

### Asset Management

Fonts are bundled through:

1. Physical file: `www/fonts/SpaceMono-Regular.ttf`
2. Asset registration: `www/assets.js`
3. App import: `app/_layout.tsx`

## 🎨 Visual Consistency

### Primary Buttons

- Background: TrustNexus primary green (`#1C3F3C`)
- Text: White (`#FFFFFF`)
- Pressed: Light green (`#C8E2CE`)
- Height: 88pt with 28pt corner radius (fully rounded like AppButton)

### Secondary Buttons

- Background: White with primary green border
- Text: Primary green
- Pressed: Light green background
- Same dimensions as primary buttons

### Form Fields

- Background: Light gray (`#E3E3E3`)
- Border: Light gray
- Text: Dark gray (`#151312`)
- Height: 48pt with 8pt corner radius (smaller than buttons for better UX)

### Cards & Lists

- Background: White
- Border: Light gray
- Corner radius: 8pt
- Separator: Light gray

### Camera Interface

- Background: Dark gray (`#151312`)
- Controls: White
- Overlay: Dark gray with 75% opacity

## 🧪 Testing

### Visual Verification Checklist

- [ ] Primary buttons match app styling
- [ ] Secondary buttons use correct border/text colors
- [ ] Form fields have consistent appearance
- [ ] Text uses SpaceMono font throughout
- [ ] Colors match TrustNexus brand palette
- [ ] Spacing and dimensions are consistent
- [ ] Camera interface is properly themed
- [ ] Status indicators use correct colors
- [ ] Cards and lists match app design

### Test Scenarios

1. **Document Capture Flow**

   - Verify camera interface styling
   - Check instruction text and buttons
   - Validate document frame appearance

2. **Selfie Capture Flow**

   - Test viewport styling
   - Verify instruction overlays
   - Check capture button appearance

3. **Form Inputs**

   - Test text field styling
   - Verify dropdown/picker appearance
   - Check validation message styling

4. **Status Screens**
   - Verify success/error/warning colors
   - Check icon and text styling
   - Validate button appearances

## 🔄 Updates & Maintenance

### Updating Colors

To update theme colors, modify `COLORS` in `constants/theme.ts`. The Sumsub theme will automatically use the updated values.

### Updating Typography

Font changes require:

1. Update font file in `www/fonts/`
2. Update asset registration in `www/assets.js`
3. Update font name in `sumsub-theme.ts`

### Updating Layout

Spacing and dimension changes should be made in `sumsub-theme.ts` using the app's scaling functions.

## 🐛 Troubleshooting

### Common Issues

1. **Font Not Loading**

   - Verify font file exists in `www/fonts/`
   - Check asset import in `app/_layout.tsx`
   - Ensure font name matches file metadata

2. **Colors Not Applied**

   - Verify theme is passed to `.withTheme()`
   - Check color format (hex with #)
   - Ensure opacity values are correct

3. **Layout Issues**
   - Verify scaling functions are imported
   - Check metric values are numbers
   - Ensure platform-specific settings

### Debug Mode

The SDK is configured with debug mode enabled. Check console logs for theme application messages:

```
🎨 Applying TrustNexus theme to Sumsub SDK
```

## 📚 References

- [Sumsub Android Theme API](https://docs.sumsub.com/docs/android-theme-api)
- [Sumsub Plugins Customization](https://docs.sumsub.com/docs/plugins-customization)
- [TrustNexus Design System](../constants/theme.ts)
