# Sumsub SDK Setup Guide

This guide provides complete instructions for setting up the Sumsub React Native SDK in the Trust Nexus project.

## 🚀 Quick Setup

Run the automated setup script:

```bash
npm run setup-sumsub
```

This script will:

- ✅ Configure iOS Podfile with Sumsub sources
- ✅ Add all required iOS permissions to Info.plist
- ✅ Configure Android Maven repository
- ✅ Enable VideoIdent and eID modules
- ✅ Update app.json with all configurations
- ✅ Install CocoaPods dependencies
- ✅ Clean Android build

## 📱 iOS Manual Steps

After running the setup script, you need to manually enable iOS capabilities in Xcode:

1. Open the iOS project in Xcode:

   ```bash
   xed ios/
   ```

2. Select your project target (trustnexus)

3. Go to **Signing & Capabilities** tab

4. Add **Near Field Communication Tag Reading** capability

   - Click the "+" button
   - Search for "Near Field Communication Tag Reading"
   - Add it to your project

5. Add **Background Modes** capability
   - Click the "+" button
   - Search for "Background Modes"
   - Enable: **Audio, AirPlay, and Picture in Picture**

## 🤖 Android Requirements

Ensure your Android project meets these requirements:

- **Kotlin**: 1.7.10 or higher
- **minSdkVersion**: 21 (Android 5.0) or higher
- **Gradle**: Compatible version with React Native

## 📦 Modules Configuration

### MRTDReader (NFC)

- **iOS**: Automatically enabled via ENV variable
- **Android**: Included by default
- **Purpose**: Read electronic chips on MRTD documents

### VideoIdent

- **iOS**: Requires iOS 12.2+
- **Android**: Enabled in module build.gradle
- **Purpose**: Video identification verification

### eID

- **iOS**: Enabled via ENV variable
- **Android**: Enabled in module build.gradle
- **Purpose**: Electronic ID verification

## 🔧 Available Scripts

```bash
# Full setup with clean rebuild
npm run prebuild:clean

# Setup without prebuild
npm run setup-sumsub

# iOS-specific setup
npm run ios:setup

# Clean Android build
npm run android:clean

# Verify configuration only
npm run sumsub:verify
```

## 📋 Configuration Files

### app.json

All iOS permissions and NFC identifiers are configured in `app.json`:

```json
{
  "expo": {
    "ios": {
      "infoPlist": {
        "NSCameraUsageDescription": "Let us take a photo",
        "NSMicrophoneUsageDescription": "Time to record a video",
        "NSPhotoLibraryUsageDescription": "Let us pick a photo",
        "NSLocationWhenInUseUsageDescription": "Please provide us with your geolocation data to prove your current location",
        "NFCReaderUsageDescription": "Let us scan the document for more precise recognition",
        "com.apple.developer.nfc.readersession.iso7816.select-identifiers": [
          "A0000002471001",
          "A0000002472001",
          "00000000000000",
          "E80704007F00070302"
        ]
      }
    }
  }
}
```

### iOS Podfile

The following is automatically added to `ios/Podfile`:

```ruby
source 'https://cdn.cocoapods.org/'
source 'https://github.com/SumSubstance/Specs.git'

# Enable MRTDReader (NFC) module
ENV['IDENSIC_WITH_MRTDREADER'] = 'true'

# Enable VideoIdent module
ENV['IDENSIC_WITH_VIDEOIDENT'] = 'true'

# Enable EID module
ENV['IDENSIC_WITH_EID'] = 'true'
```

### Android build.gradle

The Maven repository is added to `android/build.gradle`:

```gradle
allprojects {
  repositories {
    // ... other repositories
    maven { url "https://maven.sumsub.com/repository/maven-public/" }
  }
}
```

## 🐛 Troubleshooting

### iOS Issues

**Pod install fails with "IdensicMobileSDK not found"**

```bash
cd ios && pod update IdensicMobileSDK && pod install
```

**Build fails with bitcode error**

- Disable bitcode in Xcode project settings

### Android Issues

**Build fails with "Could not find com.sumsub.sns"**

- Ensure Maven repository is added to `android/build.gradle`
- Clean and rebuild: `cd android && ./gradlew clean`

**minSdkVersion error**

- Update `minSdkVersion` to 21 or higher in `android/app/build.gradle`

### Common Issues

**Module not found after setup**

```bash
# Clean everything and reinstall
rm -rf node_modules ios/Pods android/build
npm install
npm run setup-sumsub
```

## 📚 SDK Usage

After setup, use the SDK in your React Native code:

```javascript
import SNSMobileSDK from "@sumsub/react-native-mobilesdk-module";

const launchVerification = async () => {
  const accessToken = "your_access_token"; // Get from backend

  const snsMobileSDK = SNSMobileSDK.init(accessToken, () => {
    // Token expiration handler
    return fetchNewToken(); // Implement token refresh
  })
    .withHandlers({
      onStatusChanged: (event) => {
        console.log("Status changed:", event);
      },
      onLog: (event) => {
        console.log("SDK Log:", event.message);
      },
    })
    .withDebug(true)
    .withLocale("en")
    .build();

  try {
    const result = await snsMobileSDK.launch();
    console.log("Verification result:", result);
  } catch (error) {
    console.error("Verification error:", error);
  }
};
```

## 🎨 Theme Customization

The Sumsub SDK has been customized to match the TrustNexus app design:

```bash
npm run sumsub:test-theme  # Validate theme setup
```

**Key Features:**

- ✅ TrustNexus brand colors applied throughout
- ✅ SpaceMono font consistency
- ✅ Matching button heights and corner radius
- ✅ Consistent spacing and layout
- ✅ Platform-specific optimizations

For detailed theme documentation, see [SUMSUB_THEME_CUSTOMIZATION.md](./SUMSUB_THEME_CUSTOMIZATION.md)

## 🔗 Resources

- [Sumsub React Native Documentation](https://docs.sumsub.com/docs/react-native-module)
- [iOS SDK Documentation](https://docs.sumsub.com/docs/ios-sdk)
- [Android SDK Documentation](https://docs.sumsub.com/docs/android-sdk)
- [Sumsub Dashboard](https://cockpit.sumsub.com/)
- [Theme Customization Guide](./SUMSUB_THEME_CUSTOMIZATION.md)

## 📝 Version Information

- **Sumsub SDK Version**: 1.35.1
- **React Native**: 0.79.2
- **Expo SDK**: 53.0.9

Last updated: December 2024
