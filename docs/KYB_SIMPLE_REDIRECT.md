# KYB Simple Redirect Implementation

## 📋 Overview

Simple implementation for KYB (Know Your Business) verification that redirects users to the web application for `kybtier1` verification, then returns them to the mobile app via deep linking.

## 🎯 Use Cases

- **After Registration**: When user completes registration and needs to start KYB verification
- **Upgrade KYB**: When user clicks "UPGRADE KYB" button from company details
- **Skip & Upgrade**: When user skips initial verification and later decides to upgrade

## 🏗️ Implementation

### 1. KYB Redirect Service (`services/sumsub/kyb-redirect-service.ts`)

**Simple service that handles web redirect:**

```typescript
export class KYBRedirectService {
  // Check if tier requires web redirect (only kybtier1)
  static isKYBTier(tier: SUMSUB_LEVEL): boolean;

  // Open web browser with authentication
  static async openKYBInBrowser(tier: SUMSUB_LEVEL): Promise<void>;

  // Handle deep link return from web
  static handleKYBDeepLink(
    url: string
  ): { tier: string; success: boolean } | null;

  // Show completion alert
  static showKYBCompletionAlert(result, onComplete?: () => void): void;
}
```

### 2. Enhanced SDK Launcher (`services/sumsub/launchSNSMobileSDK.ts`)

**Automatic detection and routing:**

```typescript
export let launchSNSMobileSDK = async ({
  sumSubData,
  statusChangeHandler,
  logHandler,
  tier, // New parameter
}: LaunchSNSMobileSDKProps) => {
  // Check if kybtier1 → redirect to web
  if (tier && KYBRedirectService.isKYBTier(tier)) {
    await KYBRedirectService.openKYBInBrowser(tier);
    return;
  }

  // Continue with mobile SDK for KYC
};
```

### 3. Deep Link Hook (`hooks/useKYBDeepLink.ts`)

**Handles return from web:**

```typescript
export const useKYBDeepLink = () => {
  // Listen for deep links
  // Handle KYB completion
  // Show success/failure alerts
};
```

## 🔗 Deep Linking

### URL Format

**Return URL**: `myapp://kyb-complete?tier=kybtier1&status=success&timestamp=**********`

### Parameters

- `tier`: The KYB tier that was completed
- `status`: `success`, `completed`, `failed`, or `cancelled`
- `timestamp`: Unix timestamp for link validation

## 🌐 Web Integration

### URL Generation

```typescript
const webUrl =
  `${WEB_BASE_URL}/kyb-verification?` +
  `tier=${tier}&` +
  `token=${encodeURIComponent(token)}&` +
  `email=${encodeURIComponent(userEmail)}&` +
  `returnUrl=${encodeURIComponent(deepLinkUrl)}`;
```

### Expected Web Behavior

1. **Authentication**: Web receives token and email for auto-login
2. **Verification**: User completes KYB verification on web
3. **Return**: Web redirects to `returnUrl` with completion status

## 🚀 Usage Examples

### Basic Usage

```typescript
// In your verification trigger (e.g., "START KYB" button)
import { launchSNSMobileSDK } from "@/services/sumsub/launchSNSMobileSDK";

const handleStartKYB = async () => {
  const sumSubData = await getSumsubAccessToken();

  await launchSNSMobileSDK({
    sumSubData,
    statusChangeHandler: handleStatusChange,
    tier: SUMSUB_LEVEL.KYB_TIER1, // This will redirect to web
  });
};
```

### Upgrade KYB Button

```typescript
// In company details page
const handleUpgradeKYB = async () => {
  try {
    await KYBRedirectService.openKYBInBrowser(SUMSUB_LEVEL.KYB_TIER1);

    Alert.alert(
      "KYB Verification",
      "You've been redirected to complete your business verification.",
      [{ text: "OK" }]
    );
  } catch (error) {
    Alert.alert("Error", "Failed to open verification. Please try again.");
  }
};
```

## 🧪 Testing

### Test Deep Link

```bash
# iOS Simulator
xcrun simctl openurl booted "myapp://kyb-complete?tier=kybtier1&status=success&timestamp=**********"

# Android
adb shell am start -W -a android.intent.action.VIEW -d "myapp://kyb-complete?tier=kybtier1&status=success&timestamp=**********"
```

### Test Scenarios

1. **Successful Completion**: `status=success`
2. **Failed Verification**: `status=failed`
3. **User Cancelled**: `status=cancelled`
4. **Old Link**: timestamp older than 1 hour (should be ignored)

## 🔧 Configuration

### Environment URLs

```typescript
// Development
const WEB_BASE_URL = "https://dev.profitecosystem.com";

// Production
const WEB_BASE_URL = "https://app.profitecosystem.com";
```

### Deep Link Scheme

Configured in `app.json`:

```json
{
  "expo": {
    "scheme": "myapp"
  }
}
```

## 🛠️ Troubleshooting

### Common Issues

1. **Browser Won't Open**

   - Check if URL is valid
   - Verify user authentication
   - Check device browser availability

2. **Deep Link Not Working**

   - Verify app scheme configuration
   - Check if app is properly installed
   - Test with different app states (foreground/background/closed)

3. **Authentication Issues**
   - Verify token is valid and not expired
   - Check user email is available
   - Ensure web can handle the authentication

### Debug Logging

```typescript
console.log("🏢 Opening KYB verification in browser for tier:", tier);
console.log("🌐 Opening KYB URL:", webUrl);
console.log("🔗 Handling KYB deep link:", url);
console.log("✅ KYB deep link parsed:", { tier, success });
```

## 📱 User Experience

### Flow

1. **User Action**: Clicks "START KYB" or "UPGRADE KYB"
2. **Redirect**: App opens web browser with authentication
3. **Verification**: User completes KYB on web
4. **Return**: Web redirects back to app via deep link
5. **Completion**: App shows success/failure alert

### Benefits

- ✅ **Simple Implementation**: No complex WebView setup
- ✅ **Native Browser**: Better performance and security
- ✅ **Familiar UX**: Users know they're on the web
- ✅ **Easy Maintenance**: Less mobile-specific code
- ✅ **Fallback Ready**: Works even if deep link fails

## 🔄 Future Enhancements

- **Token Refresh**: Handle token expiration during verification
- **Progress Tracking**: Optional progress updates via API
- **Multiple Tiers**: Easy to extend for kybtier2, kybtier3
- **Analytics**: Track completion rates and user behavior
