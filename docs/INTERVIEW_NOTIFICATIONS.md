# Interview Notification System

This document describes the implementation of the interview notification system in Trust Nexus Native.

## Overview

The interview notification system provides real-time notifications for interview requests using both socket-based notifications (for logged-in users) and polling notifications (for non-logged-in users).

## Architecture

### Core Components

1. **SignalR Service** (`services/notification/signalr-notification-service.ts`)

   - Manages WebSocket connections for real-time notifications
   - <PERSON>les connection lifecycle and user group management
   - Transforms notifications to mobile format

2. **Polling API** (`services/notification/interview-notification-api.ts`)

   - Provides REST API calls for notification polling
   - Handles user notification retrieval for non-authenticated users
   - Supports interview completion status checking

3. **React Query Hooks** (`services/notification/interview-notification-hooks.ts`)

   - Manages API state and caching for polling notifications
   - Provides polling with 30-second intervals for non-authenticated users
   - <PERSON><PERSON> notification sending for testing purposes

4. **Notification Modal** (`components/modals/interview-notification-modal.tsx`)

   - Bottom sheet modal following Trust Nexus Native patterns
   - Integrates with Sumsub SDK for interview verification
   - <PERSON>les accept/decline actions for both SignalR and polling notifications

5. **Notification Provider** (`providers/notification/notification-provider.tsx`)

   - Orchestrates both SignalR and polling notifications
   - Manages modal state and user interactions
   - Handles authentication state changes and interview completion checks
   - Integrates with app lifecycle and authentication

## Features

### Socket Notifications (Logged-in Users)

- Real-time notifications via SignalR WebSocket connection
- Automatic connection management and reconnection
- User group-based message routing
- Connection status indicators

### Polling Notifications (Non-authenticated Users)

- REST API polling every 30 seconds for non-logged-in users only
- Interview completion status checking
- Most recent notification display
- Displays modal if interview not completed

### Interview Flow Integration

- Seamless integration with existing Sumsub SDK
- Uses `SUMSUB_LEVEL.INTERVIEW` for interview verification
- Proper status handling and completion detection
- Error handling and fallback mechanisms

### UI/UX Features

- Bottom sheet modal with slide animations
- Rounded top corners following app design patterns
- Accept/Decline buttons with loading states
- Development debug information
- Consistent styling with Trust Nexus Native theme

## API Endpoints

### Socket Notifications

- **Endpoint**: `/api/v1/Notifications/send`
- **Method**: POST
- **Purpose**: Send notifications via SignalR to connected users

### Polling Notifications

- **Endpoint**: `/api/v1/Notifications/user/{userId}`
- **Method**: GET
- **Purpose**: Retrieve all notifications for a specific user

## Configuration

### Environment Variables

- `EXPO_PUBLIC_API_URL`: Base API URL for notification endpoints

### Dependencies

- `@microsoft/signalr`: SignalR client for WebSocket connections
- `@tanstack/react-query`: State management and caching
- `react-native-modal`: Modal component with animations

## Usage

### Automatic Integration

The notification system is automatically integrated into the app via the `NotificationProvider` in the providers tree. No additional setup is required.

### Testing

A debug component (`InterviewNotificationTest`) is available in development mode on the home screen for testing notifications.

### Manual Testing via Swagger

1. Open Swagger UI at your API endpoint
2. Navigate to `/api/v1/Notifications/send`
3. Send a test notification with:
   ```json
   {
     "userId": "user-id-here",
     "message": "You have been invited for an interview verification.",
     "title": "Interview Request",
     "type": "interview"
   }
   ```

## Implementation Details

### Notification Flow

1. **Socket Notification**: Backend sends notification via SignalR
2. **Client Receives**: Mobile app receives notification through WebSocket
3. **Modal Display**: Interview notification modal appears automatically
4. **User Action**: User can accept or decline the interview
5. **Sumsub Integration**: If accepted, Sumsub SDK launches with interview level
6. **Completion**: Interview completion updates user status

### Polling Flow

1. **API Polling**: App polls `/api/v1/Notifications/user/{userId}` every 30 seconds
2. **Status Check**: Checks `userInfo.interviewCompletedDate` to determine if interview is needed
3. **Modal Display**: Shows modal for most recent interview notification if not completed
4. **User Action**: Same accept/decline flow as socket notifications

### Error Handling

- Connection failures automatically trigger polling fallback
- Sumsub SDK errors are logged and handled gracefully
- Network errors are retried automatically by React Query
- User feedback is provided for all error states

## File Structure

```
├── types/
│   ├── interview-notification.ts     # Type definitions
│   └── auth.ts                      # Updated with interview fields
├── services/notification/
│   ├── signalr-notification-service.ts    # SignalR WebSocket service
│   ├── interview-notification-api.ts      # REST API service
│   └── interview-notification-hooks.ts    # React Query hooks
├── hooks/
│   └── useSignalRNotifications.ts         # SignalR React hook
├── components/
│   ├── modals/
│   │   └── interview-notification-modal.tsx    # Interview modal
│   └── debug/
│       └── interview-notification-test.tsx     # Testing component
└── providers/notification/
    └── notification-provider.tsx               # Main notification provider
```

## Security Considerations

- User authentication is verified before establishing SignalR connections
- User IDs are validated on both client and server sides
- Interview completion status prevents duplicate notifications
- All API calls use proper authentication headers

## Performance Considerations

- SignalR connections are automatically managed and cleaned up
- Polling is disabled when SignalR is connected to reduce API calls
- React Query provides efficient caching and deduplication
- Modal components are only rendered when needed

## Future Enhancements

- Push notifications for background app states
- Notification history and management
- Custom notification sounds and vibrations
- Batch notification handling
- Advanced retry mechanisms
