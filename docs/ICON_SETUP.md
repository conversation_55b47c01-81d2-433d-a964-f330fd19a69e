# Trust Nexus Native - Icon Setup Documentation

## Problem Resolved
Fixed icon rendering issues where app icons and splash screen icons appeared cropped or cut off on both iOS and Android platforms.

## Root Cause
The original icon configuration was using `small-logo.png` (43x49 pixels) which was too small for proper app icon generation. This caused:
- Severe cropping on Android adaptive icons
- Poor quality scaling on iOS
- Inconsistent splash screen appearance

## Solution Implemented

### 1. SVG to PNG Conversion System
Created an automated icon generation system using the SVG source file:

**Source File**: `assets/images/small-logo.svg`
**Generated Files**:
- `icon.png` (1024x1024) - Main app icon for iOS App Store
- `adaptive-icon.png` (432x432) - Android adaptive icon with proper padding
- `splash-icon.png` (512x512) - Splash screen icon
- `favicon.png` (256x256) - Web favicon

### 2. Android Adaptive Icon Optimization
Special handling for Android adaptive icons:
- **Total Size**: 432x432 pixels
- **Content Area**: 288x288 pixels (safe zone)
- **Padding**: 72 pixels on all sides
- This ensures the icon displays correctly when cropped into different shapes (circle, square, rounded square)

### 3. Configuration Updates
Updated `app.json` with proper icon references:
```json
{
  "expo": {
    "icon": "./assets/images/icon.png",
    "plugins": [
      [
        "expo-splash-screen",
        {
          "image": "./assets/images/splash-icon.png",
          "imageWidth": 200,
          "resizeMode": "contain",
          "backgroundColor": "#ffffff"
        }
      ]
    ],
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/images/adaptive-icon.png",
        "backgroundColor": "#ffffff"
      }
    }
  }
}
```

## Usage Instructions

### Generating Icons
Run the icon generation script whenever you update the SVG source:
```bash
npm run generate-icons
```

### Rebuilding Platform Icons
After generating new icons, rebuild the platform-specific files:
```bash
npm run prebuild:clean
```

### Testing
Test the icons on both platforms:
```bash
# iOS
npx expo run:ios

# Android
npx expo run:android
```

## File Structure
```
assets/images/
├── small-logo.svg          # Source SVG file
├── icon.png               # Main app icon (1024x1024)
├── adaptive-icon.png      # Android adaptive icon (432x432 with padding)
├── splash-icon.png        # Splash screen icon (512x512)
└── favicon.png           # Web favicon (256x256)

scripts/
└── generate-icons.js      # Icon generation script

ios/trustnexus/Images.xcassets/
├── AppIcon.appiconset/    # iOS app icons
└── SplashScreenLogo.imageset/  # iOS splash screen icons

android/app/src/main/res/
├── mipmap-*/ic_launcher*  # Android app icons
└── drawable-*/splashscreen_logo.png  # Android splash icons
```

## Technical Details

### Dependencies
- `sharp` - Image processing library for SVG to PNG conversion
- `expo-splash-screen` - Splash screen configuration
- Expo prebuild system for platform-specific icon generation

### Icon Requirements
- **iOS**: 1024x1024 PNG for App Store submission
- **Android Adaptive**: 432x432 with 72px padding on all sides
- **Splash Screen**: 512x512 for optimal display across devices
- **Web**: 256x256 favicon

### Quality Standards
- All icons generated from vector SVG source for maximum quality
- Transparent backgrounds for proper compositing
- Proper padding for Android adaptive icon system
- Optimized sizes for each platform's requirements

## Troubleshooting

### Icons Still Appear Cropped
1. Verify SVG source file is properly centered
2. Run `npm run generate-icons` to regenerate
3. Run `npm run prebuild:clean` to rebuild platform files
4. Clear app cache and reinstall

### Build Errors
1. Ensure all icon files exist in `assets/images/`
2. Check `app.json` configuration matches file paths
3. Verify `sharp` dependency is installed: `npm install --save-dev sharp`

### Platform-Specific Issues
- **iOS**: Check Xcode project for proper icon asset references
- **Android**: Verify adaptive icon XML configuration in `res/mipmap-anydpi-v26/`

## Maintenance
- Update SVG source file when logo changes
- Run icon generation script after any logo updates
- Test on both platforms after regeneration
- Keep backup of working icon files
