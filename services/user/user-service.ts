import { api, API_ROUTES, AUTH_TOKEN_KEY, USER_ID_KEY } from "@/constants/api";
import { GetCurrentUserResponse, SUMSUB_LEVEL } from "@/types/auth";
import { GetDocumentsResponse } from "@/types/document";
import { getStorageItemAsync } from "@/utils/storage";
import { getTierNumber } from "@/utils/tier-number";

export const getCurrentUser = async () => {
  try {
    const userId = await getStorageItemAsync(USER_ID_KEY);
    const token = await getStorageItemAsync(AUTH_TOKEN_KEY);
    if (!userId || !token) {
      throw new Error("User id not found");
    }
    const response = await api.get<GetCurrentUserResponse>(
      `${API_ROUTES.ACCOUNTS}/${userId}`
    );
    if (!response.isSuccess) {
      throw new Error("Error getting current user");
    }
    const tierAsNumber = getTierNumber(response?.data?.state as SUMSUB_LEVEL);
    return { ...response?.data, tierAsNumber };
  } catch (error) {
    throw new Error("Error getting current user");
  }
};

export const getUserDocuments = async (applicantId: string) => {
  try {
    const response = await api.get<GetDocumentsResponse>(
      `${API_ROUTES.ACCOUNTS}/get-documents-for-applicantid/${applicantId}`
    );

    return response;
  } catch (error) {
    throw new Error("Error getting user documents");
  }
};
