import { useQuery, useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "../query-keys";
import { api, API_ROUTES } from "@/constants/api";
import { TieredDocumentsResponse } from "@/types/document";

interface UseGetDocumentsByTierOptions {
  enabled?: boolean;
  refetchInterval?: number;
  refetchOnWindowFocus?: boolean;
}

// Service function to get documents by tier
const getDocumentsByTier = async (applicantId: string): Promise<TieredDocumentsResponse> => {
  try {
    const response = await api.get<TieredDocumentsResponse>(
      `${API_ROUTES.ACCOUNTS}/get-documents-for-applicantid-all-tiers/${applicantId}`
    );

    if (!response.isSuccess) {
      throw new Error("Failed to fetch documents by tier");
    }

    return response;
  } catch (error) {
    throw new Error("Error getting documents by tier");
  }
};

export const useGetDocumentsByTier = (
  applicantId?: string,
  options?: UseGetDocumentsByTierOptions
) => {
  const queryClient = useQueryClient();

  const query = useQuery<TieredDocumentsResponse>({
    queryKey: [QUERY_KEYS.DOCUMENTS_BY_TIER, applicantId],
    queryFn: () => getDocumentsByTier(applicantId!),
    enabled: !!applicantId && (options?.enabled ?? true),
    refetchInterval: options?.refetchInterval,
    refetchOnWindowFocus: options?.refetchOnWindowFocus,
    retry: false,
  });

  // Invalidate queries helper functions
  const invalidateDocumentsByTier = async () => {
    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.DOCUMENTS_BY_TIER, applicantId],
    });
  };

  const invalidateAllDocumentQueries = async () => {
    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.DOCUMENTS_BY_TIER],
    });
    await queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.USER_DOCUMENTS],
    });
  };

  return {
    ...query,
    invalidateDocumentsByTier,
    invalidateAllDocumentQueries,
  };
};
