import { getCurrentUser, getUserDocuments } from "@/services/user/user-service";
import { useQuery } from "@tanstack/react-query";
import { QUERY_KEYS } from "../query-keys";

export const useCurrentUser = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.CURRENT_USER],
    queryFn: getCurrentUser,
  });
};

export const useUserDocuments = (applicantId?: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.USER_DOCUMENTS, applicantId],
    queryFn: () => getUserDocuments(applicantId ?? ""),
    enabled: !!applicantId,
    retry: false,
  });
};
