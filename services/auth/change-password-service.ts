import { api, API_ROUTES } from "@/constants/api";
import { getCurrentUserId } from "@/utils/token-utils";

export interface ChangePasswordInputs {
  password_current: string;
  password_new: string;
  password_confirm: string;
}

export interface ChangePasswordResponse {
  isSuccess: boolean;
  message: string;
  data?: any;
}

/**
 * Change user password using mobile API
 */
export const changePassword = async (
  inputs: ChangePasswordInputs
): Promise<ChangePasswordResponse> => {
  try {
    console.log("🔐 Attempting password change");

    // Get current user ID
    const userId = await getCurrentUserId();
    if (!userId) {
      throw new Error("Authentication required. Please log in again.");
    }

    // Map the form data to the API expected format
    const apiPayload = {
      password: inputs.password_new,
      confirmPassword: inputs.password_confirm,
      user_id: userId,
    };

    console.log("🔐 Change password API call:", {
      userId,
      payload: { ...apiPayload, password: "[REDACTED]", confirmPassword: "[REDACTED]" }
    });

    const response = await api.post<ChangePasswordResponse>(
      "/Accounts/update-password",
      apiPayload
    );

    console.log("✅ Change password response:", response);

    if (!response.isSuccess) {
      throw new Error(response.message || "Failed to change password");
    }

    return response;
  } catch (error: any) {
    console.error("❌ Change password failed:", error);
    throw new Error(error?.message || "Failed to change password");
  }
};
