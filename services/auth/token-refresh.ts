import { API_ROUTES, API_URL } from "@/constants/api-constants";
import {
  getAuthStatus,
  getCurrentToken,
  getCurrentUserId,
  storeToken,
  storeUserId,
} from "@/utils/token-utils";

// Force logout function that clears refresh state and triggers main logout
const forceLogout = async (): Promise<void> => {
  try {
    console.log("🚪 Forcing logout and clearing auth data");

    // Clear refresh state
    isRefreshing = false;
    refreshPromise = null;

    // Trigger the main logout function to clear data and notify listeners
    const { triggerLogout } = await import("@/constants/api");
    await triggerLogout();
  } catch (error) {
    console.error("Error during force logout:", error);
    throw error;
  }
};

// Types for refresh operations
export interface RefreshResult {
  isSuccess: boolean;
  message: string;
  data?: {
    token: string;
    user_id?: string;
  } | null;
  shouldLogout: boolean;
  error?: string;
}

interface RefreshTokenResponse {
  isSuccess: boolean;
  message?: string;
  data?: {
    token: string;
    user_id?: string;
  } | null;
  errors?: Array<{
    code: number;
    message: string;
    source: string;
    severity: number;
    customMessage: string;
  }>;
}

// Track refresh attempts to prevent concurrent refreshes
let isRefreshing = false;
let refreshPromise: Promise<RefreshResult> | null = null;

/**
 * Refresh access token using the refresh endpoint
 * @param fromBackground - Whether this is called from background or middleware
 */
export const refreshAccessToken = async (
  fromBackground = false
): Promise<RefreshResult> => {
  try {
    const currentToken = await getCurrentToken();
    const userId = await getCurrentUserId();

    if (!userId) {
      console.warn("No user ID available for token refresh");
      return {
        isSuccess: false,
        message: "No user ID available for refresh",
        data: null,
        shouldLogout: true,
        error: "NO_USER_ID",
      };
    }

    // Check if we have a token
    if (!currentToken) {
      console.warn("No token available for refresh");
      return {
        isSuccess: false,
        message: "No token available for refresh",
        data: null,
        shouldLogout: true,
        error: "NO_TOKEN",
      };
    }

    console.log("🔄 Attempting token refresh for user:", userId);

    // Prepare headers
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    // Add current token to headers if available
    if (currentToken) {
      headers["Authorization"] = `Bearer ${currentToken.replace(
        /^Bearer\s+/,
        ""
      )}`;
    }

    // Make refresh request
    const response = await fetch(
      `${API_URL}${API_ROUTES.TOKEN}/refresh-token`,
      {
        method: "POST",
        headers,
        body: JSON.stringify({
          user_id: userId,
        }),
      }
    );

    if (!response.ok) {
      console.error("Token refresh failed with status:", response.status);

      // Handle different error statuses
      if (response.status === 401 || response.status === 403) {
        return {
          isSuccess: false,
          message: "Session expired, please log in again",
          data: null,
          shouldLogout: true,
          error: "UNAUTHORIZED",
        };
      }

      return {
        isSuccess: false,
        message: `Token refresh failed with status ${response.status}`,
        data: null,
        shouldLogout: response.status === 401 || response.status === 403,
        error: "NETWORK_ERROR",
      };
    }

    const data: RefreshTokenResponse = await response.json();

    if (data?.isSuccess && data?.data?.token) {
      const newToken = data.data.token.replace(/^Bearer\s+/, "");

      console.log("✅ Token refresh successful");

      // Store the new token
      await storeToken(newToken);

      // Update user ID if provided
      if (data.data.user_id) {
        await storeUserId(data.data.user_id);
      }

      return {
        isSuccess: true,
        message: "Token refreshed successfully",
        data: {
          token: newToken,
          user_id: data.data.user_id,
        },
        shouldLogout: false,
      };
    } else {
      console.error("Token refresh API returned failure:", data);

      return {
        isSuccess: false,
        message: data?.message || "Token refresh failed",
        data: null,
        shouldLogout: true,
        error: "API_ERROR",
      };
    }
  } catch (error: any) {
    console.error("Token refresh error:", error);

    return {
      isSuccess: false,
      message: error?.message || "Token refresh failed due to network error",
      data: null,
      shouldLogout: !fromBackground, // Don't force logout on background errors
      error: "NETWORK_ERROR",
    };
  }
};

/**
 * Check if token needs refresh and refresh if necessary
 * @param force - Force refresh even if token seems valid
 * @param fromBackground - Whether this is called from background
 */
export const maybeRefreshToken = async (
  force = false,
  fromBackground = false
): Promise<RefreshResult> => {
  try {
    // Prevent concurrent refresh attempts
    if (isRefreshing && refreshPromise) {
      console.log("⏳ Token refresh already in progress, waiting...");
      return await refreshPromise;
    }

    const authStatus = await getAuthStatus();

    // If we're not authenticated at all, don't attempt refresh
    if (!authStatus.token || !authStatus.userId) {
      return {
        isSuccess: false,
        message: "No authentication data available",
        data: null,
        shouldLogout: true,
        error: "NO_AUTH_DATA",
      };
    }

    // Check if refresh is needed
    const needsRefresh =
      force || authStatus.needsRefresh || authStatus.isExpired;

    if (!needsRefresh) {
      console.log("✅ Token is still valid, no refresh needed");
      return {
        isSuccess: true,
        message: "Token is still valid",
        data: {
          token: authStatus.token,
          user_id: authStatus.userId,
        },
        shouldLogout: false,
      };
    }

    console.log("🔄 Token needs refresh:", {
      isExpired: authStatus.isExpired,
      needsRefresh: authStatus.needsRefresh,
      timeLeft: authStatus.timeUntilExpiration,
      force,
    });

    // Set refresh state and create promise
    isRefreshing = true;
    refreshPromise = refreshAccessToken(fromBackground);

    try {
      const result = await refreshPromise;
      return result;
    } finally {
      // Clear refresh state
      isRefreshing = false;
      refreshPromise = null;
    }
  } catch (error: any) {
    console.error("Error in maybeRefreshToken:", error);

    // Clear refresh state on error
    isRefreshing = false;
    refreshPromise = null;

    return {
      isSuccess: false,
      message: error?.message || "Token refresh check failed",
      data: null,
      shouldLogout: !fromBackground,
      error: "UNKNOWN_ERROR",
    };
  }
};

/**
 * Get authentication headers with automatic token refresh
 * This is the main function to use for API calls
 */
export const getAuthHeaders = async (): Promise<HeadersInit> => {
  try {
    // Try to refresh token if needed
    const refreshResult = await maybeRefreshToken();

    const baseHeaders: HeadersInit = {
      "Content-Type": "application/json",
    };

    // If refresh was successful or token is still valid, add authorization header
    if (refreshResult.isSuccess && refreshResult.data?.token) {
      baseHeaders["Authorization"] = `Bearer ${refreshResult.data.token}`;
    } else if (refreshResult.shouldLogout) {
      // If we should logout, don't include auth header
      console.warn("Authentication failed, removing auth header");
    } else {
      // Try to get current token as fallback
      const currentToken = await getCurrentToken();
      if (currentToken) {
        baseHeaders["Authorization"] = `Bearer ${currentToken.replace(
          /^Bearer\s+/,
          ""
        )}`;
      }
    }

    return baseHeaders;
  } catch (error: any) {
    console.error("Error getting auth headers:", error);

    // Return basic headers without auth on error
    return {
      "Content-Type": "application/json",
    };
  }
};

// Export the forceLogout function
export { forceLogout };

/**
 * Check authentication status and refresh if needed
 * Useful for app startup or periodic checks
 */
export const checkAndRefreshAuth = async (): Promise<boolean> => {
  try {
    const refreshResult = await maybeRefreshToken();

    if (refreshResult.shouldLogout) {
      await forceLogout();
      return false;
    }

    return refreshResult.isSuccess;
  } catch (error) {
    console.error("Error checking and refreshing auth:", error);
    return false;
  }
};

/**
 * Get current refresh status
 */
export const getRefreshStatus = () => ({
  isRefreshing,
  hasRefreshPromise: !!refreshPromise,
});

// Debug utilities (development only)
export const debugTokenInfo = async () => {
  if (__DEV__) {
    const authStatus = await getAuthStatus();
    console.log("🔍 Token Debug Info:", {
      hasToken: !!authStatus.token,
      hasUserId: !!authStatus.userId,
      isAuthenticated: authStatus.isAuthenticated,
      isExpired: authStatus.isExpired,
      needsRefresh: authStatus.needsRefresh,
      timeUntilExpiration: authStatus.timeUntilExpiration,
      refreshStatus: getRefreshStatus(),
    });
  }
};
