import { useMutation, UseMutationOptions } from "@tanstack/react-query";
import { 
  changePassword, 
  ChangePasswordInputs, 
  ChangePasswordResponse 
} from "./change-password-service";

interface ChangePasswordOptions {
  mutationOptions?: Omit<
    UseMutationOptions<ChangePasswordResponse, Error, ChangePasswordInputs>,
    "mutationKey" | "mutationFn"
  >;
}

/**
 * Hook for changing user password
 */
export const useChangePassword = (options?: ChangePasswordOptions) => {
  return useMutation<ChangePasswordResponse, Error, ChangePasswordInputs>({
    mutationKey: ["change-password"],
    mutationFn: changePassword,
    ...options?.mutationOptions,
  });
};
