import { api, API_ROUTES } from "@/constants/api";
import {
  LoginInputs,
  LoginResponse,
  RegistrationInputs,
  RegistrationResponse,
} from "@/types/auth";
import { normalizeEmail, testEmailNormalization } from "@/utils/email-utils";
import { clearAuthData, storeToken, storeUserId } from "@/utils/token-utils";

export const login = async (inputs: LoginInputs) => {
  try {
    // Normalize email to lowercase to ensure case-insensitive login
    const normalizedEmail = normalizeEmail(inputs.email);
    const normalizedInputs = {
      ...inputs,
      email: normalizedEmail,
    };

    console.log("🔑 Attempting login with normalized email:", normalizedEmail);
    console.log(
      "📧 Original email:",
      inputs.email,
      "-> Normalized email:",
      normalizedEmail
    );

    // Run email normalization tests in development
    if (__DEV__) {
      testEmailNormalization();
    }

    const response = await api.post<LoginResponse>(
      API_ROUTES.TOKEN,
      normalizedInputs
    );

    console.log("GET TOKEN RESPONSE ->", response);

    if (!response.isSuccess) {
      throw new Error(response.errors?.[0]?.message || "Something went wrong");
    }

    const token = response?.data?.token?.replace("Bearer ", "");
    const userId = response?.data?.user_id;

    // Store auth data using new token utilities
    if (token) {
      await storeToken(token);
    }
    if (userId) {
      await storeUserId(userId);
    }

    return {
      token,
      user: {
        ...response.data,
      },
    };
  } catch (error: any) {
    console.error("❌ Login failed:", error);
    throw new Error(error?.message || "Something went wrong");
  }
};

export const register = async (inputs: RegistrationInputs) => {
  try {
    // Normalize email to lowercase to ensure consistency
    const normalizedEmail = normalizeEmail(inputs.email);
    const normalizedInputs = {
      ...inputs,
      email: normalizedEmail,
    };

    console.log(
      "📝 Attempting registration with normalized email:",
      normalizedEmail
    );
    console.log(
      "📧 Original email:",
      inputs.email,
      "-> Normalized email:",
      normalizedEmail
    );

    const response = await api.post<RegistrationResponse>(
      API_ROUTES.ACCOUNTS,
      normalizedInputs
    );

    if (!response.isSuccess) {
      throw new Error(response.errors?.[0]?.message || "Something went wrong");
    }

    const loginResponse = await login({
      email: normalizedEmail,
      password: inputs.password,
    });
    return loginResponse.token;
  } catch (error: any) {
    throw new Error(error?.message || "Something went wrong");
  }
};

export const logout = async () => {
  try {
    console.log("🚪 Logging out user");
    await clearAuthData();
    console.log("✅ Logout successful");
  } catch (error) {
    console.error("❌ Logout error:", error);
    throw new Error("Error logging out");
  }
};
