import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "../query-keys";
import {
  createUserCompanyConsent,
  getCompanies,
  getCompanyByID,
  getUserCompaniesConsents,
  revokeUserCompanyConsent,
} from "./company-service";

export const useCompanies = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.ALL_COMPANIES],
    queryFn: getCompanies,
    retry: false,
  });
};

export const useCompanyByID = (id: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.COMPANY_BY_ID, id],
    queryFn: () => getCompanyByID(id),
    enabled: !!id,
    retry: false,
  });
};

export const useUserCompaniesConsents = (userId: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.USER_COMPANIES_CONSENTS, userId],
    queryFn: () => getUserCompaniesConsents(userId),
    enabled: !!userId,
    retry: false,
  });
};

interface CreateUserCompanyConsentOptions {
  onSuccess?: () => void;
  onError?: () => void;
}

export const useRevokeUserCompanyConsent = ({
  onError,
  onSuccess,
}: CreateUserCompanyConsentOptions) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: revokeUserCompanyConsent,
    onSuccess: (data, { userId, companyId }) => {
      console.log("🎉 Revoke consent mutation success callback:", data);

      // Check if we have a valid response (even if isSuccess might be false)
      if (data) {
        console.log("✅ Treating as successful consent revocation");
        onSuccess?.();

        // Invalidate queries to refresh data
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.USER_COMPANIES_CONSENTS, userId],
        });
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.COMPANY_BY_ID, companyId],
        });
      } else {
        console.warn("⚠️ No data returned from consent revocation");
        // Still call onSuccess since we got here without an error
        onSuccess?.();
      }
    },
    onError: (error) => {
      console.error("❌ Revoke consent mutation error callback:", error);
      onError?.();
    },
  });
};

export const useCreateUserCompanyConsent = ({
  onSuccess,
  onError,
}: CreateUserCompanyConsentOptions) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createUserCompanyConsent,
    onSuccess: (data, { userId, companyId }) => {
      console.log("🎉 Consent mutation success callback:", data);

      // Check if we have a valid response (even if isSuccess might be false)
      if (data) {
        console.log("✅ Treating as successful consent creation");
        onSuccess?.();

        // Invalidate queries to refresh data
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.USER_COMPANIES_CONSENTS, userId],
        });
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.COMPANY_BY_ID, companyId],
        });
      } else {
        console.warn("⚠️ No data returned from consent creation");
        // Still call onSuccess since we got here without an error
        onSuccess?.();
      }
    },
    onError: (error) => {
      console.error("❌ Consent mutation error callback:", error);
      onError?.();
    },
  });
};
