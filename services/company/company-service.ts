import { api, API_ROUTES } from "@/constants/api";
import {
  GetCompaniesResponse,
  GetCompanyByIDResponse,
  GetUserCompaniesConsentsResponse,
  UserCompanyConsentResponse,
} from "@/types/company";

export const getCompanies = async () => {
  try {
    const response = await api.get<GetCompaniesResponse>(API_ROUTES.COMPANIES);
    return response.data;
  } catch (error) {
    console.error("Error getting companies:", error);
    throw new Error("Error getting companies");
  }
};

export const getCompanyByID = async (id: string) => {
  try {
    const response = await api.get<GetCompanyByIDResponse>(
      `${API_ROUTES.COMPANIES}/${id}`
    );

    return response.data;
  } catch (error: any) {
    throw new Error(error?.message || "Something went wrong");
  }
};

export const getUserCompaniesConsents = async (userId: string) => {
  try {
    const response = await api.get<GetUserCompaniesConsentsResponse>(
      `${API_ROUTES.USER_COMPANIES_CONSENTS}/${userId}`
    );

    return response.data;
  } catch (error) {
    console.error("Error getting user companies consents:", error);
    throw new Error("Error getting user companies consents");
  }
};

export const revokeUserCompanyConsent = async ({
  userId,
  companyId,
  applicant_id,
}: {
  userId: string;
  companyId: number;
  applicant_id: string;
}) => {
  try {
    console.log("🚀 Revoking user company consent:", {
      userId,
      companyId,
      applicant_id,
    });

    const response = await api.post<UserCompanyConsentResponse>(
      `${API_ROUTES.USER_COMPANIES_CONSENTS_REVOKE}`,
      {
        company_id: companyId,
        user_id: userId,
        applicant_id,
      }
    );

    console.log("📡 Revoke consent API response:", response);

    // Check if the response indicates success
    if (response.isSuccess) {
      console.log("✅ Consent revoked successfully");
      return response.data || true; // Return the actual data or true for success
    } else {
      console.warn("⚠️ API returned success=false for revoke");

      // Check if there are specific error messages
      if (response.errors && response.errors.length > 0) {
        const errorMessage =
          response.errors[0].message || response.errors[0].customMessage;
        console.error("📋 Server error details:", errorMessage);
        throw new Error(errorMessage);
      }

      // Generic error for unsuccessful response
      throw new Error(
        "Consent revocation was not successful. Please try again."
      );
    }
  } catch (error: any) {
    console.error("❌ Error revoking user company consent:", error);

    // Check if this is a network error vs server error
    if (error?.response?.status) {
      console.error("📡 Server responded with status:", error.response.status);
      console.error("📡 Server response data:", error.response.data);

      // If server returned 200 but with isSuccess: false, the consent might still be revoked
      if (error.response.status === 200) {
        console.log(
          "🤔 Server returned 200 but isSuccess: false - consent might be revoked"
        );
        // Let's not throw an error here, return a success indicator
        return true;
      }
    }

    // Re-throw with more specific error message
    throw new Error(error?.message || "Server error. Please try again later.");
  }
};

export const createUserCompanyConsent = async ({
  userId,
  companyId,
  applicant_id,
}: {
  userId: string;
  companyId: number;
  applicant_id: string;
}) => {
  try {
    console.log("🚀 Creating user company consent:", {
      userId,
      companyId,
      applicant_id,
    });

    const response = await api.post<UserCompanyConsentResponse>(
      `${API_ROUTES.USER_COMPANIES_CONSENTS_CREATE}`,
      {
        company_id: companyId,
        user_id: userId,
        applicant_id,
      }
    );

    console.log("📡 Consent API response:", response);

    // Check if the response indicates success
    if (response.isSuccess) {
      console.log("✅ Consent created successfully");
      return response.data || true; // Return the actual data or true for success
    } else {
      console.warn("⚠️ API returned success=false");

      // Check if there are specific error messages
      if (response.errors && response.errors.length > 0) {
        const errorMessage =
          response.errors[0].message || response.errors[0].customMessage;
        console.error("📋 Server error details:", errorMessage);
        throw new Error(errorMessage);
      }

      // Generic error for unsuccessful response
      throw new Error("Consent creation was not successful. Please try again.");
    }
  } catch (error: any) {
    console.error("❌ Error creating user company consent:", error);

    // Check if this is a network error vs server error
    if (error?.response?.status) {
      console.error("📡 Server responded with status:", error.response.status);
      console.error("📡 Server response data:", error.response.data);

      // If server returned 200 but with isSuccess: false, the consent might still be created
      if (error.response.status === 200) {
        console.log(
          "🤔 Server returned 200 but isSuccess: false - consent might be created"
        );
        // Let's not throw an error here, return a success indicator
        return true;
      }
    }

    // Re-throw with more specific error message
    throw new Error(error?.message || "Server error. Please try again later.");
  }
};
