import { SUMSUB_LEVEL } from "@/types/auth";
import { SumsubStatusChangedEvent } from "@/types/sumsub-sdk";
import SNSMobileSDK, {
  type StatusChangedEvent,
} from "@sumsub/react-native-mobilesdk-module";

/**
 * Sumsub SDK Session Manager
 * Handles proper SDK lifecycle management to prevent overlapping instances
 * and ensure clean session transitions during tier upgrades
 */

interface ActiveSession {
  tier: SUMSUB_LEVEL;
  timestamp: number;
  isActive: boolean;
}

class SumsubSDKSessionManager {
  private activeSession: ActiveSession | null = null;
  private sessionTimeout: ReturnType<typeof setTimeout> | null = null;
  private readonly SESSION_TIMEOUT_MS = 30000; // 30 seconds timeout

  /**
   * Check if there's an active SDK session
   */
  public hasActiveSession(): boolean {
    return this.activeSession?.isActive === true;
  }

  /**
   * Get current active session info
   */
  public getActiveSession(): ActiveSession | null {
    return this.activeSession;
  }

  /**
   * Start a new SDK session with proper cleanup of previous sessions
   */
  public async startSession(tier: SUMSUB_LEVEL): Promise<void> {
    console.log(`🔄 Starting new Sumsub SDK session for tier: ${tier}`);

    // Clean up any existing session first
    if (this.hasActiveSession()) {
      console.log(
        "⚠️ Active session detected, cleaning up before starting new session"
      );
      await this.endSession();

      // Wait a brief moment for cleanup to complete
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    // Create new session
    this.activeSession = {
      tier,
      timestamp: Date.now(),
      isActive: true,
    };

    // Set session timeout
    this.sessionTimeout = setTimeout(() => {
      console.log("⏰ SDK session timeout reached, cleaning up");
      this.endSession();
    }, this.SESSION_TIMEOUT_MS);

    console.log(`✅ SDK session started for tier: ${tier}`);
  }

  /**
   * End the current SDK session and cleanup resources
   */
  public async endSession(): Promise<void> {
    if (!this.activeSession) {
      console.log("ℹ️ No active SDK session to end");
      return;
    }

    console.log(`🔚 Ending SDK session for tier: ${this.activeSession.tier}`);

    // Clear timeout
    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout);
      this.sessionTimeout = null;
    }

    // Mark session as inactive
    if (this.activeSession) {
      this.activeSession.isActive = false;
    }

    // Note: The Sumsub SDK doesn't provide explicit cleanup methods
    // The session will be cleaned up when a new SDK instance is created
    // or when the app is backgrounded/closed

    // Clear session reference
    this.activeSession = null;

    console.log("✅ SDK session ended and cleaned up");
  }

  /**
   * Validate if a new session can be started for the given tier
   */
  public canStartSession(tier: SUMSUB_LEVEL): {
    canStart: boolean;
    reason?: string;
  } {
    if (!this.hasActiveSession()) {
      return { canStart: true };
    }

    const activeSession = this.getActiveSession();
    if (!activeSession) {
      return { canStart: true };
    }

    // Check if trying to start the same tier session
    if (activeSession.tier === tier) {
      return {
        canStart: false,
        reason: `SDK session for tier ${tier} is already active`,
      };
    }

    // Check session age
    const sessionAge = Date.now() - activeSession.timestamp;
    if (sessionAge < 5000) {
      // Less than 5 seconds old
      return {
        canStart: false,
        reason: `Previous SDK session is too recent (${Math.round(
          sessionAge / 1000
        )}s ago). Please wait before starting a new session.`,
      };
    }

    return { canStart: true };
  }

  /**
   * Force cleanup of any stuck sessions
   */
  public forceCleanup(): void {
    console.log("🧹 Force cleaning up SDK sessions");

    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout);
      this.sessionTimeout = null;
    }

    this.activeSession = null;

    console.log("✅ Force cleanup completed");
  }

  /**
   * Get session status for debugging
   */
  public getSessionStatus(): {
    hasActive: boolean;
    tier?: SUMSUB_LEVEL;
    age?: number;
    timeoutRemaining?: number;
  } {
    if (!this.activeSession) {
      return { hasActive: false };
    }

    const age = Date.now() - this.activeSession.timestamp;
    const timeoutRemaining = this.sessionTimeout
      ? Math.max(0, this.SESSION_TIMEOUT_MS - age)
      : 0;

    return {
      hasActive: this.activeSession.isActive,
      tier: this.activeSession.tier,
      age,
      timeoutRemaining,
    };
  }
}

// Export singleton instance
export const sdkSessionManager = new SumsubSDKSessionManager();

/**
 * Enhanced status change handler that includes session management
 */
export const createSessionAwareStatusHandler = (
  originalHandler: (event: SumsubStatusChangedEvent) => void,
  tier: SUMSUB_LEVEL
) => {
  return (event: StatusChangedEvent) => {
    console.log(
      `📱 SDK Status Change [${tier}]: ${event.prevStatus} → ${event.newStatus}`
    );

    // Handle session cleanup on completion or failure
    if (
      event.newStatus === "Approved" ||
      event.newStatus === "Failed" ||
      event.newStatus === "ActionCompleted"
    ) {
      console.log(
        `🏁 SDK session completed for tier ${tier}, scheduling cleanup`
      );

      // Delay cleanup slightly to allow for any final processing
      setTimeout(() => {
        sdkSessionManager.endSession();
      }, 2000);
    }

    // Call original handler
    originalHandler(event as SumsubStatusChangedEvent);
  };
};

/**
 * Session-aware error handler
 */
export const createSessionAwareErrorHandler = (tier: SUMSUB_LEVEL) => {
  return (event: unknown) => {
    console.error(`❌ SDK Error [${tier}]:`, event);

    // Clean up session on error
    setTimeout(() => {
      console.log("🧹 Cleaning up SDK session due to error");
      sdkSessionManager.endSession();
    }, 1000);
  };
};
