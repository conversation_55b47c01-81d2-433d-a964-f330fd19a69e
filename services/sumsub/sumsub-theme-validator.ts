import { COLORS } from "@/constants/theme";
import { scale, verticalScale } from "@/utils/styling-scale";

/**
 * Validates the Sumsub theme configuration for consistency and completeness
 * Use this during development to ensure theme is properly configured
 */
export const validateSumsubTheme = (theme: any): { isValid: boolean; issues: string[] } => {
  const issues: string[] = [];

  // Check required structure
  if (!theme.universal) {
    issues.push("Missing 'universal' theme configuration");
    return { isValid: false, issues };
  }

  const { colors, fonts, metrics } = theme.universal;

  // Validate colors
  if (!colors) {
    issues.push("Missing colors configuration");
  } else {
    const requiredColors = [
      'backgroundCommon',
      'primaryButtonBackground', 
      'primaryButtonContent',
      'secondaryButtonBackground',
      'secondaryButtonContent',
      'contentStrong',
      'contentNeutral'
    ];

    requiredColors.forEach(colorKey => {
      if (!colors[colorKey]) {
        issues.push(`Missing required color: ${colorKey}`);
      }
    });

    // Validate color format
    Object.entries(colors).forEach(([key, value]) => {
      if (typeof value === 'string' && !isValidHexColor(value as string)) {
        issues.push(`Invalid color format for ${key}: ${value}`);
      }
    });
  }

  // Validate fonts
  if (!fonts) {
    issues.push("Missing fonts configuration");
  } else {
    if (!fonts.assets || !Array.isArray(fonts.assets)) {
      issues.push("Missing or invalid fonts.assets array");
    }

    const requiredFonts = ['headline1', 'headline2', 'subtitle1', 'subtitle2', 'body', 'caption'];
    requiredFonts.forEach(fontKey => {
      if (!fonts[fontKey]) {
        issues.push(`Missing font configuration: ${fontKey}`);
      }
    });
  }

  // Validate metrics
  if (!metrics) {
    issues.push("Missing metrics configuration");
  } else {
    const requiredMetrics = [
      'buttonHeight',
      'buttonCornerRadius', 
      'fieldHeight',
      'fieldCornerRadius',
      'screenHorizontalMargin'
    ];

    requiredMetrics.forEach(metricKey => {
      if (metrics[metricKey] === undefined) {
        issues.push(`Missing metric: ${metricKey}`);
      }
    });
  }

  return {
    isValid: issues.length === 0,
    issues
  };
};

/**
 * Validates hex color format
 */
const isValidHexColor = (color: string): boolean => {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
  return hexRegex.test(color);
};

/**
 * Generates a theme comparison report between TrustNexus colors and Sumsub theme
 */
export const generateThemeReport = (theme: any): string => {
  const report: string[] = [];
  
  report.push("=== SUMSUB THEME REPORT ===\n");
  
  // Color mapping report
  report.push("🎨 COLOR MAPPING:");
  report.push(`Primary Color: ${COLORS.primary} → primaryButtonBackground`);
  report.push(`Background: ${COLORS.background} → backgroundCommon`);
  report.push(`Secondary: ${COLORS.secondary} → contentStrong`);
  report.push(`Gray: ${COLORS.gray} → contentNeutral`);
  report.push(`Light Gray: ${COLORS.grayLight} → backgroundNeutral`);
  report.push("");

  // Font configuration report
  report.push("📝 TYPOGRAPHY:");
  if (theme.universal?.fonts) {
    const fonts = theme.universal.fonts;
    report.push(`Font Family: ${fonts.headline1?.name || 'Not configured'}`);
    report.push(`Headline1: ${fonts.headline1?.size || 'Not configured'}pt`);
    report.push(`Body: ${fonts.body?.size || 'Not configured'}pt`);
    report.push(`Caption: ${fonts.caption?.size || 'Not configured'}pt`);
  }
  report.push("");

  // Layout metrics report
  report.push("📐 LAYOUT METRICS:");
  if (theme.universal?.metrics) {
    const metrics = theme.universal.metrics;
    report.push(`Button Height: ${metrics.buttonHeight || 'Not configured'}pt`);
    report.push(`Corner Radius: ${metrics.buttonCornerRadius || 'Not configured'}pt`);
    report.push(`Field Height: ${metrics.fieldHeight || 'Not configured'}pt`);
    report.push(`Horizontal Margin: ${metrics.screenHorizontalMargin || 'Not configured'}pt`);
  }
  report.push("");

  // Platform-specific configurations
  report.push("📱 PLATFORM CONFIGURATIONS:");
  report.push(`iOS Config: ${theme.ios ? 'Configured' : 'Not configured'}`);
  report.push(`Android Config: ${theme.android ? 'Configured' : 'Not configured'}`);
  
  return report.join('\n');
};

/**
 * Logs theme validation results to console
 */
export const logThemeValidation = (theme: any): void => {
  console.log("🔍 Validating Sumsub theme configuration...");
  
  const validation = validateSumsubTheme(theme);
  
  if (validation.isValid) {
    console.log("✅ Theme validation passed!");
    console.log(generateThemeReport(theme));
  } else {
    console.warn("⚠️ Theme validation issues found:");
    validation.issues.forEach(issue => {
      console.warn(`  - ${issue}`);
    });
  }
};

/**
 * Creates a minimal test theme for debugging
 */
export const createTestTheme = () => {
  return {
    universal: {
      colors: {
        primaryButtonBackground: "#FF0000", // Red for easy identification
        primaryButtonContent: "#FFFFFF",
        backgroundCommon: "#FFFFFF"
      },
      metrics: {
        buttonHeight: 48,
        buttonCornerRadius: 8
      }
    }
  };
};
