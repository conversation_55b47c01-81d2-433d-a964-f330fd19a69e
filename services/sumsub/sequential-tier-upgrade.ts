import { SUMSUB_LEVEL } from "@/types/auth";
import { SumsubStatusChangedEvent } from "@/types/sumsub-sdk";
import {
  validateTierUpgrade,
  getNextAllowedTier,
  extractTierNumber,
  TIER_NAMES,
} from "@/utils/tier-number";
import { runSumsubProcess } from "./launchSNSMobileSDK";

/**
 * Sequential Tier Upgrade Service
 * Handles automatic progression through intermediate tiers to reach target tier
 * Enforces tier1 → tier2 → tier3 sequential progression rules
 */

interface SequentialUpgradeParams {
  currentTier: string;
  targetTier: SUMSUB_LEVEL;
  userEmail: string;
  isKYB: boolean;
  onProgress?: () => void;
  onComplete?: () => void;
}

interface UpgradeStep {
  fromTier: string;
  toTier: string;
  sumsubLevel: SUMSUB_LEVEL;
  isTarget: boolean;
}

/**
 * Maps tier string to SUMSUB_LEVEL enum
 */
const mapTierStringToSumsubLevel = (
  tierString: string,
  isKYB: boolean
): SUMSUB_LEVEL => {
  if (isKYB) {
    switch (tierString) {
      case "kybtier1":
        return SUMSUB_LEVEL.KYB_TIER1;
      case "kybtier2":
        return SUMSUB_LEVEL.KYB_TIER2;
      case "kybtier3":
        return SUMSUB_LEVEL.KYB_TIER3;
      default:
        throw new Error(`Unsupported KYB tier: ${tierString}`);
    }
  } else {
    switch (tierString) {
      case "tier1":
        return SUMSUB_LEVEL.TIER1;
      case "tier2":
        return SUMSUB_LEVEL.TIER2;
      case "tier3":
        return SUMSUB_LEVEL.TIER3;
      default:
        throw new Error(`Unsupported KYC tier: ${tierString}`);
    }
  }
};

/**
 * Maps SUMSUB_LEVEL enum to tier string
 */
const mapSumsubLevelToTierString = (sumsubLevel: SUMSUB_LEVEL): string => {
  switch (sumsubLevel) {
    case SUMSUB_LEVEL.TIER1:
      return "tier1";
    case SUMSUB_LEVEL.TIER2:
      return "tier2";
    case SUMSUB_LEVEL.TIER3:
      return "tier3";
    case SUMSUB_LEVEL.KYB_TIER1:
      return "kybtier1";
    case SUMSUB_LEVEL.KYB_TIER2:
      return "kybtier2";
    case SUMSUB_LEVEL.KYB_TIER3:
      return "kybtier3";
    default:
      throw new Error(`Unsupported SUMSUB_LEVEL: ${sumsubLevel}`);
  }
};

/**
 * Calculates the sequence of upgrade steps needed to reach target tier
 */
const calculateUpgradeSteps = (
  currentTier: string,
  targetTier: SUMSUB_LEVEL,
  isKYB: boolean
): UpgradeStep[] => {
  const steps: UpgradeStep[] = [];
  const targetTierString = mapSumsubLevelToTierString(targetTier);
  const currentTierNum = extractTierNumber(currentTier);
  const targetTierNum = extractTierNumber(targetTierString);

  console.log(
    `📋 Calculating upgrade steps: ${currentTier} → ${targetTierString}`
  );
  console.log(
    `📊 Current tier number: ${currentTierNum}, Target tier number: ${targetTierNum}`
  );

  // Generate sequential steps
  let stepTier = currentTier;
  for (let tierNum = currentTierNum + 1; tierNum <= targetTierNum; tierNum++) {
    console.log(`🔄 Processing tier ${tierNum}, current stepTier: ${stepTier}`);

    const nextTierString = getNextAllowedTier(stepTier);
    console.log(`➡️ Next allowed tier from ${stepTier}: ${nextTierString}`);

    if (!nextTierString) {
      throw new Error(`Cannot determine next tier from ${stepTier}`);
    }

    const sumsubLevel = mapTierStringToSumsubLevel(nextTierString, isKYB);
    const isTarget = tierNum === targetTierNum;

    const step = {
      fromTier: stepTier,
      toTier: nextTierString,
      sumsubLevel,
      isTarget,
    };

    console.log(
      `📝 Generated step: ${step.fromTier} → ${step.toTier} (${
        isTarget ? "TARGET" : "INTERMEDIATE"
      })`
    );
    steps.push(step);

    stepTier = nextTierString;
  }

  console.log(
    `📋 Generated ${steps.length} upgrade steps:`,
    steps.map(
      (s) =>
        `${s.fromTier} → ${s.toTier} (${
          s.isTarget ? "TARGET" : "INTERMEDIATE"
        })`
    )
  );

  return steps;
};

/**
 * Executes a single upgrade step
 */
const executeUpgradeStep = async (
  step: UpgradeStep,
  userEmail: string,
  onStepComplete: (step: UpgradeStep, success: boolean) => void
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const tierName = TIER_NAMES[extractTierNumber(step.toTier)] || step.toTier;
    console.log(
      `🚀 Starting upgrade step: ${step.fromTier} → ${step.toTier} (${tierName})`
    );

    const statusChangeHandler = (event: SumsubStatusChangedEvent) => {
      console.log(
        `📱 Step Status [${step.toTier}]: ${event.prevStatus} → ${event.newStatus}`
      );

      if (
        event.newStatus === "Approved" ||
        event.newStatus === "ActionCompleted"
      ) {
        console.log(
          `✅ Upgrade step completed: ${step.fromTier} → ${step.toTier}`
        );
        onStepComplete(step, true);
        resolve();
      } else if (event.newStatus === "Failed") {
        console.error(
          `❌ Upgrade step failed: ${step.fromTier} → ${step.toTier}`
        );
        onStepComplete(step, false);
        reject(new Error(`Tier upgrade failed at ${step.toTier}`));
      }
    };

    // Start the Sumsub process for this step
    runSumsubProcess({
      tier: step.sumsubLevel,
      userEmail,
      changeHandler: statusChangeHandler,
    }).catch((error) => {
      console.error(`❌ Failed to start upgrade step ${step.toTier}:`, error);
      onStepComplete(step, false);
      reject(error);
    });
  });
};

/**
 * Main function to start sequential tier upgrade process
 * Automatically progresses through intermediate tiers to reach target
 */
export const startSequentialTierUpgrade = async ({
  currentTier,
  targetTier,
  userEmail,
  isKYB,
  onProgress,
  onComplete,
}: SequentialUpgradeParams): Promise<void> => {
  try {
    console.log(
      `🎯 Starting sequential tier upgrade: ${currentTier} → ${targetTier}`
    );

    // Debug: Check target tier conversion
    const targetTierString = mapSumsubLevelToTierString(targetTier);
    console.log(
      `🔍 Target tier converted: ${targetTier} → ${targetTierString}`
    );
    console.log(`🔍 Is KYB: ${isKYB}`);

    // Calculate all required upgrade steps first
    const upgradeSteps = calculateUpgradeSteps(currentTier, targetTier, isKYB);

    // Validate each sequential step (not the final jump)
    console.log(`🔍 Validating ${upgradeSteps.length} upgrade steps...`);
    for (const step of upgradeSteps) {
      console.log(`🔍 Validating step: ${step.fromTier} → ${step.toTier}`);
      const stepValidation = validateTierUpgrade(step.fromTier, step.toTier);
      console.log(`🔍 Step validation result:`, stepValidation);

      if (!stepValidation.isValid) {
        console.error(
          `❌ Step validation failed: ${step.fromTier} → ${step.toTier}`,
          stepValidation.error
        );
        throw new Error(
          `Invalid upgrade step ${step.fromTier} → ${step.toTier}: ${stepValidation.error}`
        );
      }
    }

    if (upgradeSteps.length === 0) {
      console.log("ℹ️ No upgrade steps needed - user already at target tier");
      onComplete?.();
      return;
    }

    console.log(
      `📈 Sequential upgrade will require ${upgradeSteps.length} step(s)`
    );

    // Execute steps sequentially
    for (let i = 0; i < upgradeSteps.length; i++) {
      const step = upgradeSteps[i];
      const isLastStep = i === upgradeSteps.length - 1;

      console.log(
        `📍 Executing step ${i + 1}/${upgradeSteps.length}: ${
          step.fromTier
        } → ${step.toTier}`
      );

      await executeUpgradeStep(step, userEmail, (_, success) => {
        if (success) {
          console.log(
            `✅ Step ${i + 1}/${upgradeSteps.length} completed successfully`
          );
          onProgress?.();

          if (isLastStep) {
            console.log(
              `🎉 Sequential tier upgrade completed! Reached target: ${targetTier}`
            );
            onComplete?.();
          } else {
            console.log(
              `⏭️ Continuing to next step: ${upgradeSteps[i + 1].toTier}`
            );
          }
        }
      });

      // Brief pause between steps to ensure proper session cleanup
      if (!isLastStep) {
        console.log("⏸️ Pausing briefly between upgrade steps...");
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }
  } catch (error) {
    console.error("❌ Sequential tier upgrade failed:", error);
    throw error;
  }
};
