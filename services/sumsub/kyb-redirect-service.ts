import { getCurrentUser } from "@/services/user/user-service";
import { SUMSUB_LEVEL } from "@/types/auth";
import { getCurrentToken } from "@/utils/token-utils";
import { getTierName } from "@/utils/tier-number";
import * as Linking from "expo-linking";
import { Alert } from "react-native";

/**
 * Simple KYB service that redirects to web for kybtier1 verification
 * Uses deep linking to return to the app after completion
 */

// Constants
const WEB_BASE_URL = "https://trustnexus.profitecosystem.com";
const DEEP_LINK_SCHEME = "myapp";

// Types
export interface KYBDeepLinkResult {
  tier: string;
  success: boolean;
}

/**
 * Check if tier requires KYB web redirect
 */
export const isKYBTier = (tier: SUMSUB_LEVEL): boolean => {
  return tier === SUMSUB_LEVEL.KYB_TIER1;
};

/**
 * Open KYB verification in web browser with authentication
 */
export const openKYBInBrowser = async (): Promise<void> => {
  try {
    console.log(
      "🏢 Opening KYB verification in browser for tier:",
      SUMSUB_LEVEL.KYB_TIER1
    );

    const currentUser = await getCurrentUser();
    const token = await getCurrentToken();

    if (!currentUser || !token) {
      throw new Error("User not authenticated");
    }

    // Create deep link URL for return
    const deepLinkUrl = `${DEEP_LINK_SCHEME}://kyb-complete?tier=${
      SUMSUB_LEVEL.KYB_TIER1
    }&timestamp=${Date.now()}`;

    // Create web URL with authentication and return URL
    const webUrl =
      `${WEB_BASE_URL}/api/mobile-kyb?` +
      `tier=${SUMSUB_LEVEL.KYB_TIER1}&` +
      `token=${encodeURIComponent(token)}&` +
      `email=${encodeURIComponent(currentUser.email || "")}&` +
      `returnUrl=${encodeURIComponent(deepLinkUrl)}`;

    console.log("🌐 Opening KYB URL:", webUrl);

    // Open in browser
    const canOpen = await Linking.canOpenURL(webUrl);
    if (canOpen) {
      await Linking.openURL(webUrl);
    } else {
      throw new Error("Cannot open web browser");
    }
  } catch (error: any) {
    console.error("❌ Failed to open KYB in browser:", error);
    throw new Error(error.message || "Failed to open KYB verification");
  }
};

/**
 * Handle deep link return from web KYB verification
 */
export const handleKYBDeepLink = (url: string): KYBDeepLinkResult | null => {
  try {
    console.log("🔗 Handling KYB deep link:", url);

    if (!url.includes("kyb-complete")) {
      return null;
    }

    const urlObj = new URL(url);
    const tier = urlObj.searchParams.get("tier");
    const status = urlObj.searchParams.get("status");
    const timestamp = urlObj.searchParams.get("timestamp");

    if (!tier) {
      console.warn("⚠️ No tier found in deep link");
      return null;
    }

    // Check if link is recent (within 1 hour)
    if (timestamp) {
      const linkTime = parseInt(timestamp);
      const now = Date.now();
      const oneHour = 60 * 60 * 1000;

      if (now - linkTime > oneHour) {
        console.warn("⚠️ Deep link is too old, ignoring");
        return null;
      }
    }

    const success = status === "success" || status === "completed";

    console.log("✅ KYB deep link parsed:", { tier, success });

    return { tier, success };
  } catch (error) {
    console.error("❌ Error parsing KYB deep link:", error);
    return null;
  }
};

/**
 * Show completion alert and handle navigation
 */
export const showKYBCompletionAlert = (
  result: KYBDeepLinkResult,
  onComplete?: () => void
): void => {
  if (result.success) {
    // Convert raw tier value to proper display name
    const tierDisplayName = getTierName(result.tier as SUMSUB_LEVEL);

    Alert.alert(
      "KYB Verification Complete",
      `Your business verification for ${tierDisplayName} has been completed successfully.`,
      [
        {
          text: "OK",
          onPress: () => {
            console.log("🎉 KYB verification completed successfully");
            onComplete?.();
          },
        },
      ]
    );
  } else {
    Alert.alert(
      "KYB Verification Failed",
      "Your business verification was not completed. Please try again.",
      [
        {
          text: "OK",
          onPress: () => {
            console.log("❌ KYB verification failed");
            onComplete?.();
          },
        },
      ]
    );
  }
};
