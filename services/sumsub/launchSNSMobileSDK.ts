import { SUMSUB_LEVEL } from "@/types/auth";
import { SumsubStatusChangedEvent } from "@/types/sumsub-sdk";
import SNSMobileSDK, {
  type StatusChangedEvent,
} from "@sumsub/react-native-mobilesdk-module";
import { getSumsubAccessToken, getLivenessCheckToken } from "./sumsub-service";
import { createSumsubTheme } from "./sumsub-theme";
import {
  sdkSessionManager,
  createSessionAwareStatusHandler,
  createSessionAwareErrorHandler,
} from "./sdk-session-manager";

// Import types from our custom type definitions

interface LaunchSNSMobileSDKProps {
  sumSubData: {
    token: string;
    userEmail: string;
  };
  statusChangeHandler: (event: StatusChangedEvent) => void;
  tier: SUMSUB_LEVEL; // Add tier parameter to determine KYB vs KYC
}

export let launchSNSMobileSDK = async ({
  sumSubData,
  statusChangeHandler,
  tier,
}: LaunchSNSMobileSDKProps) => {
  // Check if we can start a new session
  const sessionCheck = sdkSessionManager.canStartSession(tier);
  if (!sessionCheck.canStart) {
    console.error("❌ Cannot start SDK session:", sessionCheck.reason);
    throw new Error(`SDK session conflict: ${sessionCheck.reason}`);
  }

  try {
    // Start session management
    await sdkSessionManager.startSession(tier);

    console.log(`🚀 Launching Sumsub SDK for tier: ${tier}`);
    console.log("📊 Session status:", sdkSessionManager.getSessionStatus());

    // Token expiration handler - refreshes token when it expires
    const tokenExpirationHandler = async (): Promise<string> => {
      try {
        console.log("🔄 Refreshing Sumsub token...");
        // Use appropriate token service based on tier
        const tokenData = await getSumsubAccessToken({ tierEnum: tier });
        console.log("✅ Token refreshed successfully");
        return tokenData.token;
      } catch (error) {
        console.error("❌ Failed to refresh Sumsub token:", error);
        throw error;
      }
    };

    // Create theme configuration matching VerifyMe design system
    const theme = createSumsubTheme();
    console.log("🎨 Applying VerifyMe theme to Sumsub SDK");

    // Create session-aware handlers
    const sessionAwareStatusHandler = createSessionAwareStatusHandler(
      statusChangeHandler,
      tier
    );
    const sessionAwareErrorHandler = createSessionAwareErrorHandler(tier);

    let snsMobileSDK = SNSMobileSDK.init(
      sumSubData.token,
      tokenExpirationHandler
    )
      .withHandlers({
        onStatusChanged: sessionAwareStatusHandler,
        onError: sessionAwareErrorHandler,
      })
      .withDebug(true)
      .withApplicantConf({
        email: sumSubData.userEmail,
      })
      .withTheme(theme) // Apply TrustNexus theme for visual consistency
      .withLocale("en") // Optional, for cases when you need to override the system locale
      .build();

    snsMobileSDK.launch();
    console.log(`✅ SDK launched successfully for tier: ${tier}`);
  } catch (error) {
    console.error(`❌ Failed to launch SDK for tier ${tier}:`, error);
    // Clean up session on error
    await sdkSessionManager.endSession();
    throw error;
  }
};

export const runSumsubProcess = async ({
  tier,
  userEmail,
  changeHandler,
}: {
  tier: SUMSUB_LEVEL;
  userEmail: string;
  changeHandler: (event: SumsubStatusChangedEvent) => void;
}) => {
  try {
    console.log(`🔄 Getting Sumsub token for tier: ${tier}...`);
    const tokenData = await getSumsubAccessToken({ tierEnum: tier });

    console.log(`✅ Sumsub token received ${tier}: ${tokenData}`);

    console.log(`🚀 Launching Sumsub SDK for tier: ${tier}...`);
    await launchSNSMobileSDK({
      sumSubData: {
        token: tokenData.token,
        userEmail: userEmail,
      },
      tier: tier,
      statusChangeHandler: changeHandler,
    });
  } catch (error) {
    console.error(`❌ Error during Sumsub process for tier ${tier}:`, error);
  }
};

/**
 * Run liveness check process with automatic user type detection
 * Uses the appropriate token request method based on user type (individual vs company with beneficiary)
 * Throws error if token cannot be obtained (for fallback handling)
 */
export const runLivenessCheckProcess = async ({
  userEmail,
  changeHandler,
}: {
  userEmail: string;
  changeHandler: (event: SumsubStatusChangedEvent) => void;
}) => {
  console.log(`🔄 Getting liveness check token with user type detection...`);
  const tokenData = await getLivenessCheckToken();

  if (!tokenData || !tokenData.token) {
    console.error(`❌ Failed to get liveness check token`);
    throw new Error("Liveness check token not available");
  }

  console.log(`✅ Liveness check token received: SUCCESS`);

  console.log(`🚀 Launching Sumsub SDK for liveness check...`);
  await launchSNSMobileSDK({
    sumSubData: {
      token: tokenData.token,
      userEmail: userEmail,
    },
    tier: SUMSUB_LEVEL.LIVE,
    statusChangeHandler: changeHandler,
  });
};
