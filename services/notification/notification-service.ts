import { Notification, NotificationType } from "@/types/notification";
import { api } from "@/constants/api";
import { getCurrentUserId } from "@/utils/token-utils";

/**
 * Map backend notification type to frontend NotificationType enum
 */
const mapBackendTypeToFrontendType = (
  backendType: string
): NotificationType => {
  const type = backendType?.toLowerCase();

  switch (type) {
    case "interview":
      return NotificationType.INTERVIEW;
    case "document_approved":
    case "documentapproved":
      return NotificationType.DOCUMENT_APPROVED;
    case "document_rejected":
    case "documentrejected":
      return NotificationType.DOCUMENT_REJECTED;
    case "document_pending":
    case "documentpending":
      return NotificationType.DOCUMENT_PENDING;
    case "company_consent":
    case "companyconsent":
      return NotificationType.COMPANY_CONSENT;
    case "tier_upgrade":
    case "tierupgrade":
      return NotificationType.TIER_UPGRADE;
    case "system":
      return NotificationType.SYSTEM;
    case "warning":
      return NotificationType.WARNING;
    default:
      return NotificationType.SYSTEM;
  }
};

/**
 * Get all notifications for the current user
 */
export const getUserNotifications = async (): Promise<Notification[]> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      console.log("No user ID available, returning empty notifications");
      return [];
    }

    const response = await api.get<{ isSuccess: boolean; data: any[] }>(
      `/Notifications/user/${userId}`
    );

    if (!response.isSuccess) {
      console.error("API returned unsuccessful response");
      return [];
    }

    // Transform backend notification format to frontend format
    const notifications: Notification[] = (response.data || []).map(
      (item: any) => ({
        id: item.id?.toString() || Date.now().toString(),
        title: item.notificationType?.name || "Notification",
        message: item.message || "",
        type: mapBackendTypeToFrontendType(item.notificationType?.name),
        read: false, // Backend doesn't seem to have read status yet
        createdAt: item.createdAt || new Date().toISOString(),
        data: {
          applicantId: item.applicantId,
          userId: item.userId,
        },
      })
    );

    return notifications;
  } catch (error) {
    console.error("Error fetching notifications:", error);
    // Return empty array instead of throwing to prevent app crashes
    return [];
  }
};

/**
 * Mark a notification as read
 * Note: This endpoint may not exist yet on backend
 */
export const markNotificationAsRead = async (
  notificationId: string
): Promise<boolean> => {
  try {
    await api.patch(`/Notifications/${notificationId}/read`);
    return true;
  } catch (error) {
    console.error("Error marking notification as read:", error);
    // For now, just return true since backend might not have this endpoint
    return true;
  }
};

/**
 * Mark all notifications as read
 * Note: This endpoint may not exist yet on backend
 */
export const markAllNotificationsAsRead = async (): Promise<boolean> => {
  try {
    await api.patch("/Notifications/read-all");
    return true;
  } catch (error) {
    console.error("Error marking all notifications as read:", error);
    // For now, just return true since backend might not have this endpoint
    return true;
  }
};

/**
 * Delete a notification
 * Note: This endpoint may not exist yet on backend
 */
export const deleteNotification = async (
  notificationId: string
): Promise<boolean> => {
  try {
    await api.delete(`/Notifications/${notificationId}`);
    return true;
  } catch (error) {
    console.error("Error deleting notification:", error);
    // For now, just return true since backend might not have this endpoint
    return true;
  }
};

/**
 * Create a new notification
 * This would typically be called by the backend, but can be used for testing
 * Uses the same endpoint as interview notifications
 */
export const createNotification = async (
  notification: Omit<Notification, "id">
): Promise<Notification> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      throw new Error("User ID not available");
    }

    // Use the same format as interview notifications
    const response = await api.post<{ isSuccess: boolean; data: boolean }>(
      "/Notifications/send",
      {
        userId: userId,
        message: notification.message,
        title: notification.title,
        type: notification.type,
      }
    );

    if (!response.isSuccess) {
      throw new Error("Failed to send notification");
    }

    // Return a mock notification since backend doesn't return the created notification
    return {
      ...notification,
      id: Date.now().toString(),
    };
  } catch (error) {
    console.error("Error creating notification:", error);
    throw new Error("Failed to create notification");
  }
};

/**
 * Get notification icon based on type
 */
export const getNotificationIcon = (type: NotificationType): string => {
  switch (type) {
    case NotificationType.DOCUMENT_APPROVED:
      return "checkmark-circle";
    case NotificationType.DOCUMENT_REJECTED:
      return "close-circle";
    case NotificationType.DOCUMENT_PENDING:
      return "time";
    case NotificationType.COMPANY_CONSENT:
      return "business";
    case NotificationType.TIER_UPGRADE:
      return "trending-up";
    case NotificationType.INTERVIEW:
      return "videocam";
    case NotificationType.SYSTEM:
      return "settings";
    case NotificationType.WARNING:
      return "warning";
    default:
      return "notifications";
  }
};

/**
 * Get notification color based on type
 */
export const getNotificationColor = (type: NotificationType): string => {
  switch (type) {
    case NotificationType.DOCUMENT_APPROVED:
      return "#10B981"; // green
    case NotificationType.DOCUMENT_REJECTED:
      return "#EF4444"; // red
    case NotificationType.DOCUMENT_PENDING:
      return "#F59E0B"; // amber
    case NotificationType.COMPANY_CONSENT:
      return "#3B82F6"; // blue
    case NotificationType.TIER_UPGRADE:
      return "#8B5CF6"; // purple
    case NotificationType.INTERVIEW:
      return "#1C3F3C"; // Trust Nexus primary color
    case NotificationType.SYSTEM:
      return "#6B7280"; // gray
    case NotificationType.WARNING:
      return "#F59E0B"; // amber
    default:
      return "#6B7280"; // gray
  }
};
