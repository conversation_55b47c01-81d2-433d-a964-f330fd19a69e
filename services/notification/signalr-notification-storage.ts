import AsyncStorage from "@react-native-async-storage/async-storage";
import { InterviewNotification } from "@/types/interview-notification";

const SIGNALR_NOTIFICATIONS_KEY = "signalr_notifications";
const MAX_STORED_NOTIFICATIONS = 100; // Limit to prevent storage bloat

export interface StoredSignalRNotification
  extends Omit<InterviewNotification, "id"> {
  id: string; // Local storage ID (different from API ID)
  apiId?: number; // API notification ID for accept/reject operations
  userId: string;
  received: boolean; // Track if this was received via SignalR
}

/**
 * Store a SignalR notification locally for history
 */
export const storeSignalRNotification = async (
  notification: InterviewNotification,
  userId: string
): Promise<void> => {
  try {
    const storedNotification: StoredSignalRNotification = {
      ...notification,
      id: `signalr-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`,
      userId,
      received: true,
    };

    const existingNotifications = await getStoredSignalRNotifications(userId);
    const updatedNotifications = [
      storedNotification,
      ...existingNotifications,
    ].slice(0, MAX_STORED_NOTIFICATIONS); // Keep only the most recent notifications

    await AsyncStorage.setItem(
      `${SIGNALR_NOTIFICATIONS_KEY}_${userId}`,
      JSON.stringify(updatedNotifications)
    );

    console.log(
      "📱 SignalR notification stored locally:",
      storedNotification.id
    );
  } catch (error) {
    console.error("❌ Error storing SignalR notification:", error);
  }
};

/**
 * Get all stored SignalR notifications for a user
 */
export const getStoredSignalRNotifications = async (
  userId: string
): Promise<StoredSignalRNotification[]> => {
  try {
    const stored = await AsyncStorage.getItem(
      `${SIGNALR_NOTIFICATIONS_KEY}_${userId}`
    );
    if (!stored) {
      return [];
    }

    const notifications: StoredSignalRNotification[] = JSON.parse(stored);

    // Convert timestamp strings back to Date objects
    return notifications.map((notif) => ({
      ...notif,
      timestamp: new Date(notif.timestamp),
    }));
  } catch (error) {
    console.error("❌ Error retrieving stored SignalR notifications:", error);
    return [];
  }
};

/**
 * Clear all stored SignalR notifications for a user
 */
export const clearStoredSignalRNotifications = async (
  userId: string
): Promise<void> => {
  try {
    await AsyncStorage.removeItem(`${SIGNALR_NOTIFICATIONS_KEY}_${userId}`);
    console.log("🗑️ Cleared stored SignalR notifications for user:", userId);
  } catch (error) {
    console.error("❌ Error clearing stored SignalR notifications:", error);
  }
};

/**
 * Remove old notifications (older than specified days)
 */
export const cleanupOldSignalRNotifications = async (
  userId: string,
  daysToKeep: number = 30
): Promise<void> => {
  try {
    const notifications = await getStoredSignalRNotifications(userId);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const filteredNotifications = notifications.filter(
      (notif) => new Date(notif.timestamp) > cutoffDate
    );

    await AsyncStorage.setItem(
      `${SIGNALR_NOTIFICATIONS_KEY}_${userId}`,
      JSON.stringify(filteredNotifications)
    );

    console.log(
      `🧹 Cleaned up old SignalR notifications, kept ${filteredNotifications.length} of ${notifications.length}`
    );
  } catch (error) {
    console.error("❌ Error cleaning up old SignalR notifications:", error);
  }
};
