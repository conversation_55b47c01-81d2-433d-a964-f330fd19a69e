import { Notification } from "@/types/notification";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { QUERY_KEYS } from "../query-keys";
import {
  createNotification,
  deleteNotification,
  getUserNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
} from "./notification-service";
import { useSession } from "@/providers/auth/auth-provider";

/**
 * Helper function to sort notifications by creation time (newest first)
 */
const sortNotifications = (notifications: Notification[]): Notification[] => {
  return [...notifications].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

/**
 * Hook to fetch user notifications
 * Only fetches when user is authenticated
 */
export const useNotifications = () => {
  const { token } = useSession();
  const isAuthenticated = !!token;

  return useQuery({
    queryKey: [QUERY_KEYS.NOTIFICATIONS],
    queryFn: getUserNotifications,
    enabled: isAuthenticated, // Only fetch when authenticated
    staleTime: 1000 * 60 * 5, // 5 minutes (reduced for better sorting)
    refetchOnWindowFocus: true,
    refetchInterval: isAuthenticated ? 1000 * 60 * 5 : false, // Only auto refetch when authenticated
  });
};

/**
 * Hook to mark a notification as read
 */
export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markNotificationAsRead,
    onSuccess: (success, notificationId) => {
      if (success) {
        // Update the notifications cache and maintain sorting
        queryClient.setQueryData(
          [QUERY_KEYS.NOTIFICATIONS],
          (oldData: Notification[] | undefined) => {
            if (!oldData) return oldData;

            const updatedData = oldData.map((notification) =>
              notification.id === notificationId
                ? { ...notification, read: true }
                : notification
            );

            return sortNotifications(updatedData);
          }
        );
      }
    },
    onError: (error) => {
      console.error("Error marking notification as read:", error);
    },
  });
};

/**
 * Hook to mark all notifications as read
 */
export const useMarkAllNotificationsAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markAllNotificationsAsRead,
    onSuccess: (success) => {
      if (success) {
        // Update the notifications cache and maintain sorting
        queryClient.setQueryData(
          [QUERY_KEYS.NOTIFICATIONS],
          (oldData: Notification[] | undefined) => {
            if (!oldData) return oldData;

            const updatedData = oldData.map((notification) => ({
              ...notification,
              read: true,
            }));

            return sortNotifications(updatedData);
          }
        );
      }
    },
    onError: (error) => {
      console.error("Error marking all notifications as read:", error);
    },
  });
};

/**
 * Hook to delete a notification
 */
export const useDeleteNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteNotification,
    onSuccess: (success, notificationId) => {
      if (success) {
        // Remove the notification from cache and maintain sorting
        queryClient.setQueryData(
          [QUERY_KEYS.NOTIFICATIONS],
          (oldData: Notification[] | undefined) => {
            if (!oldData) return oldData;

            const filteredData = oldData.filter(
              (notification) => notification.id !== notificationId
            );

            return sortNotifications(filteredData);
          }
        );
      }
    },
    onError: (error) => {
      console.error("Error deleting notification:", error);
    },
  });
};

/**
 * Hook to create a new notification (for testing)
 */
export const useCreateNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createNotification,
    onSuccess: (newNotification) => {
      // Add the new notification to cache and maintain sorting
      queryClient.setQueryData(
        [QUERY_KEYS.NOTIFICATIONS],
        (oldData: Notification[] | undefined) => {
          if (!oldData) return [newNotification];

          const updatedData = [newNotification, ...oldData];
          return sortNotifications(updatedData);
        }
      );
    },
    onError: (error) => {
      console.error("Error creating notification:", error);
    },
  });
};
