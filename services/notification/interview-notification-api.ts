import { api } from "@/constants/api";
import {
  NotificationDto,
  NotificationApiResponse,
  SendNotificationRequest,
  BooleanApiResponse,
} from "@/types/interview-notification";

/**
 * Get all notifications for a specific user
 * Used for polling notifications when user is not logged in via SignalR
 */
export const getUserNotifications = async (
  userId: string
): Promise<NotificationDto[]> => {
  try {
    console.log(`📥 Fetching notifications for user: ${userId}`);

    const response = await api.get<NotificationApiResponse>(
      `/Notifications/user/${userId}`
    );

    if (!response.isSuccess) {
      throw new Error("Failed to fetch notifications");
    }

    return response.data || [];
  } catch (error) {
    console.error("❌ Error fetching user notifications:", error);
    throw error;
  }
};

/**
 * Send a notification to a specific user
 * Used for testing purposes via Swagger
 */
export const sendNotification = async (
  request: SendNotificationRequest
): Promise<boolean> => {
  try {
    console.log("📤 Sending notification:", request);

    const response = await api.post<BooleanApiResponse>(
      "/Notifications/send",
      request
    );

    if (!response.isSuccess) {
      throw new Error("Failed to send notification");
    }

    return response.data;
  } catch (error) {
    console.error("❌ Error sending notification:", error);
    throw error;
  }
};

/**
 * Check if user has completed interview
 * This checks the userInfo.interviewCompletedDate field
 */
export const hasUserCompletedInterview = (userInfo: any): boolean => {
  return !!userInfo?.interviewCompletedDate;
};
