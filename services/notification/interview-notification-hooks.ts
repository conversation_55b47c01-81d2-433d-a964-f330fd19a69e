import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getUserNotifications,
  sendNotification,
} from "./interview-notification-api";
import {
  NotificationDto,
  SendNotificationRequest,
} from "@/types/interview-notification";
import { useSession } from "@/providers/auth/auth-provider";

// Query keys for React Query
export const INTERVIEW_NOTIFICATION_QUERY_KEYS = {
  USER_NOTIFICATIONS: "user-notifications",
} as const;

/**
 * Hook to get user notifications with polling
 * Used for non-logged-in users or as fallback
 * Only polls when user is authenticated or when explicitly enabled for non-authenticated users
 */
export const useGetUserNotifications = (
  userId: string | null,
  enabled: boolean = true
) => {
  const { token } = useSession();
  const isAuthenticated = !!token;

  // For authenticated users, always respect the enabled flag
  // For non-authenticated users, only enable if explicitly requested AND userId exists
  const shouldEnable = isAuthenticated
    ? !!userId && enabled
    : !!userId && enabled;

  return useQuery({
    queryKey: [INTERVIEW_NOTIFICATION_QUERY_KEYS.USER_NOTIFICATIONS, userId],
    queryFn: async (): Promise<NotificationDto[]> => {
      if (!userId) {
        throw new Error("User ID is required");
      }
      return getUserNotifications(userId);
    },
    enabled: shouldEnable,
    refetchInterval: shouldEnable ? 30000 : false, // Only poll when enabled
    staleTime: 25000, // Consider data stale after 25 seconds
  });
};

/**
 * Hook to get only interview notifications for a user
 */
export const useGetInterviewNotifications = (
  userId: string | null,
  enabled: boolean = true
) => {
  const { data: allNotifications, ...rest } = useGetUserNotifications(
    userId,
    enabled
  );

  // Filter for interview notifications and get most recent
  const interviewNotifications = allNotifications
    ? allNotifications.filter(
        (notification) =>
          notification.notificationType.name.toLowerCase() === "interview"
      )
    : [];

  const mostRecentInterview =
    interviewNotifications.length > 0
      ? interviewNotifications.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )[0]
      : null;

  return {
    ...rest,
    data: interviewNotifications,
    mostRecentNotification: mostRecentInterview,
  };
};

/**
 * Hook to send a notification (for testing purposes)
 */
export const useSendNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: SendNotificationRequest) => sendNotification(request),
    onSuccess: (_, variables) => {
      // Invalidate and refetch user notifications for the target user
      queryClient.invalidateQueries({
        queryKey: [
          INTERVIEW_NOTIFICATION_QUERY_KEYS.USER_NOTIFICATIONS,
          variables.userId,
        ],
      });
      console.log("✅ Notification sent successfully");
    },
    onError: (error) => {
      console.error("❌ Failed to send notification:", error);
    },
  });
};
