import {
  <PERSON><PERSON><PERSON>onnection,
  Hub<PERSON>on<PERSON><PERSON><PERSON><PERSON>er,
  LogLevel,
} from "@microsoft/signalr";
import { InterviewNotification } from "@/types/interview-notification";

export class SignalRNotificationService {
  private static instance: SignalRNotificationService;
  private connection: HubConnection | null = null;
  private handlers: Map<string, (data: any) => void> = new Map();
  private userId: string | null = null;

  private constructor() {}

  public static getInstance(): SignalRNotificationService {
    if (!SignalRNotificationService.instance) {
      SignalRNotificationService.instance = new SignalRNotificationService();
    }
    return SignalRNotificationService.instance;
  }

  public async startConnection(baseUrl: string, userId: string): Promise<void> {
    if (this.connection) {
      await this.stopConnection();
    }

    this.userId = userId;

    this.connection = new HubConnectionBuilder()
      .withUrl(`${baseUrl}/notificationHub`)
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Information)
      .build();

    this.connection.on("ReceiveNotification", (notification) => {
      const handler = this.handlers.get("notification");
      if (handler) {
        // Check if it's an interview notification - handle both API formats
        const isInterviewNotification =
          notification.notificationType?.name?.toLowerCase() === "interview" ||
          notification.type?.toLowerCase() === "interview";

        if (isInterviewNotification) {
          // Transform notification to match mobile format
          const mobileNotification: InterviewNotification = {
            id: notification.id,
            message: notification.message || "Interview request received",
            title: notification.title || "Interview Request",
            type: "interview",
            timestamp: new Date(notification.timestamp || Date.now()),
          };
          handler(mobileNotification);
        }
      }
    });

    try {
      await this.connection.start();
      console.log("✅ SignalR Connected");

      // Join user group for this user
      if (this.connection.state === "Connected") {
        await this.connection.invoke("JoinUserGroup", userId);
        console.log(`👥 Joined user group for userId: ${userId}`);
      }
    } catch (err) {
      console.error("❌ SignalR Connection Error: ", err);
      throw err;
    }
  }

  public async stopConnection(): Promise<void> {
    if (this.connection) {
      try {
        // Only try to leave the user group if connection is in Connected state
        if (this.userId && this.connection.state === "Connected") {
          await this.connection.invoke("LeaveUserGroup", this.userId);
          console.log(`👋 Left user group for userId: ${this.userId}`);
        }
        await this.connection.stop();
        console.log("🔌 SignalR Disconnected");
      } catch (err) {
        console.error("❌ SignalR Disconnection Error: ", err);
      } finally {
        // Clean up references regardless of connection state
        this.connection = null;
        this.userId = null;
      }
    }
  }

  public onNotification(
    callback: (notification: InterviewNotification) => void
  ): void {
    this.handlers.set("notification", callback);
  }

  public removeNotificationHandler(): void {
    this.handlers.delete("notification");
  }

  public isConnected(): boolean {
    return this.connection?.state === "Connected";
  }
}

export default SignalRNotificationService.getInstance();
