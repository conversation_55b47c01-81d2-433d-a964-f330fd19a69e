import { Document } from "@/types/document";

/**
 * Document type enumeration for different file formats
 */
export enum DocumentType {
  PDF = "pdf",
  IMAGE = "image",
  UNKNOWN = "unknown",
}

/**
 * Supported image file extensions
 */
const IMAGE_EXTENSIONS = [
  ".jpg",
  ".jpeg",
  ".png",
  ".gif",
  ".bmp",
  ".webp",
  ".svg",
  ".tiff",
  ".tif",
];

/**
 * Supported PDF file extensions
 */
const PDF_EXTENSIONS = [".pdf"];

// MIME types are prepared for future use if needed
// const IMAGE_MIME_TYPES = ["image/jpeg", "image/png", "image/gif", "image/bmp", "image/webp"];
// const PDF_MIME_TYPES = ["application/pdf"];

/**
 * Determines the document type based on file name and other properties
 * @param document - The document object to analyze
 * @returns DocumentType enum value
 */
export const getDocumentType = (document: Document): DocumentType => {
  if (!document) return DocumentType.UNKNOWN;

  const fileName = document.fileName?.toLowerCase() || "";

  // Check file extension first - this is the most reliable method
  const fileExtension = getFileExtension(fileName);

  if (PDF_EXTENSIONS.includes(fileExtension)) {
    return DocumentType.PDF;
  }

  if (IMAGE_EXTENSIONS.includes(fileExtension)) {
    return DocumentType.IMAGE;
  }

  // Check document type from Sumsub classification
  const docType = document.idDocDefIdDocType?.toLowerCase() || "";

  // Check for PDF in document type
  if (docType.includes("pdf") || docType.includes("attachment")) {
    return DocumentType.PDF;
  }

  // Some document types are typically images
  const imageDocTypes = ["selfie", "passport", "id_card", "driver_license"];
  if (imageDocTypes.some((type) => docType.includes(type))) {
    return DocumentType.IMAGE;
  }

  // If no clear extension but has thumbnail, likely an image
  // (moved this check after docType check to avoid PDF misclassification)
  if (document.fileS3PathThumbnail && !docType.includes("pdf")) {
    return DocumentType.IMAGE;
  }

  return DocumentType.UNKNOWN;
};

/**
 * Checks if a document is a PDF file
 * @param document - The document to check
 * @returns true if the document is a PDF
 */
export const isPDFDocument = (document: Document): boolean => {
  return getDocumentType(document) === DocumentType.PDF;
};

/**
 * Checks if a document is an image file
 * @param document - The document to check
 * @returns true if the document is an image
 */
export const isImageDocument = (document: Document): boolean => {
  return getDocumentType(document) === DocumentType.IMAGE;
};

/**
 * Extracts file extension from filename
 * @param fileName - The filename to extract extension from
 * @returns file extension including the dot (e.g., ".pdf")
 */
export const getFileExtension = (fileName: string): string => {
  if (!fileName) return "";

  const lastDotIndex = fileName.lastIndexOf(".");
  if (lastDotIndex === -1) return "";

  return fileName.substring(lastDotIndex).toLowerCase();
};

/**
 * Gets a human-readable file type description
 * @param document - The document to get type for
 * @returns human-readable file type string
 */
export const getFileTypeDescription = (document: Document): string => {
  const documentType = getDocumentType(document);

  switch (documentType) {
    case DocumentType.PDF:
      return "PDF Document";
    case DocumentType.IMAGE:
      return "Image";
    default:
      return document.idDocDefIdDocType || "Document";
  }
};

/**
 * Determines if a document can be previewed
 * @param document - The document to check
 * @returns true if the document can be previewed
 */
export const canPreviewDocument = (document: Document): boolean => {
  const documentType = getDocumentType(document);
  return (
    documentType === DocumentType.PDF || documentType === DocumentType.IMAGE
  );
};

/**
 * Gets the appropriate icon name for a document type
 * @param document - The document to get icon for
 * @returns Ionicons icon name
 */
export const getDocumentIcon = (document: Document): string => {
  const documentType = getDocumentType(document);

  switch (documentType) {
    case DocumentType.PDF:
      return "document-text-outline";
    case DocumentType.IMAGE:
      return "image-outline";
    default:
      return "document-outline";
  }
};

/**
 * Validates if a document has the required properties for preview
 * @param document - The document to validate
 * @returns validation result with error message if invalid
 */
export const validateDocumentForPreview = (
  document: Document
): { isValid: boolean; error?: string } => {
  if (!document) {
    return { isValid: false, error: "Document not found" };
  }

  if (!document.fileS3Path) {
    return { isValid: false, error: "Document file path not available" };
  }

  if (!canPreviewDocument(document)) {
    return { isValid: false, error: "Document type not supported for preview" };
  }

  return { isValid: true };
};

/**
 * Filters documents by type
 * @param documents - Array of documents to filter
 * @param type - Document type to filter by
 * @returns filtered array of documents
 */
export const filterDocumentsByType = (
  documents: Document[],
  type: DocumentType
): Document[] => {
  return documents.filter((doc) => getDocumentType(doc) === type);
};

/**
 * Groups documents by their type
 * @param documents - Array of documents to group
 * @returns object with documents grouped by type
 */
export const groupDocumentsByType = (
  documents: Document[]
): Record<DocumentType, Document[]> => {
  const grouped: Record<DocumentType, Document[]> = {
    [DocumentType.PDF]: [],
    [DocumentType.IMAGE]: [],
    [DocumentType.UNKNOWN]: [],
  };

  documents.forEach((doc) => {
    const type = getDocumentType(doc);
    grouped[type].push(doc);
  });

  return grouped;
};
