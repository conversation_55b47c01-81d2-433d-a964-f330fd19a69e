import { SUMSUB_LEVEL } from "@/types/auth";

// Map tier numbers to tier names (same as NextJS app)
export const TIER_NAMES: Record<number, string> = {
  0: "Entry",
  1: "Basic",
  2: "Standard",
  3: "Pro",
};

export const getTierNumber = (state?: SUMSUB_LEVEL) => {
  if (!state) return 0;
  return parseInt(state.replace(/[^\d]/g, "")) || 0;
};

/**
 * Convert SUMSUB_LEVEL to tier name
 * @param state - SUMSUB_LEVEL enum value
 * @returns Tier name (Entry, Basic, Standard, Pro) or fallback format
 */
export const getTierName = (state?: SUMSUB_LEVEL): string => {
  const tierNumber = getTierNumber(state);
  return TIER_NAMES[tierNumber] || `Tier #${tierNumber}`;
};

/**
 * Get tier display name from tier number
 * @param tier - Tier number (0, 1, 2, 3)
 * @returns Tier name (Entry, Basic, Standard, Pro)
 */
export const getTierDisplayName = (tier?: number): string => {
  if (tier === undefined || tier < 0) return TIER_NAMES[0]; // Default to "Entry"
  return TIER_NAMES[tier] || `Tier #${tier}`;
};

/**
 * Get tier badge props for consistent styling
 * @param tier - Tier number
 * @returns Object with tier name only
 */
export const getTierBadgeProps = (tier?: number) => {
  const displayName = getTierDisplayName(tier);

  return {
    displayName,
  };
};

/**
 * Get the next tier from current tier string
 * @param currentTier - Current tier string (e.g., "tier1", "kybtier2")
 * @returns Next tier string or null if no next tier
 */
export const getNextTier = (currentTier: string): string | null => {
  const match = currentTier.match(/(.*?)(\d+)$/);
  if (!match) return null;

  const prefix = match[1];
  const number = parseInt(match[2], 10);
  const nextNumber = number + 1;

  return `${prefix}${nextNumber}`;
};

/**
 * Helper function to extract tier number from tier string
 * @param tierString - Tier string (e.g., "tier1", "kybtier2")
 * @returns Tier number
 */
export const extractTierNumber = (tierString: string): number => {
  if (!tierString) return 0;
  const match = tierString.match(/(\d+)$/);
  return match ? parseInt(match[1], 10) : 0;
};

/**
 * Helper function to determine if user needs multi-step upgrade to reach target
 * @param currentTier - Current tier string
 * @param targetTier - Target tier string
 * @returns Boolean indicating if multi-step upgrade is needed
 */
export const needsMultiStepUpgrade = (
  currentTier: string,
  targetTier: string
): boolean => {
  const currentTierNum = extractTierNumber(currentTier);
  const targetTierNum = extractTierNumber(targetTier);
  return targetTierNum - currentTierNum > 1;
};

/**
 * Validates if a tier upgrade is allowed based on sequential progression rules
 * Users must complete each tier sequentially (tier1 → tier2 → tier3)
 * @param currentTier - Current user tier string (e.g., "tier1", "kybtier2")
 * @param targetTier - Target tier string to upgrade to
 * @returns Object with validation result and error message if invalid
 */
export const validateTierUpgrade = (
  currentTier: string,
  targetTier: string
): { isValid: boolean; error?: string; allowedTier?: string } => {
  const currentTierNum = extractTierNumber(currentTier);
  const targetTierNum = extractTierNumber(targetTier);

  // Extract prefix (tier, kybtier, etc.)
  const currentMatch = currentTier.match(/(.*?)(\d+)$/);
  const targetMatch = targetTier.match(/(.*?)(\d+)$/);

  if (!currentMatch || !targetMatch) {
    return {
      isValid: false,
      error: "Invalid tier format",
    };
  }

  const currentPrefix = currentMatch[1];
  const targetPrefix = targetMatch[1];

  // Ensure same tier type (KYC vs KYB)
  if (currentPrefix !== targetPrefix) {
    return {
      isValid: false,
      error: "Cannot upgrade between different tier types (KYC/KYB)",
    };
  }

  // Check if trying to skip tiers
  if (targetTierNum - currentTierNum > 1) {
    const nextTierNum = currentTierNum + 1;
    const allowedTier = `${currentPrefix}${nextTierNum}`;
    return {
      isValid: false,
      error: `Sequential tier progression required. You must upgrade to ${TIER_NAMES[nextTierNum]} (${allowedTier}) before accessing ${TIER_NAMES[targetTierNum]}`,
      allowedTier,
    };
  }

  // Check if trying to downgrade
  if (targetTierNum <= currentTierNum) {
    return {
      isValid: false,
      error: "Cannot downgrade or upgrade to the same tier",
    };
  }

  // Valid sequential upgrade
  return { isValid: true };
};

/**
 * Gets the next allowed tier for upgrade based on current tier
 * @param currentTier - Current user tier string
 * @returns Next tier string or null if at maximum tier
 */
export const getNextAllowedTier = (currentTier: string): string | null => {
  const match = currentTier.match(/(.*?)(\d+)$/);
  if (!match) return null;

  const prefix = match[1];
  const number = parseInt(match[2], 10);

  // Check if already at maximum tier (tier 3)
  if (number >= 3) return null;

  const nextNumber = number + 1;
  return `${prefix}${nextNumber}`;
};

/**
 * Determines if user can upgrade to a specific company's tier requirement
 * @param currentUserTier - Current user tier string
 * @param companyRequiredTier - Company's required tier string
 * @returns Object indicating if upgrade is possible and what tier to upgrade to
 */
export const canUpgradeToCompanyTier = (
  currentUserTier: string,
  companyRequiredTier: string
): {
  canUpgrade: boolean;
  nextTier?: string;
  isDirectUpgrade: boolean;
  requiresMultipleSteps: boolean;
} => {
  const currentTierNum = extractTierNumber(currentUserTier);
  const requiredTierNum = extractTierNumber(companyRequiredTier);

  // User already meets or exceeds requirement
  if (currentTierNum >= requiredTierNum) {
    return {
      canUpgrade: false,
      isDirectUpgrade: false,
      requiresMultipleSteps: false,
    };
  }

  const nextAllowedTier = getNextAllowedTier(currentUserTier);
  if (!nextAllowedTier) {
    return {
      canUpgrade: false,
      isDirectUpgrade: false,
      requiresMultipleSteps: false,
    };
  }

  const nextTierNum = extractTierNumber(nextAllowedTier);
  const isDirectUpgrade = nextTierNum === requiredTierNum;
  const requiresMultipleSteps = requiredTierNum - currentTierNum > 1;

  return {
    canUpgrade: true,
    nextTier: nextAllowedTier,
    isDirectUpgrade,
    requiresMultipleSteps,
  };
};
