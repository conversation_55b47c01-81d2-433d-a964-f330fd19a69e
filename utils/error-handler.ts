/**
 * Comprehensive error handling utility for the Trust Nexus app
 * Provides consistent error handling across the application
 */

export enum ErrorType {
  NETWORK = "NETWORK",
  AUTHENTICATION = "AUTHENTICATION",
  VALIDATION = "VALIDATION",
  SERVER = "SERVER",
  UNKNOWN = "UNKNOWN",
  TIMEOUT = "TIMEOUT",
}

export interface AppError {
  type: ErrorType;
  message: string;
  originalError?: any;
  code?: string | number;
  retryable: boolean;
  userFriendlyMessage: string;
}

/**
 * Creates a standardized error object
 */
export const createAppError = (
  type: ErrorType,
  message: string,
  originalError?: any,
  code?: string | number
): AppError => {
  const retryable = [
    ErrorType.NETWORK,
    ErrorType.TIMEOUT,
    ErrorType.SERVER,
  ].includes(type);

  let userFriendlyMessage: string;

  switch (type) {
    case ErrorType.NETWORK:
      userFriendlyMessage =
        "Network connection failed. Please check your internet connection and try again.";
      break;
    case ErrorType.TIMEOUT:
      userFriendlyMessage = "Request timed out. Please try again.";
      break;
    case ErrorType.AUTHENTICATION:
      userFriendlyMessage = "Authentication failed. Please log in again.";
      break;
    case ErrorType.SERVER:
      userFriendlyMessage = "Server error occurred. Please try again later.";
      break;
    case ErrorType.VALIDATION:
      userFriendlyMessage = message; // Validation messages are usually user-friendly
      break;
    default:
      userFriendlyMessage = "An unexpected error occurred. Please try again.";
  }

  return {
    type,
    message,
    originalError,
    code,
    retryable,
    userFriendlyMessage,
  };
};

/**
 * Analyzes an error and determines its type
 */
export const analyzeError = (error: any): AppError => {
  // Network errors
  if (error?.name === "AbortError" || error?.message?.includes("timeout")) {
    return createAppError(ErrorType.TIMEOUT, "Request timed out", error);
  }

  if (
    error?.message?.includes("Network request failed") ||
    error?.message?.includes("fetch") ||
    error?.code === "NETWORK_ERROR"
  ) {
    return createAppError(ErrorType.NETWORK, "Network request failed", error);
  }

  // Authentication errors
  if (error?.status === 401 || error?.code === "UNAUTHORIZED") {
    return createAppError(
      ErrorType.AUTHENTICATION,
      "Authentication failed",
      error,
      401
    );
  }

  // Server errors
  if (error?.status >= 500 || error?.code === "SERVER_ERROR") {
    return createAppError(
      ErrorType.SERVER,
      "Server error",
      error,
      error?.status
    );
  }

  // Validation errors
  if (error?.status === 400 || error?.status === 422) {
    return createAppError(
      ErrorType.VALIDATION,
      error?.message || "Validation failed",
      error,
      error?.status
    );
  }

  // Unknown errors
  return createAppError(
    ErrorType.UNKNOWN,
    error?.message || "Unknown error occurred",
    error
  );
};

/**
 * Error handling result interface
 */
export interface ErrorHandlingResult<T> {
  success: boolean;
  data?: T;
  error?: AppError;
}

/**
 * Wraps async operations with error handling
 */
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  fallbackValue?: T
): Promise<ErrorHandlingResult<T>> => {
  try {
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    const appError = analyzeError(error);
    console.error("Operation failed:", appError);

    return {
      success: false,
      error: appError,
      data: fallbackValue,
    };
  }
};

/**
 * Retry mechanism for retryable errors
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const appError = analyzeError(error);

      // Don't retry non-retryable errors
      if (!appError.retryable || attempt === maxRetries) {
        throw error;
      }

      console.log(`Attempt ${attempt} failed, retrying in ${delay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }

  throw lastError;
};

/**
 * Logs errors in a consistent format
 */
export const logError = (error: AppError, context?: string) => {
  const logMessage = `[${error.type}] ${context ? `${context}: ` : ""}${
    error.message
  }`;

  if (error.type === ErrorType.NETWORK || error.type === ErrorType.TIMEOUT) {
    console.warn(logMessage, error.originalError);
  } else {
    console.error(logMessage, error.originalError);
  }
};
