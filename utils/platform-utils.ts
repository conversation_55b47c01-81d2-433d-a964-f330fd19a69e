import { Dimensions, Platform } from "react-native";

/**
 * Platform utility functions for handling device-specific behavior
 */

/**
 * Check if the current platform is Android
 */
export const isAndroid = Platform.OS === "android";

/**
 * Check if the current platform is iOS
 */
export const isIOS = Platform.OS === "ios";

/**
 * Get screen dimensions
 */
const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = Dimensions.get("window");

/**
 * Check if device has a small screen (height < 700px)
 */
export const isSmallDevice = SCREEN_HEIGHT < 700;

/**
 * Check if device has a very small screen (height < 600px)
 */
export const isVerySmallDevice = SCREEN_HEIGHT < 600;

/**
 * Get keyboard avoiding behavior based on platform
 */
export const getKeyboardAvoidingBehavior = () => {
  return isIOS ? "padding" : "height";
};

/**
 * Get keyboard vertical offset based on platform and device size
 */
export const getKeyboardVerticalOffset = () => {
  if (isIOS) {
    return isSmallDevice ? 60 : 80;
  }
  return isSmallDevice ? 20 : 40;
};

/**
 * Get responsive padding based on device size
 */
export const getResponsivePadding = () => {
  if (isVerySmallDevice) return 15;
  if (isSmallDevice) return 20;
  return 30;
};

/**
 * Get responsive vertical spacing based on device size
 */
export const getResponsiveVerticalSpacing = () => {
  if (isVerySmallDevice) return 10;
  if (isSmallDevice) return 15;
  return 20;
};

/**
 * Screen dimension utilities
 */
export const screenDimensions = {
  height: SCREEN_HEIGHT,
  width: SCREEN_WIDTH,
  isSmall: isSmallDevice,
  isVerySmall: isVerySmallDevice,
};
