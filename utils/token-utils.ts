import { AUTH_TOKEN_KEY, USER_ID_KEY } from "@/constants/api";
import { getStorageItemAsync, setStorageItemAsync } from "@/utils/storage";

/**
 * Parse JWT token and extract payload
 */
export const parseJWT = (token: string): any | null => {
  try {
    // Remove Bearer prefix if it exists
    const cleanToken = token.replace(/^Bearer\s+/, "");

    // Split the token into parts
    const parts = cleanToken.split(".");
    if (parts.length !== 3) {
      return null;
    }

    // Decode the payload (second part)
    const payload = parts[1];

    // Add padding if needed for base64 decoding
    const paddedPayload = payload.padEnd(
      payload.length + ((4 - (payload.length % 4)) % 4),
      "="
    );

    // Decode and parse
    const decoded = atob(paddedPayload);
    return JSON.parse(decoded);
  } catch (error) {
    console.error("Error parsing JWT:", error);
    return null;
  }
};

/**
 * Get token expiration timestamp from JWT
 */
export const getTokenExpiration = (token: string): number | null => {
  try {
    const payload = parseJWT(token);
    if (!payload || !payload.exp) {
      return null;
    }

    // JWT exp is in seconds, convert to milliseconds
    return payload.exp * 1000;
  } catch (error) {
    console.error("Error getting token expiration:", error);
    return null;
  }
};

/**
 * Check if token is expired with optional grace period
 * @param token JWT token to check
 * @param gracePeriodMs Grace period in milliseconds (default: 5 minutes)
 */
export const isTokenExpired = (
  token: string | null | undefined,
  gracePeriodMs: number = 300000
): boolean => {
  if (!token) {
    return true;
  }

  try {
    const expirationTime = getTokenExpiration(token);
    if (!expirationTime) {
      return true;
    }

    const currentTime = Date.now();

    // Token is considered expired if it expires within the grace period
    return currentTime > expirationTime - gracePeriodMs;
  } catch (error) {
    console.error("Error checking token expiration:", error);
    return true;
  }
};

/**
 * Check if token will expire soon (within grace period)
 */
export const isTokenExpiringSoon = (
  token: string | null | undefined,
  gracePeriodMs: number = 300000
): boolean => {
  if (!token) {
    return false;
  }

  try {
    const expirationTime = getTokenExpiration(token);
    if (!expirationTime) {
      return false;
    }

    const currentTime = Date.now();

    // Check if token expires within grace period but hasn't expired yet
    return (
      currentTime > expirationTime - gracePeriodMs &&
      currentTime < expirationTime
    );
  } catch (error) {
    console.error("Error checking if token expires soon:", error);
    return false;
  }
};

/**
 * Get time until token expires in milliseconds
 */
export const getTimeUntilExpiration = (
  token: string | null | undefined
): number => {
  if (!token) {
    return 0;
  }

  try {
    const expirationTime = getTokenExpiration(token);
    if (!expirationTime) {
      return 0;
    }

    const currentTime = Date.now();
    const timeLeft = expirationTime - currentTime;

    return Math.max(0, timeLeft);
  } catch (error) {
    console.error("Error getting time until expiration:", error);
    return 0;
  }
};

/**
 * Extract user ID from JWT token
 */
export const getUserIdFromToken = (token: string): string | null => {
  try {
    const payload = parseJWT(token);

    // Check common JWT claims for user ID
    return (
      payload?.sub || payload?.user_id || payload?.userId || payload?.id || null
    );
  } catch (error) {
    console.error("Error extracting user ID from token:", error);
    return null;
  }
};

/**
 * Validate token structure and basic claims
 */
export const isValidTokenStructure = (
  token: string | null | undefined
): boolean => {
  if (!token) {
    return false;
  }

  try {
    const payload = parseJWT(token);
    if (!payload) {
      return false;
    }

    // Check for required claims
    const hasExpiration = typeof payload.exp === "number";
    const hasValidExpiration = hasExpiration && payload.exp > 0;

    return hasValidExpiration;
  } catch (error) {
    console.error("Error validating token structure:", error);
    return false;
  }
};

/**
 * Get current stored token
 */
export const getCurrentToken = async (): Promise<string | null> => {
  try {
    return await getStorageItemAsync(AUTH_TOKEN_KEY);
  } catch (error) {
    console.error("Error getting current token:", error);
    return null;
  }
};

/**
 * Get current stored user ID
 */
export const getCurrentUserId = async (): Promise<string | null> => {
  try {
    return await getStorageItemAsync(USER_ID_KEY);
  } catch (error) {
    console.error("Error getting current user ID:", error);
    return null;
  }
};

/**
 * Store token securely
 */
export const storeToken = async (token: string | null): Promise<void> => {
  try {
    await setStorageItemAsync(AUTH_TOKEN_KEY, token);
  } catch (error) {
    console.error("Error storing token:", error);
    throw error;
  }
};

/**
 * Store user ID securely
 */
export const storeUserId = async (userId: string | null): Promise<void> => {
  try {
    await setStorageItemAsync(USER_ID_KEY, userId);
  } catch (error) {
    console.error("Error storing user ID:", error);
    throw error;
  }
};

/**
 * Clear all auth data
 */
export const clearAuthData = async (): Promise<void> => {
  try {
    await Promise.all([
      setStorageItemAsync(AUTH_TOKEN_KEY, null),
      setStorageItemAsync(USER_ID_KEY, null),
    ]);
  } catch (error) {
    console.error("Error clearing auth data:", error);
    throw error;
  }
};

/**
 * Check if user is authenticated (has valid token)
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const token = await getCurrentToken();
    const userId = await getCurrentUserId();

    return Boolean(
      token && userId && isValidTokenStructure(token) && !isTokenExpired(token)
    );
  } catch (error) {
    console.error("Error checking authentication:", error);
    return false;
  }
};

/**
 * Get auth status with detailed information
 */
export const getAuthStatus = async () => {
  try {
    const token = await getCurrentToken();
    const userId = await getCurrentUserId();

    if (!token || !userId) {
      return {
        isAuthenticated: false,
        needsRefresh: false,
        isExpired: true,
        timeUntilExpiration: 0,
      };
    }

    const isValidStructure = isValidTokenStructure(token);
    const isExpired = isTokenExpired(token);
    const needsRefresh = isTokenExpiringSoon(token);
    const timeLeft = getTimeUntilExpiration(token);

    return {
      isAuthenticated: isValidStructure && !isExpired,
      needsRefresh,
      isExpired,
      timeUntilExpiration: timeLeft,
      token,
      userId,
    };
  } catch (error) {
    console.error("Error getting auth status:", error);
    return {
      isAuthenticated: false,
      needsRefresh: false,
      isExpired: true,
      timeUntilExpiration: 0,
    };
  }
};
