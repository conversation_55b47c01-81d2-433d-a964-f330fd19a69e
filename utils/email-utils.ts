/**
 * Email utility functions for normalization and validation
 */

/**
 * Normalize email address by converting to lowercase and trimming whitespace
 * @param email - Raw email input from user
 * @returns Normalized email address
 */
export const normalizeEmail = (email: string): string => {
  if (!email || typeof email !== "string") {
    return "";
  }

  return email.toLowerCase().trim();
};

/**
 * Basic email validation using regex pattern
 * @param email - Email address to validate
 * @returns true if email format is valid
 */
export const isValidEmailFormat = (email: string): boolean => {
  if (!email || typeof email !== "string") {
    return false;
  }

  // Basic email regex pattern
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate and normalize email address
 * @param email - Raw email input
 * @returns Object with normalized email and validation status
 */
export const validateAndNormalizeEmail = (
  email: string
): {
  normalizedEmail: string;
  isValid: boolean;
  error?: string;
} => {
  try {
    const normalized = normalizeEmail(email);

    if (!normalized) {
      return {
        normalizedEmail: "",
        isValid: false,
        error: "Email is required",
      };
    }

    if (!isValidEmailFormat(normalized)) {
      return {
        normalizedEmail: normalized,
        isValid: false,
        error: "Invalid email format",
      };
    }

    return {
      normalizedEmail: normalized,
      isValid: true,
    };
  } catch (error) {
    return {
      normalizedEmail: "",
      isValid: false,
      error: "Error processing email",
    };
  }
};

/**
 * Get email domain from normalized email
 * @param email - Email address
 * @returns Domain part of the email
 */
export const getEmailDomain = (email: string): string => {
  const normalized = normalizeEmail(email);
  const atIndex = normalized.indexOf("@");

  if (atIndex === -1) {
    return "";
  }

  return normalized.substring(atIndex + 1);
};

/**
 * Check if two emails are the same after normalization
 * @param email1 - First email
 * @param email2 - Second email
 * @returns true if emails match after normalization
 */
export const emailsMatch = (email1: string, email2: string): boolean => {
  return normalizeEmail(email1) === normalizeEmail(email2);
};

/**
 * Test cases for email normalization (development only)
 */
export const testEmailNormalization = () => {
  if (!__DEV__) {
    return;
  }

  const testCases = [
    {
      input: "<EMAIL>",
      expected: "<EMAIL>",
      description: "Uppercase email",
    },
    {
      input: "  <EMAIL>  ",
      expected: "<EMAIL>",
      description: "Mixed case with whitespace",
    },
    {
      input: "<EMAIL>",
      expected: "<EMAIL>",
      description: "Uppercase domain",
    },
    {
      input: "<EMAIL>",
      expected: "<EMAIL>",
      description: "Already lowercase",
    },
  ];

  testCases.forEach(({ input, expected }) => {
    const result = normalizeEmail(input);
    const passed = result === expected;

    if (!passed) {
      console.error(`   FAILED: Expected "${expected}", got "${result}"`);
    }
  });
};
