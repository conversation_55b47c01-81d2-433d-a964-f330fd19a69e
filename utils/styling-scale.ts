import { useMemo } from "react";
import { Dimensions, PixelRatio, ScaledSize } from "react-native";

// Default guideline sizes are based on standard iPhone 11 Pro dimensions
const GUIDELINE_BASE_WIDTH = 400;
const GUIDELINE_BASE_HEIGHT = 800;

// Get initial dimensions
const window = Dimensions.get("window");

// Initialize dimensions
let { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = window;
let [shortDimension, longDimension] =
  SCREEN_WIDTH < SCREEN_HEIGHT
    ? [SCREEN_WIDTH, SCREEN_HEIGHT]
    : [SCREEN_HEIGHT, SCREEN_WIDTH];

// Update dimensions on change
Dimensions.addEventListener("change", ({ window }: { window: ScaledSize }) => {
  SCREEN_WIDTH = window.width;
  SCREEN_HEIGHT = window.height;
  [shortDimension, longDimension] =
    SCREEN_WIDTH < SCREEN_HEIGHT
      ? [SCREEN_WIDTH, SCREEN_HEIGHT]
      : [SCREEN_HEIGHT, SCREEN_WIDTH];
});

/**
 * Scale a value horizontally based on screen width
 * @param size - The size to scale
 * @param defaultValue - Optional fallback value if scaling fails
 * @returns The scaled value or defaultValue if provided
 */
export const scale = (size: number, defaultValue?: number): number => {
  if (typeof size !== "number" || isNaN(size)) {
    return defaultValue ?? 0;
  }

  try {
    const scaledValue = Math.round(
      PixelRatio.roundToNearestPixel(
        (shortDimension / GUIDELINE_BASE_WIDTH) * size
      )
    );

    return isNaN(scaledValue) ? defaultValue ?? size : scaledValue;
  } catch {
    return defaultValue ?? size;
  }
};

/**
 * Scale a value vertically based on screen height
 * @param size - The size to scale
 * @param defaultValue - Optional fallback value if scaling fails
 * @returns The scaled value or defaultValue if provided
 */
export const verticalScale = (size: number, defaultValue?: number): number => {
  if (typeof size !== "number" || isNaN(size)) {
    return defaultValue ?? 0;
  }

  try {
    const scaledValue = Math.round(
      PixelRatio.roundToNearestPixel(
        (longDimension / GUIDELINE_BASE_HEIGHT) * size
      )
    );

    return isNaN(scaledValue) ? defaultValue ?? size : scaledValue;
  } catch {
    return defaultValue ?? size;
  }
};

/**
 * Scale a value moderately - less than scale() for larger screens
 * @param size - The size to scale
 * @param factor - The factor to moderate by (default 0.5)
 * @param defaultValue - Optional fallback value if scaling fails
 * @returns The moderately scaled value
 */
export const moderateScale = (
  size: number,
  factor: number = 0.5,
  defaultValue?: number
): number => {
  if (typeof size !== "number" || isNaN(size)) {
    return defaultValue ?? 0;
  }

  try {
    const scaledValue = size + (scale(size) - size) * factor;
    return isNaN(scaledValue) ? defaultValue ?? size : Math.round(scaledValue);
  } catch {
    return defaultValue ?? size;
  }
};

/**
 * Hook for memoized scaling values that update with screen dimensions
 * @param size - The size to scale
 * @param scaleFunction - The scaling function to use
 * @param defaultValue - Optional fallback value
 * @returns Memoized scaled value
 */
export const useScaledValue = (
  size: number,
  scaleFunction:
    | typeof scale
    | typeof verticalScale
    | typeof moderateScale = scale,
  defaultValue?: number
): number => {
  return useMemo(
    () => scaleFunction(size, defaultValue),
    [size, scaleFunction, defaultValue, SCREEN_WIDTH, SCREEN_HEIGHT]
  );
};

/**
 * Create scaled style values for multiple properties
 * @param styles - Object with style values to scale
 * @param scaleFunction - The scaling function to use
 * @returns Object with scaled values
 */
export const createScaledStyle = <T extends { [K in keyof T]: number }>(
  styles: T,
  scaleFunction:
    | typeof scale
    | typeof verticalScale
    | typeof moderateScale = scale
): T => {
  const scaledStyles = {} as T;

  for (const key in styles) {
    if (Object.prototype.hasOwnProperty.call(styles, key)) {
      scaledStyles[key] = scaleFunction(styles[key]) as T[typeof key];
    }
  }

  return scaledStyles;
};

// Export constants for reference
export const DIMENSIONS = {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  GUIDELINE_BASE_WIDTH,
  GUIDELINE_BASE_HEIGHT,
} as const;
