{"name": "kyc-marketplace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.1.6", "@sumsub/websdk-react": "^2.3.4", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-devtools": "^5.59.16", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^0.2.1", "cookies-next": "^5.0.2", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "https": "^1.0.0", "immer": "^10.1.1", "input-otp": "^1.4.1", "jose": "^6.0.11", "jwt-decode": "^4.0.0", "lucide-react": "^0.330.0", "moment-timezone": "^0.5.46", "next": "15.1.5", "next-client-cookies": "^2.0.1", "next-intl": "^3.23.2", "node-fetch": "^2.7.0", "nuqs": "^2.4.0", "react": "19.0.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "react-html-parser": "^2.0.2", "react-icons": "^5.5.0", "react-svg": "^16.1.34", "react-toastify": "^11.0.5", "react-use": "^17.5.1", "sharp": "^0.33.5", "sonner": "^1.5.0", "tailwind-merge": "^2.2.1", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "to-words": "^4.1.0", "use-query-params": "^2.2.1", "usehooks-ts": "^3.1.0", "zod": "^3.23.8", "zustand": "^5.0.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.59.7", "@types/node": "^20", "@types/react": "npm:types-react@^19.0.0-rc.1", "@types/react-date-range": "^1.4.9", "@types/react-dom": "npm:types-react-dom@^19.0.0-rc.1", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "prettier": "^3.3.3", "react-phone-number-input": "^3.4.9", "tailwindcss": "^3.4.14", "typescript": "^5"}}