{"name": "trust-nexus", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "npx expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "setup-sumsub": "node ./scripts/setup-sumsub.js", "generate-icons": "node ./scripts/generate-icons.js", "check-icons": "node ./scripts/check-icons.js", "prebuild": "npx expo prebuild && npm run setup-sumsub", "prebuild:clean": "npx expo prebuild --clean && npm run setup-sumsub", "andorid:preview": "eas build --platform android --profile preview", "ios:setup": "npm run setup-sumsub && cd ios && pod install", "android:clean": "cd android && ./gradlew clean", "sumsub:verify": "node ./scripts/setup-sumsub.js --verify-only"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^4.1.3", "@microsoft/signalr": "^8.0.7", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "^1.7.6", "@sumsub/react-native-mobilesdk-module": "^1.35.1", "@tanstack/react-query": "^5.69.0", "expo": "53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linking": "~7.1.5", "expo-navigation-bar": "~4.2.4", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-native": "0.79.2", "react-native-blob-util": "^0.22.2", "react-native-gesture-handler": "~2.24.0", "react-native-image-viewing": "^0.2.2", "react-native-modal": "^14.0.0-rc.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.12.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zod": "^3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.5", "react-test-renderer": "18.3.1", "sharp": "^0.34.2", "typescript": "~5.8.3"}, "private": true}