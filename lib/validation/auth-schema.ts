import { RegistrationTypeOption } from "@/types/auth";
import { z } from "zod";

export const loginSchema = z.object({
  email: z
    .string()
    .trim() // Trim whitespace before validation
    .min(1, { message: "Email is required" })
    .email({ message: "Invalid email address" }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters" }),
});

export const registrationSchema = z
  .object({
    registrationType: z.nativeEnum(RegistrationTypeOption),
    firstName: z
      .string()
      .min(1, { message: "First name is required" })
      .max(50, { message: "First name is too long" }),
    lastName: z
      .string()
      .min(1, { message: "Last name is required" })
      .max(50, { message: "Last name is too long" }),
    email: z
      .string()
      .trim() // Trim whitespace before validation
      .min(1, { message: "Email is required" })
      .email({ message: "Invalid email address" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
    confirmPassword: z
      .string()
      .min(1, { message: "Please confirm your password" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export type LoginFormValues = z.infer<typeof loginSchema>;
export type RegistrationFormValues = z.infer<typeof registrationSchema>;
