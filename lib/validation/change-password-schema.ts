import { z } from "zod";

// Password validation regex - matches web app requirements
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$%^&+=!~*()_.]).{8,15}$/;
const PASSWORD_REGEX_MESSAGE = 
  "Password must be 8 to 15 characters long, contain four character types: lowercase letters, uppercase letters, numbers and symbols (#, @, !, etc.)";

export const changePasswordSchema = z
  .object({
    password_current: z.string({
      required_error: "Current password is required",
    }).min(1, "Current password is required"),
    password_new: z
      .string({
        required_error: "New password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
    password_confirm: z
      .string({
        required_error: "Confirm password is required",
      })
      .regex(PASSWORD_REGEX, {
        message: PASSWORD_REGEX_MESSAGE,
      }),
  })
  .superRefine(({ password_new, password_confirm, password_current }, ctx) => {
    if (password_new !== password_confirm) {
      ctx.addIssue({
        code: "custom",
        message: "Passwords do not match",
        path: ["password_confirm"],
      });
    }
    if (password_current === password_new) {
      ctx.addIssue({
        code: "custom",
        message: "New password must be different from the current password",
        path: ["password_new"],
      });
    }
  });

export type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;
